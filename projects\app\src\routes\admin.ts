import { RouteConfigType } from '@/types/routes';

export const adminRoutes: RouteConfigType[] = [
  {
    name: '基础信息管理',
    code: 'basicInfoManagement',
    icon: 'navProfileLine',
    children: [
      {
        name: '学校信息',
        path: '/tenant/info',
        code: 'school_info'
      },
      {
        name: '成员管理',
        path: '/tenant/member',
        code: 'member_management'
      },
      {
        name: '教师管理',
        path: '/tenant/teamManagement/teacher',
        code: 'teacher_management'
      },
      {
        name: '学生管理',
        path: '/tenant/teamManagement/student',
        code: 'student_management'
      },
      {
        name: '教学管理',
        path: '/tenant/teamManagement/teach',
        code: 'teach_management'
      },
      {
        name: '学期管理',
        path: '/tenant/teamManagement/semester',
        code: 'semester_setting'
      },
      {
        name: '角色管理',
        path: '/tenant/role',
        code: 'role_management'
      },
      {
        name: '行政与教学管理',
        path: '/tenant/teamManagement/administration',
        code: 'administration_management'
      }
    ]
  },
  {
    name: '应用中心管理',
    code: 'agentCenterManagement',
    icon: 'navFolderOpenLine',
    children: [
      {
        name: '应用管理',
        path: '/tenant/app',
        code: 'app_management',
        activePrefixes: ['/tenant/app/detail']
      },
      {
        name: '场景管理',
        path: '/tenant/scene',
        code: 'scene_management'
      },
      {
        name: '知识库管理',
        path: '/tenant/dataset',
        code: 'dataset_management',
        activePrefixes: ['/tenant/dataset/detail'],
        children: [
          {
            name: '知识库详情',
            path: '/tenant/dataset/detail',
            code: 'dataset_detail',
            unauth: true,
            hidden: true
          }
        ]
      },
      {
        name: '快捷指令管理',
        path: '/tenant/tenantPrompt',
        code: 'Prompt_management'
      },
      {
        name: '工作流管理',
        path: '/tenant/workflow',
        code: 'workflow_management'
      },
      {
        name: '应用详情',
        path: '/tenant/app/detail',
        code: 'tenant_app_detail',
        hidden: true,
        unauth: true
      },
      {
        name: '提问表单管理',
        path: '/tenant/formManager',
        code: 'formManager_management'
      }
    ]
  },
  {
    name: '校本资源管理',
    code: 'SchoolBasedResourcesManage',
    icon: 'navFolderOpenLine',
    children: [
      {
        name: '资源管理',
        path: '/tenant/resources',
        code: 'resources_manage'
      },
      {
        name: '资源上传',
        path: '/tenant/resourcesUpload',
        code: 'resources_upload',
        hidden: true
      }
    ]
  }
  // {
  //   name: '评价管理',
  //   code: 'evaluate_manage',
  //   icon: 'navFolderOpenLine',
  //   children: [
  //     {
  //       name: '评价规则',
  //       path: '/tenant/evaluate/rule',
  //       code: 'evaluate_rule',
  //       activePrefixes: ['/tenant/evaluate/rule'],
  //       children: [
  //         {
  //           name: '评价指标',
  //           path: '/tenant/evaluate/rule/components/IndicatorsTab',
  //           code: 'evaluate_rule',
  //           activePrefixes: ['/tenant/evaluate/rule'],
  //           hidden: true,
  //           unauth: false
  //         }
  //       ]
  //     },

  //     {
  //       name: '过程性评价',
  //       code: 'process_evaluate',
  //       children: [
  //         {
  //           name: '课堂表现评价',
  //           path: '/tenant/evaluate/process/class',
  //           code: 'classes_show_evaluate',
  //           activePrefixes: ['/tenant/evaluate/process/class'],
  //           children: [
  //             {
  //               name: '班级详情',
  //               path: '/tenant/evaluate/process/class/detail',
  //               hidden: true
  //             },
  //             {
  //               name: '评价记录',
  //               path: '/tenant/evaluate/process/class/record',
  //               hidden: true
  //             },
  //             {
  //               name: '评价指标',
  //               path: '/tenant/evaluate/process/class/indactor',
  //               hidden: true
  //             }
  //           ]
  //         },
  //         {
  //           name: '作业评价',
  //           path: '/tenant/evaluate/process/homework',
  //           activePrefixes: ['/tenant/evaluate/process/homework'],
  //           code: 'homework_evaluate',
  //           children: [
  //             {
  //               name: '作业详情',
  //               path: '/tenant/evaluate/process/homework/detail',
  //               hidden: true
  //             },
  //             {
  //               name: '作业记录',
  //               path: '/tenant/evaluate/process/homework/record',
  //               hidden: true
  //             }
  //           ]
  //         }
  //       ]
  //     },
  // {
  //   name: '评价数据',
  //   path: '/tenant/evaluate/data',
  //   activePrefixes: ['/tenant/evaluate/data'],

  //   code: 'evaluate_data'
  // }
  //   ]
  // }
];
