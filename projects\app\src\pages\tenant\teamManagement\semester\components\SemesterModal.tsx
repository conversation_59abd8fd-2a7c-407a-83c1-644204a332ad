import { Box, FormControl, FormLabel, Flex, Button, ModalBody } from '@chakra-ui/react';
import { useForm, Controller } from 'react-hook-form';
import React, { useState, useEffect } from 'react';
import { useRequest } from '@/hooks/useRequest';
import MyModal from '@/components/MyModal';
import { DatePicker, Select, ConfigProvider } from 'antd';
import styles from '../../index.module.scss';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import 'dayjs/locale/zh-cn';
import zhCN from 'antd/locale/zh_CN';
import {
  getSystemDictTree,
  createClientSemester,
  updateClientSemester,
  getDetailClientSemester
} from '@/api/tenant/teamManagement/semester';
import { DictTree } from '@/types/api/tenant/teamManagement/semester';
import { useToast } from '@/hooks/useToast';

dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.locale('zh-cn');

const { RangePicker } = DatePicker;

interface FormData {
  year: string;
  type: number;
  dateRange: [string, string] | null;
}

const SemesterModal = ({
  modalId,
  mode = 'add',
  onClose,
  onSuccess
}: {
  modalId: string;
  mode?: 'add' | 'edit' | 'view';
  onClose: (submited: boolean) => void;
  onSuccess: () => void;
}) => {
  // 根据mode和modalId设置标题
  const getTitleByMode = () => {
    if (mode === 'view') return '查看学期';
    return modalId ? '编辑学期' : '添加学期';
  };

  const title = getTitleByMode();
  const isViewMode = mode === 'view';

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: any) => {
      const baseParams = {
        endDate: data.dateRange ? data.dateRange[1] : null,
        startDate: data.dateRange ? data.dateRange[0] : null
      };

      if (modalId) {
        return updateClientSemester({
          ...baseParams,
          id: modalId
        });
      } else {
        return createClientSemester({
          ...baseParams,
          year: data.year,
          type: data.type
        });
      }
    },
    onSuccess() {
      onSuccess();
      onClose(true);
    },
    successToast: modalId ? '编辑成功' : '新增成功'
  });

  type Option = { label: string; value: string | number };

  const [yearOptions, setYearOptions] = useState<Option[]>([]);
  const [semesterOptions, setSemesterOptions] = useState<Option[]>([]);

  useEffect(() => {
    const transformOptions = (
      data: DictTree,
      valueKey: 'dictKey' | 'dictValue' = 'dictKey'
    ): Option[] => {
      if (!data || !Array.isArray(data) || data.length === 0 || !data[0]?.children) {
        return [];
      }
      return data[0].children.map((item) => ({
        label: item.dictValue,
        value: item[valueKey]
      }));
    };

    const fetchOptions = async (
      dictType: string,
      valueKey: 'dictKey' | 'dictValue' = 'dictKey',
      setOptions: React.Dispatch<React.SetStateAction<Option[]>>
    ) => {
      try {
        const response = await getSystemDictTree(dictType);
        const transformedOptions = transformOptions(response, valueKey);
        setOptions(transformedOptions);
        return transformedOptions;
      } catch (error) {
        console.error(`获取${dictType}数据失败:`, error);
        return [];
      }
    };

    const fetchAllOptions = async () => {
      await Promise.all([
        fetchOptions('semester_year', 'dictValue', setYearOptions),
        fetchOptions('semester_type', 'dictKey', setSemesterOptions)
      ]);
    };

    fetchAllOptions();

    // 如果是编辑或查看模式，获取详情数据
    if (modalId) {
      getDetailClientSemester(modalId).then((res) => {
        if (res) {
          setValue('year', res.year);
          setValue('type', res.type);
          setValue(
            'dateRange',
            res.startDate && res.endDate
              ? [dayjs(res.startDate).format('YYYY-MM-DD'), dayjs(res.endDate).format('YYYY-MM-DD')]
              : null
          );
        }
      });
    }
  }, [modalId, setValue]);

  return (
    <MyModal isOpen={true} title={title} isCentered>
      <ModalBody>
        <Box p="24px">
          <FormControl isInvalid={!!errors.year}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: isViewMode ? 'transparent' : '#F53F3F'
                  }}
                >
                  学年
                </Box>
              </FormLabel>
              <Flex flexDir="column">
                <Controller
                  name="year"
                  control={control}
                  rules={{ required: isViewMode ? false : '请选择学年' }}
                  render={({ field }) => (
                    <Select
                      className={styles['formItem']}
                      {...field}
                      style={{ width: '400px', height: '40px' }}
                      placeholder="请选择学年"
                      options={yearOptions}
                      dropdownStyle={{ zIndex: 9999 }}
                      disabled={!!modalId || isViewMode}
                    />
                  )}
                />
                {!isViewMode && errors.year && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.year.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl isInvalid={!!errors.type} mt="20px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: isViewMode ? 'transparent' : '#F53F3F'
                  }}
                >
                  学期类型
                </Box>
              </FormLabel>
              <Flex flexDir="column">
                <Controller
                  name="type"
                  control={control}
                  rules={{ required: isViewMode ? false : '请选择学期类型' }}
                  render={({ field }) => (
                    <Select
                      className={styles['formItem']}
                      {...field}
                      style={{ width: '400px', height: '40px' }}
                      placeholder="请选择学期类型"
                      options={semesterOptions}
                      dropdownStyle={{ zIndex: 9999 }}
                      disabled={!!modalId || isViewMode}
                    />
                  )}
                />
                {!isViewMode && errors.type && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.type.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="20px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: isViewMode ? 'transparent' : '#F53F3F'
                  }}
                >
                  学期日期
                </Box>
              </FormLabel>
              <Flex flexDir="column">
                <Controller
                  name="dateRange"
                  control={control}
                  rules={{ required: isViewMode ? false : '请选择学期日期' }}
                  render={({ field }) => (
                    <ConfigProvider locale={zhCN}>
                      <RangePicker
                        style={{
                          width: '400px',
                          height: '38px',
                          backgroundColor: '#F6F6F6',
                          border: 'none'
                        }}
                        popupClassName={`rangePicker ${styles.rangePicker} hidden-ascader-level1-check`}
                        placeholder={['开始日期', '结束日期']}
                        onChange={(dates, dateStrings) => {
                          if (!isViewMode) {
                            if (dates === null) {
                              field.onChange(null);
                            } else {
                              field.onChange(dateStrings);
                            }
                          }
                        }}
                        onBlur={field.onBlur}
                        value={field.value ? [dayjs(field.value[0]), dayjs(field.value[1])] : null}
                        disabled={isViewMode}
                      />
                    </ConfigProvider>
                  )}
                />
                {!isViewMode && errors.dateRange && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.dateRange.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="32px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex>
                {!isViewMode && (
                  <Button
                    borderColor="#0052D9"
                    h="36px"
                    variant={'grayBase'}
                    borderRadius="8px"
                    onClick={() => onClose(false)}
                  >
                    取消
                  </Button>
                )}
                <Button
                  h="36px"
                  ml={!isViewMode ? '24px' : '0'}
                  borderRadius="8px"
                  onClick={isViewMode ? () => onClose(false) : handleSubmit(onSubmit as any)}
                  isLoading={!isViewMode && isSubmiting}
                >
                  {isViewMode ? '确认' : '确定'}
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SemesterModal;
