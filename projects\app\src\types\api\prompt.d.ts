import { PromptExternalTypeEnum } from '@/constants/api/prompt';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';

// 提示语表
export type PromptType = {
  id: string; // 提示语ID
  tenantId: string;
  tmbId: string;
  appId: string; // 应用ID
  promptTitle: string; // 标题
  description: string; // 说明
  inputContent: string; // 引导语
  proContent?: string; // 引导语专业版
  hiddenContent: string; // 隐藏提示词
  externalType: PromptExternalTypeEnum; // 外链类型
  sort: number;
  source: DataSource;
  permission: number;
  promptId: string;
  createTime: string; // 创建时间
  type: number;
};

// 提示语标签表
export type PromptLabelType = {
  _id: string; // ID
  tenantId: string;
  tmbId: string;
  labelName: string; // 名称
  isMine: number; // 是否是我的
  createTime: string; // 创建时间
};

export type PromptDetailParams = {
  id: string;
};
export type PromptDeleteValidParams = {
  id: string;
};

export type PromptListParams = {
  tenantAppId: string;
};

export type PromptLabelCreateParams = {
  appId: string; // labelId
  labelName: string; // 名称
  isMine?: number;
};

export type PromptLabelListParams = {
  appId: string;
};

export interface PromptReorderParams {
  param: {
    id: string;
    sort: number;
  }[];
}

export type PromptCenterListParams = {
  source?: DataSource;
  permission?: number;
  tenantAppId: string;
  searchKey?: string;
};

export type PromptCenterListType = {
  id: string; // 提示语ID
  tenantId: string;
  tmbId: string;
  appId: string; // 应用ID
  promptTitle: string; // 标题
  description: string; // 说明
  inputContent: string; // 引导语
  proContent?: string; // 引导语专业版
  hiddenContent: string; // 隐藏提示词
  externalType: PromptExternalTypeEnum; // 外链类型
  sort: number;
  source: DataSource;
  permission: number;
  promptId: string;
  createTime: string; // 创建时间
  appName: string;
  createUsername: string;
  type: number;
};

export type PromptCenterCreateParams = {
  promptTitle: string; // 标题
  description?: string; // 说明
  tenantAppId: string;
  proContent?: string; // 用户输入框内容
  inputContent?: string; // 用户输入框内容
  hiddenContent?: string; // 隐藏提示词
  externalType?: PromptExternalTypeEnum;
  permission?: PermissionTypeEnum;
  type?: number;
};

export type PromptLabelUpdateParams = {
  id: string;
  tenantAppId?: string;
  promptTitle?: string; // 标题
  description?: string; // 说明
  proContent?: string; // 用户输入框内容
  inputContent?: string; // 用户输入框内容
  hiddenContent?: string; // 隐藏提示词
  externalType?: PromptExternalTypeEnum;
  permission?: PermissionTypeEnum;
  type?: number;
};
