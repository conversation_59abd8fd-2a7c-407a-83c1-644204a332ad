import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Modal, Select } from 'antd';
import { PlusOutlined, MinusOutlined, MenuOutlined } from '@ant-design/icons';
import { Box, Flex, Text, Button } from '@chakra-ui/react';
import { Toast } from '@/utils/ui/toast';
import {
  getSubjectDetail,
  sortSubject,
  updateSubject,
  reSortSubject,
  getSubjectBasicList
} from '@/api/tenant/teamManagement/subjectConfiguration';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const { Option } = Select;

interface SubjectOption {
  id: number;
  name: string;
}

// 可排序的科目项组件
interface SortableSubjectItemProps {
  id: string;
  index: number;
  subjectId: number;
  availableOptions: SubjectOption[];
  onSubjectChange: (index: number, value: number) => void;
  onAddSubject: () => void;
  onRemoveSubject: (index: number) => void;
  canRemove: boolean;
  isFromExistingData: boolean; // 是否来自已存在的数据（不能更改）
}

const SortableSubjectItem: React.FC<SortableSubjectItemProps> = ({
  id,
  index,
  subjectId,
  availableOptions,
  onSubjectChange,
  onAddSubject,
  onRemoveSubject,
  canRemove,
  isFromExistingData
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  };

  return (
    <Box ref={setNodeRef} style={style} pb="20px" px="16px">
      <Flex
        align="center"
        gap="12px"
        css={{
          '.ant-select-selector': {
            // background: '#F2F3F5 !important',
            border: isFromExistingData ? 'none !important' : ''
          }
        }}
      >
        {/* 科目选择下拉框 */}
        <Select
          style={{
            flex: 1,
            fontSize: '14px'
          }}
          placeholder="请选择科目"
          value={subjectId || undefined}
          onChange={(value) => onSubjectChange(index, value)}
          size="large"
          disabled={isFromExistingData} // 来自已存在数据的科目不能更改
        >
          {availableOptions.map((subject) => (
            <Option key={subject.id} value={subject.id}>
              {subject.name}
            </Option>
          ))}
        </Select>

        {/* 操作按钮 */}
        <Flex gap="8px">
          {/* 只有来自已存在数据的科目才显示加号按钮 */}
          {isFromExistingData && (
            <Button
              borderRadius="50%"
              onClick={onAddSubject}
              bg="#d1d5db"
              color="#6b7280"
              w="20px"
              h="20px"
              minW="20px"
              p={0}
              _hover={{ bg: '#c1c1c1' }}
              title="添加"
            >
              <PlusOutlined />
            </Button>
          )}
          <Button
            borderRadius="50%"
            onClick={() => onRemoveSubject(index)}
            isDisabled={!canRemove}
            bg={!canRemove ? '#f3f4f6' : '#d1d5db'}
            color={!canRemove ? '#d1d5db' : '#6b7280'}
            w="20px"
            h="20px"
            minW="20px"
            p={0}
            _hover={{ bg: !canRemove ? '#f3f4f6' : '#c1c1c1' }}
            title="删除"
          >
            <MinusOutlined />
          </Button>
          {/* 只有来自已存在数据的科目才显示排序按钮 */}
          {isFromExistingData && (
            <Button
              borderRadius="50%"
              {...attributes}
              {...listeners}
              bg="transparent"
              color="#6b7280"
              w="20px"
              h="20px"
              minW="20px"
              p={0}
              cursor="grab"
              _hover={{ bg: 'transparent' }}
              title="拖拽排序"
            >
              <MenuOutlined />
            </Button>
          )}
        </Flex>
      </Flex>
    </Box>
  );
};

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (subjectIds: number[]) => void;
  title: string;
  semesterId: string;
  gradeId: string;
  loading?: boolean;
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  title,
  semesterId,
  gradeId,
  loading = false
}) => {
  const [subjects, setSubjects] = useState<number[]>([]);
  const [availableSubjects, setAvailableSubjects] = useState<SubjectOption[]>([]);
  const [detailLoading, setDetailLoading] = useState(false);
  const [detailData, setDetailData] = useState<any[]>([]); // 保存detail接口返回的完整数据
  const [existingSubjectsCount, setExistingSubjectsCount] = useState(0); // 记录来自已存在数据的科目数量
  const scrollContainerRef = useRef<HTMLDivElement>(null); // 滚动容器引用

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  // 获取学科详情
  const fetchSubjectDetail = useCallback(async () => {
    if (!semesterId || !gradeId) return;

    try {
      setDetailLoading(true);

      // 同时调用新的subject/list接口和detail接口
      const [subjectListResponse, detailResponse] = await Promise.all([
        getSubjectBasicList({ gradeId: parseInt(gradeId) }),
        getSubjectDetail({ semesterId: parseInt(semesterId), gradeId: parseInt(gradeId) })
      ]);

      // 从新的subject/list接口获取当前年级的所有可选科目
      if (subjectListResponse && subjectListResponse.length > 0) {
        const availableOptions = subjectListResponse.map((item) => ({
          id: parseInt(item.id), // 将字符串id转换为数字
          name: item.name
        }));
        setAvailableSubjects(availableOptions);
      } else {
        setAvailableSubjects([]);
      }

      // 从detail接口获取当前已选中的科目
      if (detailResponse && detailResponse.length > 0) {
        // 保存完整的detail数据
        setDetailData(detailResponse);

        // 按sort字段排序
        const sortedDetailData = [...detailResponse].sort((a, b) => a.sort - b.sort);
        const selectedSubjectIds = sortedDetailData.map((item) => parseInt(item.subjectId));
        setSubjects(selectedSubjectIds);
        setExistingSubjectsCount(selectedSubjectIds.length); // 记录来自已存在数据的科目数量
      } else {
        setDetailData([]);
        setSubjects([0]); // 默认添加一个空的科目选择
        setExistingSubjectsCount(0); // 没有已存在的科目
      }
    } catch (error) {
      console.error('获取学科详情失败:', error);
      Toast.error('获取学科详情失败');
      setSubjects([0]);
      setAvailableSubjects([]);
    } finally {
      setDetailLoading(false);
    }
  }, [semesterId, gradeId]);

  useEffect(() => {
    if (visible) {
      fetchSubjectDetail();
    } else {
      // 关闭弹窗时重置状态
      setSubjects([]);
      setAvailableSubjects([]);
      setExistingSubjectsCount(0);
    }
  }, [visible, semesterId, gradeId, fetchSubjectDetail]);

  // 添加科目
  const handleAddSubject = () => {
    setSubjects([...subjects, 0]); // 0 表示未选择

    // 自动滚动到最底部
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    }, 100); // 延迟一点时间确保DOM更新完成
  };

  // 删除科目
  const handleRemoveSubject = (index: number) => {
    const newSubjects = subjects.filter((_, i) => i !== index);
    setSubjects(newSubjects);
  };

  // 更改科目选择
  const handleSubjectChange = (index: number, value: number) => {
    const newSubjects = [...subjects];
    newSubjects[index] = value;
    setSubjects(newSubjects);
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = subjects.findIndex((_, index) => `subject-${index}` === active.id);
      const newIndex = subjects.findIndex((_, index) => `subject-${index}` === over?.id);

      if (oldIndex !== -1 && newIndex !== -1 && detailData.length > 0) {
        // 先更新本地状态
        const newSubjects = arrayMove(subjects, oldIndex, newIndex);
        const newDetailData = arrayMove(detailData, oldIndex, newIndex);
        setSubjects(newSubjects);
        setDetailData(newDetailData);

        try {
          // 调用排序API - 使用detail接口中的id字段
          const draggedItem = detailData[oldIndex];
          const upperItem = newIndex === 0 ? null : newDetailData[newIndex - 1];

          // 第一步：调用排序接口
          await sortSubject({
            id: parseInt(draggedItem.id), // 使用detail接口返回的id字段
            upperId: upperItem ? parseInt(upperItem.id) : 0 // 目标位置上方元素的id，顶部则为0
          });

          // 第二步：排序成功后调用重新排序接口
          await reSortSubject({
            semesterId: parseInt(semesterId),
            gradeId: parseInt(gradeId)
          });

          Toast.success('排序成功');
        } catch (error) {
          console.error('排序失败:', error);
          Toast.error('排序失败，请重试');
          // 排序失败时恢复原来的顺序
          setSubjects(subjects);
          setDetailData(detailData);
        }
      }
    }
  };

  // 确认保存
  const handleConfirm = async () => {
    // 过滤掉未选择的科目（值为0的）
    const validSubjects = subjects.filter((id) => id > 0);

    // 检查是否有重复选择
    const uniqueSubjects = [...new Set(validSubjects)];
    if (uniqueSubjects.length !== validSubjects.length) {
      Toast.error('不能选择重复的科目');
      return;
    }

    // 检查是否有必要的参数
    if (!semesterId || !gradeId) {
      Toast.error('缺少必要参数');
      return;
    }

    try {
      // 调用更新接口
      await updateSubject({
        semesterId: parseInt(semesterId),
        gradeId: parseInt(gradeId),
        subjectIds: validSubjects
      });

      Toast.success('保存成功');
      onConfirm(validSubjects); // 仍然调用回调，用于关闭弹窗和刷新数据
    } catch (error) {
      console.error('保存失败:', error);
      Toast.error('保存失败，请重试');
    }
  };

  // 获取可选的科目选项（排除已选择的）
  const getAvailableOptions = (currentIndex: number) => {
    if (!availableSubjects || availableSubjects.length === 0) {
      return [];
    }
    const selectedInOthers = subjects.filter((_, index) => index !== currentIndex);
    return availableSubjects.filter((subject) => subject && !selectedInOthers.includes(subject.id));
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
      centered
      styles={{
        header: {
          paddingBottom: '20px',
          marginBottom: '0px',
          fontSize: '18px',
          fontWeight: '600',
          color: '#1f2937'
        },
        body: {
          padding: '24px'
        }
      }}
      closeIcon={
        <span
          style={{
            fontSize: '20px',
            color: '#6b7280',
            fontWeight: 'normal'
          }}
        >
          ×
        </span>
      }
    >
      <Box>
        {detailLoading ? (
          <Box textAlign="center" py="40px">
            <Text>加载中...</Text>
          </Box>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToVerticalAxis]}
          >
            <SortableContext
              items={subjects.map((_, index) => `subject-${index}`)}
              strategy={verticalListSortingStrategy}
            >
              <Box
                ref={scrollContainerRef}
                maxHeight="500px"
                overflowY="auto"
                pr="4px"
                css={{
                  '&::-webkit-scrollbar': {
                    width: '6px'
                  },
                  '&::-webkit-scrollbar-track': {
                    background: '#f1f1f1',
                    borderRadius: '3px'
                  },
                  '&::-webkit-scrollbar-thumb': {
                    background: '#c1c1c1',
                    borderRadius: '3px'
                  },
                  '&::-webkit-scrollbar-thumb:hover': {
                    background: '#a8a8a8'
                  }
                }}
              >
                {subjects.map((subjectId, index) => (
                  <SortableSubjectItem
                    key={`subject-${index}`}
                    id={`subject-${index}`}
                    index={index}
                    subjectId={subjectId}
                    availableOptions={getAvailableOptions(index)}
                    onSubjectChange={handleSubjectChange}
                    onAddSubject={handleAddSubject}
                    onRemoveSubject={handleRemoveSubject}
                    canRemove={subjects.length > 1}
                    isFromExistingData={index < existingSubjectsCount} // 来自已存在数据的科目不能更改
                  />
                ))}
              </Box>
            </SortableContext>
          </DndContext>
        )}

        {/* 如果没有科目，显示添加按钮 */}
        {subjects.length === 0 && (
          <Box
            p="16px"
            bg="#f8f9fa"
            borderRadius="8px"
            border="1px solid #e9ecef"
            textAlign="center"
            mb="24px"
          >
            <Button
              variant="outline"
              onClick={handleAddSubject}
              w="100%"
              h="40px"
              borderColor="#d1d5db"
              color="#6b7280"
              _hover={{ bg: '#f8f9fa' }}
              leftIcon={<PlusOutlined />}
            >
              添加科目
            </Button>
          </Box>
        )}

        {/* 底部按钮 */}
        <Flex justify="flex-end" gap="12px" mt="20px">
          <Button onClick={onCancel} variant="outline" h="40px" px="24px" colorScheme="primary">
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            isLoading={loading}
            h="40px"
            px="24px"
            colorScheme="primary"
          >
            确定
          </Button>
        </Flex>
      </Box>
    </Modal>
  );
};

export default EditModal;
