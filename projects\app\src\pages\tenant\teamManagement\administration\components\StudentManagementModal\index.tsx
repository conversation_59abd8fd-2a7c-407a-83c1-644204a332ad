import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDebounce } from 'use-debounce';
import { Popover, <PERSON>r, Button as AntButton } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import 'antd/dist/reset.css';

// 添加 Cascader 样式确保菜单显示
const cascaderStyles = `
  .ant-cascader-dropdown {
    z-index: 10001 !important;
  }

  .ant-cascader-menu-item {
    color: #000000 !important;
    font-size: 14px !important;
    background-color: #ffffff !important;
  }

  .ant-cascader-menu-item:hover {
    background-color: #f5f5f5 !important;
    color: #000000 !important;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  let styleElement = document.getElementById('cascader-styles');
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'cascader-styles';
    styleElement.textContent = cascaderStyles;
    document.head.appendChild(styleElement);
  }
}

import {
  Box,
  Flex,
  Text,
  Button,
  CloseButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Input
} from '@chakra-ui/react';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import {
  getStudentPageExcludeClazz,
  getStudentPageByClazz,
  batchChangeClazz,
  changeClazz,
  getSchoolDeptTree
} from '@/api/tenant/teamManagement/administration';
import { StudentInfo } from '@/types/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';

// 界面模式
type ViewMode = 'main' | 'addStudents';

// 当前班级学生信息
interface CurrentClassStudent {
  id: string;
  name: string;
  code: string;
}

// 可调入学生信息（扩展）
interface AvailableStudent extends StudentInfo {
  currentClass?: string;
}

interface StudentManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRefresh?: () => void;
  classId?: string;
  className?: string;
  semesterId?: string;
}

const StudentManagementModal: React.FC<StudentManagementModalProps> = ({
  isOpen,
  onClose,
  onRefresh,
  classId,
  className,
  semesterId
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('main');
  const [loading, setLoading] = useState(false);

  // 当前班级学生列表
  const [currentClassStudents, setCurrentClassStudents] = useState<CurrentClassStudent[]>([]);

  // 可调入的学生列表
  const [availableStudents, setAvailableStudents] = useState<AvailableStudent[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);

  // 搜索条件（仅用于调入学生界面）
  const [searchName, setSearchName] = useState('');
  const [searchCode, setSearchCode] = useState('');

  // 防抖搜索条件
  const [debouncedSearchName] = useDebounce(searchName, 500);
  const [debouncedSearchCode] = useDebounce(searchCode, 500);

  // 调出学生相关状态
  const [removePopoverVisible, setRemovePopoverVisible] = useState<string | null>(null);
  const [selectedStageId, setSelectedStageId] = useState<number | undefined>();
  const [selectedGradeId, setSelectedGradeId] = useState<number | undefined>();
  const [selectedNewClazzId, setSelectedNewClazzId] = useState<number | undefined>();
  const [removingStudentId, setRemovingStudentId] = useState<string | null>(null);

  // 班级树相关状态
  const [deptTreeData, setDeptTreeData] = useState<any[]>([]);
  const [deptTreeLoading, setDeptTreeLoading] = useState(false);
  const [cascaderValue, setCascaderValue] = useState<(string | number)[]>([]);

  // 分页
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
    total: 0
  });

  // 保存搜索前的页码
  const [savedPageBeforeSearch, setSavedPageBeforeSearch] = useState<number | null>(null);

  // 跟踪上一次的搜索状态
  const prevSearchStateRef = useRef<boolean>(false);

  // 获取当前班级学生数据
  const fetchCurrentClassStudents = useCallback(async () => {
    if (!classId || !semesterId) return;

    try {
      setLoading(true);
      const response = await getStudentPageByClazz({
        current: pagination.current,
        size: pagination.size,
        clazzId: parseInt(classId)
      });

      if (response && response.records) {
        // 转换为组件需要的格式
        const students = response.records.map((student) => ({
          id: student.id,
          name: student.name,
          code: student.code
        }));
        setCurrentClassStudents(students);
        setPagination((prev) => ({
          ...prev,
          total: response.total,
          current: response.current,
          size: response.size
        }));
      }
    } catch (error) {
      console.error('获取当前班级学生失败:', error);
      Toast.error('获取当前班级学生失败');
    } finally {
      setLoading(false);
    }
  }, [classId, semesterId, pagination.current, pagination.size]);

  // 获取可调入的学生数据
  const fetchAvailableStudents = useCallback(async () => {
    if (!classId || !semesterId) return;

    try {
      setLoading(true);
      const response = await getStudentPageExcludeClazz({
        clazzId: classId,
        semesterId: semesterId,
        name: debouncedSearchName || undefined,
        code: debouncedSearchCode || undefined,
        current: pagination.current.toString(),
        size: pagination.size.toString()
      });

      if (response && response.records) {
        // 为每个学生添加当前班级信息
        const studentsWithClass = response.records.map((student) => ({
          ...student,
          currentClass: 'C-102' // 模拟当前班级
        }));
        setAvailableStudents(studentsWithClass);
        setPagination((prev) => ({
          ...prev,
          total: response.total,
          current: response.current,
          size: response.size
        }));
      }
    } catch (error) {
      console.error('获取可调入学生数据失败:', error);
      Toast.error('获取可调入学生数据失败');
    } finally {
      setLoading(false);
    }
  }, [
    classId,
    semesterId,
    debouncedSearchName,
    debouncedSearchCode,
    pagination.current,
    pagination.size
  ]);

  // 主界面数据获取
  useEffect(() => {
    if (isOpen && viewMode === 'main' && classId && semesterId) {
      fetchCurrentClassStudents();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, classId, semesterId, viewMode, pagination.current, pagination.size]);

  // 监听防抖搜索条件变化，智能处理分页
  useEffect(() => {
    if (isOpen && viewMode === 'addStudents' && classId && semesterId) {
      const hasSearchCondition = debouncedSearchName !== '' || debouncedSearchCode !== '';
      const prevHasSearchCondition = prevSearchStateRef.current;

      // 从无搜索变为有搜索
      if (!prevHasSearchCondition && hasSearchCondition) {
        if (pagination.current !== 1) {
          setSavedPageBeforeSearch(pagination.current);
          setPagination((prev) => ({ ...prev, current: 1 }));
        } else {
          // 已经在第1页，直接调用API
          fetchAvailableStudents();
        }
      }
      // 从有搜索变为无搜索
      else if (prevHasSearchCondition && !hasSearchCondition) {
        if (savedPageBeforeSearch !== null) {
          setPagination((prev) => ({ ...prev, current: savedPageBeforeSearch }));
          setSavedPageBeforeSearch(null);
        } else {
          // 没有保存的页码，直接调用API
          fetchAvailableStudents();
        }
      }
      // 搜索条件变化但都是有搜索状态
      else if (hasSearchCondition) {
        if (pagination.current !== 1) {
          setPagination((prev) => ({ ...prev, current: 1 }));
        } else {
          // 已经在第1页，直接调用API
          fetchAvailableStudents();
        }
      }

      // 更新ref
      prevSearchStateRef.current = hasSearchCondition;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchName, debouncedSearchCode]);

  // 监听分页变化，触发数据获取
  useEffect(() => {
    if (isOpen && viewMode === 'addStudents' && classId && semesterId) {
      fetchAvailableStudents();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current]);

  // 初始进入调入学生界面时加载数据
  useEffect(() => {
    if (isOpen && viewMode === 'addStudents' && classId && semesterId) {
      fetchAvailableStudents();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, viewMode, classId, semesterId]);

  // 获取班级树数据
  const fetchDeptTree = useCallback(async () => {
    if (!semesterId) return;

    try {
      setDeptTreeLoading(true);
      const response = await getSchoolDeptTree(semesterId);

      if (response && Array.isArray(response)) {
        // 转换数据格式为 Cascader 需要的格式
        const transformTreeData = (nodes: any[]): any[] => {
          return nodes.map((node) => ({
            label: node.deptName,
            value: node.id,
            children:
              node.children && node.children.length > 0
                ? transformTreeData(node.children)
                : undefined
          }));
        };

        const transformedData = transformTreeData(response);
        setDeptTreeData(transformedData);
      }
    } catch (error) {
      console.error('获取班级树失败:', error);
      Toast.error('获取班级树失败');
    } finally {
      setDeptTreeLoading(false);
    }
  }, [semesterId]);

  // 显示调出学生的 Popover
  const handleRemoveStudent = (studentId: string, studentName: string) => {
    setRemovePopoverVisible(studentId);
    // 重置选择状态
    setSelectedStageId(undefined);
    setSelectedGradeId(undefined);
    setSelectedNewClazzId(undefined);
    setCascaderValue([]);
    // 获取班级树数据
    if (deptTreeData.length === 0) {
      fetchDeptTree();
    }
  };

  // 确认调出学生
  const handleConfirmRemoveStudent = async (studentId: string, studentName: string) => {
    if (!selectedStageId || !selectedGradeId || !selectedNewClazzId) {
      Toast.error('请选择完整的年级和班级信息');
      return;
    }

    try {
      setRemovingStudentId(studentId);

      // 调用调出接口
      await changeClazz({
        id: studentId,
        stageId: selectedStageId,
        gradeId: selectedGradeId,
        clazzId: selectedNewClazzId
      });

      // 从当前列表中移除学生
      setCurrentClassStudents((prev) => prev.filter((student) => student.id !== studentId));
      Toast.success(`已成功调出学生 ${studentName}`);

      // 关闭 Popover
      setRemovePopoverVisible(null);

      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('调出学生失败:', error);
      Toast.error('调出学生失败');
    } finally {
      setRemovingStudentId(null);
    }
  };

  // 取消调出
  const handleCancelRemove = () => {
    setRemovePopoverVisible(null);
    setSelectedStageId(undefined);
    setSelectedGradeId(undefined);
    setSelectedNewClazzId(undefined);
    setCascaderValue([]);
  };

  // 切换到调入学生界面
  const handleAddStudent = () => {
    setViewMode('addStudents');
    setSelectedStudentIds([]);
    setPagination({ current: 1, size: 10, total: 0 });
    setSavedPageBeforeSearch(null); // 重置保存的页码状态
    prevSearchStateRef.current = false; // 重置搜索状态ref
  };

  // 返回主界面
  const handleBackToMain = () => {
    setViewMode('main');
    setSelectedStudentIds([]);
    setSearchName('');
    setSearchCode('');
    setSavedPageBeforeSearch(null); // 清理保存的页码状态
    prevSearchStateRef.current = false; // 重置搜索状态ref
  };

  // 处理学生选择
  const handleStudentSelect = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudentIds((prev) => [...prev, studentId]);
    } else {
      setSelectedStudentIds((prev) => prev.filter((id) => id !== studentId));
    }
  };

  // 确认调入学生
  const handleConfirmAddStudents = async () => {
    if (selectedStudentIds.length === 0) {
      Toast.error('请选择要调入的学生');
      return;
    }

    if (!classId || !semesterId) {
      Toast.error('缺少必要参数');
      return;
    }

    try {
      setLoading(true);

      // 调用批量调入接口
      await batchChangeClazz({
        clazzId: classId,
        semesterId: semesterId,
        studentIds: selectedStudentIds.map((id) => parseInt(id))
      });

      Toast.success(`已成功调入 ${selectedStudentIds.length} 名学生`);

      // 回到主界面
      setViewMode('main');
      // 刷新主界面数据
      fetchCurrentClassStudents();

      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('调入学生失败:', error);
      Toast.error('调入学生失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  // 渲染调出学生的 Popover 内容
  const renderRemovePopoverContent = (studentId: string, studentName: string) => {
    return (
      <div style={{ width: '320px', padding: '8px' }}>
        {/* 警告信息 */}
        <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '16px' }}>
          <ExclamationCircleOutlined
            style={{
              color: '#fa8c16',
              fontSize: '16px',
              marginRight: '8px',
              marginTop: '2px'
            }}
          />
          <span style={{ fontSize: '14px', color: '#262626' }}>
            请为学生【{studentName}】选择要调入的新班级。
          </span>
        </div>

        {/* 年级班级选择器 */}
        <div style={{ marginBottom: '16px' }}>
          <Cascader
            placeholder="请选择年级和班级"
            style={{ width: '100%' }}
            value={cascaderValue}
            options={deptTreeData}
            loading={deptTreeLoading}
            onChange={(value) => {
              setCascaderValue(value);

              // 根据选择的层级设置对应的 ID
              if (value && value.length >= 3) {
                setSelectedStageId(value[0] as number);
                setSelectedGradeId(value[1] as number);
                setSelectedNewClazzId(value[2] as number);
              } else {
                setSelectedStageId(undefined);
                setSelectedGradeId(undefined);
                setSelectedNewClazzId(undefined);
              }
            }}
            changeOnSelect={false}
            expandTrigger="click"
            getPopupContainer={() => document.body}
            allowClear
            showSearch={false}
          />
        </div>

        {/* 操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
          <AntButton onClick={handleCancelRemove}>取消</AntButton>
          <AntButton
            type="primary"
            loading={removingStudentId === studentId}
            onClick={() => handleConfirmRemoveStudent(studentId, studentName)}
            style={{ backgroundColor: '#7C3AED', borderColor: '#7C3AED' }}
          >
            确认调出
          </AntButton>
        </div>
      </div>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} isCentered size="4xl">
      <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
      <ModalContent bg="white" borderRadius="8px" minW="800px" maxW="800px" height="700px">
        <ModalBody p={0} display="flex" flexDirection="column" height="100%">
          {/* 标题栏 */}
          <Flex
            justifyContent="space-between"
            alignItems="center"
            p="24px 24px 20px 24px"
            borderBottom="1px solid #F2F3F5"
          >
            <Text fontSize="18px" fontWeight="500" color="#1D2129">
              学生管理
            </Text>
            <CloseButton onClick={handleCancel} />
          </Flex>

          {/* 主界面内容 */}
          {viewMode === 'main' && (
            <>
              {/* 操作按钮区域 */}
              <Box p="24px 32px 16px 32px">
                <Button
                  bg="#7C3AED"
                  color="white"
                  height="32px"
                  px="16px"
                  fontSize="14px"
                  fontWeight="400"
                  borderRadius="6px"
                  _hover={{ bg: '#6D28D9' }}
                  _active={{ bg: '#5B21B6' }}
                  onClick={handleAddStudent}
                >
                  调入学生
                </Button>
              </Box>

              {/* 学生列表 */}
              <Box flex="1" px="32px" display="flex" flexDirection="column" minHeight="0">
                <Box flex="1" overflowY="auto">
                  <Table variant="simple" size="md">
                    <Thead bg="#F8F9FA" position="sticky" top="0" zIndex="1">
                      <Tr>
                        <Th
                          color="#6B7280"
                          fontSize="14px"
                          fontWeight="500"
                          py="12px"
                          width="33.33%"
                        >
                          姓名
                        </Th>
                        <Th
                          color="#6B7280"
                          fontSize="14px"
                          fontWeight="500"
                          py="12px"
                          width="33.33%"
                        >
                          学号
                        </Th>
                        <Th
                          color="#6B7280"
                          fontSize="14px"
                          fontWeight="500"
                          py="12px"
                          width="33.33%"
                        >
                          操作
                        </Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {loading ? (
                        <Tr>
                          <Td colSpan={3} textAlign="center" py="40px">
                            加载中...
                          </Td>
                        </Tr>
                      ) : currentClassStudents.length === 0 ? (
                        <Tr>
                          <Td colSpan={3} textAlign="center" py="40px">
                            暂无学生
                          </Td>
                        </Tr>
                      ) : (
                        currentClassStudents.map((student) => (
                          <Tr key={student.id}>
                            <Td py="16px" width="33.33%">
                              {student.name}
                            </Td>
                            <Td py="16px" width="33.33%">
                              {student.code}
                            </Td>
                            <Td py="16px" width="33.33%">
                              <Popover
                                content={renderRemovePopoverContent(student.id, student.name)}
                                title={null}
                                trigger="click"
                                open={removePopoverVisible === student.id}
                                onOpenChange={(visible) => {
                                  if (visible) {
                                    handleRemoveStudent(student.id, student.name);
                                  } else {
                                    handleCancelRemove();
                                  }
                                }}
                                placement="top"
                                overlayStyle={{ zIndex: 10000 }}
                                getPopupContainer={() => document.body}
                              >
                                <Button
                                  variant="outline"
                                  borderColor="#7C3AED"
                                  color="#7C3AED"
                                  height="32px"
                                  px="16px"
                                  fontSize="14px"
                                  borderRadius="6px"
                                >
                                  调出
                                </Button>
                              </Popover>
                            </Td>
                          </Tr>
                        ))
                      )}
                    </Tbody>
                  </Table>
                </Box>
              </Box>

              {/* 分页 */}
              <Box p="16px 32px 32px 32px">
                {!loading && currentClassStudents.length > 0 && (
                  <Flex justifyContent="flex-end" alignItems="center">
                    <Flex gap="4px" alignItems="center">
                      {/* 上一页按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        isDisabled={pagination.current <= 1}
                        onClick={() =>
                          setPagination((prev) => ({ ...prev, current: prev.current - 1 }))
                        }
                        color="#6B7280"
                        _hover={{ bg: '#F3F4F6' }}
                      >
                        &lt;
                      </Button>

                      {/* 页码按钮 */}
                      {(() => {
                        const totalPages = Math.ceil(pagination.total / pagination.size);
                        const currentPage = pagination.current;
                        const pages = [];

                        // 计算显示的页码范围
                        let startPage = Math.max(1, currentPage - 2);
                        let endPage = Math.min(totalPages, currentPage + 2);

                        // 如果总页数小于等于5，显示所有页码
                        if (totalPages <= 5) {
                          startPage = 1;
                          endPage = totalPages;
                        }

                        // 生成页码按钮
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(
                            <Button
                              key={i}
                              variant={i === currentPage ? 'solid' : 'ghost'}
                              size="sm"
                              bg={i === currentPage ? '#7C3AED' : 'transparent'}
                              color={i === currentPage ? 'white' : '#6B7280'}
                              _hover={{
                                bg: i === currentPage ? '#6D28D9' : '#F3F4F6'
                              }}
                              onClick={() => setPagination((prev) => ({ ...prev, current: i }))}
                              minW="32px"
                              h="32px"
                            >
                              {i}
                            </Button>
                          );
                        }

                        // 如果需要显示省略号
                        if (startPage > 1) {
                          pages.unshift(
                            <Button
                              key="start"
                              variant="ghost"
                              size="sm"
                              color="#6B7280"
                              onClick={() => setPagination((prev) => ({ ...prev, current: 1 }))}
                              minW="32px"
                              h="32px"
                            >
                              1
                            </Button>
                          );
                          if (startPage > 2) {
                            pages.splice(
                              1,
                              0,
                              <Text key="start-ellipsis" color="#6B7280" fontSize="14px" px="2">
                                ...
                              </Text>
                            );
                          }
                        }

                        if (endPage < totalPages) {
                          if (endPage < totalPages - 1) {
                            pages.push(
                              <Text key="end-ellipsis" color="#6B7280" fontSize="14px" px="2">
                                ...
                              </Text>
                            );
                          }
                          pages.push(
                            <Button
                              key="end"
                              variant="ghost"
                              size="sm"
                              color="#6B7280"
                              onClick={() =>
                                setPagination((prev) => ({ ...prev, current: totalPages }))
                              }
                              minW="32px"
                              h="32px"
                            >
                              {totalPages}
                            </Button>
                          );
                        }

                        return pages;
                      })()}

                      {/* 下一页按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        isDisabled={
                          pagination.current >= Math.ceil(pagination.total / pagination.size)
                        }
                        onClick={() =>
                          setPagination((prev) => ({ ...prev, current: prev.current + 1 }))
                        }
                        color="#6B7280"
                        _hover={{ bg: '#F3F4F6' }}
                      >
                        &gt;
                      </Button>
                    </Flex>
                  </Flex>
                )}
              </Box>
            </>
          )}

          {/* 调入学生界面内容 */}
          {viewMode === 'addStudents' && (
            <>
              {/* 返回按钮 */}
              <Box p="24px 32px 16px 32px" color="#7C3AED">
                <Flex alignItems="center" onClick={handleBackToMain} cursor={'pointer'} w={'60px'}>
                  <ChevronLeftIcon fontSize="20px" mt="1px" />
                  <Text fontSize="14px" fontWeight="400" color="#7C3AED" mb={'0px'}>
                    返回
                  </Text>
                </Flex>
              </Box>

              {/* 搜索输入框 */}
              <Box px="32px" pb="16px">
                <Flex gap="16px">
                  <Input
                    placeholder="学生姓名"
                    value={searchName}
                    onChange={(e) => setSearchName(e.target.value)}
                    bg="#F8F9FA"
                    border="1px solid #E5E7EB"
                    borderRadius="6px"
                    fontSize="14px"
                    height="40px"
                    _placeholder={{ color: '#9CA3AF' }}
                    _focus={{
                      borderColor: '#7C3AED',
                      boxShadow: '0 0 0 1px #7C3AED'
                    }}
                  />
                  <Input
                    placeholder="学号"
                    value={searchCode}
                    onChange={(e) => setSearchCode(e.target.value)}
                    bg="#F8F9FA"
                    border="1px solid #E5E7EB"
                    borderRadius="6px"
                    fontSize="14px"
                    height="40px"
                    _placeholder={{ color: '#9CA3AF' }}
                    _focus={{
                      borderColor: '#7C3AED',
                      boxShadow: '0 0 0 1px #7C3AED'
                    }}
                  />
                </Flex>
              </Box>

              {/* 学生列表 */}
              <Box flex="1" px="32px" display="flex" flexDirection="column" minHeight="0">
                <Box flex="1" overflowY="auto">
                  <Table variant="simple" size="md">
                    <Thead bg="#F8F9FA" position="sticky" top="0" zIndex="1">
                      <Tr>
                        <Th color="#6B7280" fontSize="14px" fontWeight="500" py="12px" width="10%">
                          <Checkbox
                            colorScheme="purple"
                            isChecked={
                              selectedStudentIds.length === availableStudents.length &&
                              availableStudents.length > 0
                            }
                            isIndeterminate={
                              selectedStudentIds.length > 0 &&
                              selectedStudentIds.length < availableStudents.length
                            }
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedStudentIds(availableStudents.map((s) => s.id));
                              } else {
                                setSelectedStudentIds([]);
                              }
                            }}
                          />
                        </Th>
                        <Th color="#6B7280" fontSize="14px" fontWeight="500" py="12px" width="25%">
                          姓名
                        </Th>
                        <Th color="#6B7280" fontSize="14px" fontWeight="500" py="12px" width="25%">
                          学号
                        </Th>
                        <Th color="#6B7280" fontSize="14px" fontWeight="500" py="12px" width="40%">
                          当前所在班级
                        </Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {loading ? (
                        <Tr>
                          <Td colSpan={4} textAlign="center" py="40px">
                            加载中...
                          </Td>
                        </Tr>
                      ) : availableStudents.length === 0 ? (
                        <Tr>
                          <Td colSpan={4} textAlign="center" py="40px">
                            暂无可调入学生
                          </Td>
                        </Tr>
                      ) : (
                        availableStudents.map((student) => (
                          <Tr key={student.id}>
                            <Td py="16px" width="10%">
                              <Checkbox
                                colorScheme="purple"
                                isChecked={selectedStudentIds.includes(student.id)}
                                onChange={(e) => handleStudentSelect(student.id, e.target.checked)}
                              />
                            </Td>
                            <Td py="16px" width="25%">
                              {student.name}
                            </Td>
                            <Td py="16px" width="25%">
                              {student.code}
                            </Td>
                            <Td py="16px" width="40%">
                              {student.currentClass || '-'}
                            </Td>
                          </Tr>
                        ))
                      )}
                    </Tbody>
                  </Table>
                </Box>
              </Box>

              {/* 分页 */}
              <Box p="16px 32px 16px 32px">
                {!loading && availableStudents.length > 0 && (
                  <Flex justifyContent="flex-end" alignItems="center">
                    <Flex gap="4px" alignItems="center">
                      {/* 上一页按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        isDisabled={pagination.current <= 1}
                        onClick={() =>
                          setPagination((prev) => ({ ...prev, current: prev.current - 1 }))
                        }
                        color="#6B7280"
                        _hover={{ bg: '#F3F4F6' }}
                      >
                        &lt;
                      </Button>

                      {/* 页码按钮 */}
                      {(() => {
                        const totalPages = Math.ceil(pagination.total / pagination.size);
                        const currentPage = pagination.current;
                        const pages = [];

                        // 计算显示的页码范围
                        let startPage = Math.max(1, currentPage - 2);
                        let endPage = Math.min(totalPages, currentPage + 2);

                        // 如果总页数小于等于5，显示所有页码
                        if (totalPages <= 5) {
                          startPage = 1;
                          endPage = totalPages;
                        }

                        // 生成页码按钮
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(
                            <Button
                              key={i}
                              variant={i === currentPage ? 'solid' : 'ghost'}
                              size="sm"
                              bg={i === currentPage ? '#7C3AED' : 'transparent'}
                              color={i === currentPage ? 'white' : '#6B7280'}
                              _hover={{
                                bg: i === currentPage ? '#6D28D9' : '#F3F4F6'
                              }}
                              onClick={() => setPagination((prev) => ({ ...prev, current: i }))}
                              minW="32px"
                              h="32px"
                            >
                              {i}
                            </Button>
                          );
                        }

                        // 如果需要显示省略号
                        if (startPage > 1) {
                          pages.unshift(
                            <Button
                              key="start"
                              variant="ghost"
                              size="sm"
                              color="#6B7280"
                              onClick={() => setPagination((prev) => ({ ...prev, current: 1 }))}
                              minW="32px"
                              h="32px"
                            >
                              1
                            </Button>
                          );
                          if (startPage > 2) {
                            pages.splice(
                              1,
                              0,
                              <Text key="start-ellipsis" color="#6B7280" fontSize="14px" px="2">
                                ...
                              </Text>
                            );
                          }
                        }

                        if (endPage < totalPages) {
                          if (endPage < totalPages - 1) {
                            pages.push(
                              <Text key="end-ellipsis" color="#6B7280" fontSize="14px" px="2">
                                ...
                              </Text>
                            );
                          }
                          pages.push(
                            <Button
                              key="end"
                              variant="ghost"
                              size="sm"
                              color="#6B7280"
                              onClick={() =>
                                setPagination((prev) => ({ ...prev, current: totalPages }))
                              }
                              minW="32px"
                              h="32px"
                            >
                              {totalPages}
                            </Button>
                          );
                        }

                        return pages;
                      })()}

                      {/* 下一页按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        isDisabled={
                          pagination.current >= Math.ceil(pagination.total / pagination.size)
                        }
                        onClick={() =>
                          setPagination((prev) => ({ ...prev, current: prev.current + 1 }))
                        }
                        color="#6B7280"
                        _hover={{ bg: '#F3F4F6' }}
                      >
                        &gt;
                      </Button>
                    </Flex>
                  </Flex>
                )}
              </Box>

              {/* 确认调入按钮 */}
              <Box p="0 32px 32px 32px">
                <Flex justifyContent="flex-end">
                  <Button
                    bg="#7C3AED"
                    color="white"
                    height="32px"
                    px="16px"
                    fontSize="14px"
                    borderRadius="6px"
                    _hover={{ bg: '#6D28D9' }}
                    _active={{ bg: '#5B21B6' }}
                    isLoading={loading}
                    isDisabled={selectedStudentIds.length === 0}
                    onClick={handleConfirmAddStudents}
                  >
                    确认调入
                  </Button>
                </Flex>
              </Box>
            </>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default StudentManagementModal;
