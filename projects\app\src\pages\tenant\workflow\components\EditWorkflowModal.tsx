import React, { useMemo, useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalBody, Button } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { createWorkflow, updateWorkflow } from '@/api/tenant/workflow';
import { CreateWorkflowParams, UpdateWorkflowParams } from '@/types/api/tenant/workflow';
import { useToast } from '@/hooks/useToast';
import { Select, Input, Form } from 'antd';
import { getTenantAppList } from '@/api/tenant/app';
import { TenantAppListItemType } from '@/types/api/tenant/app';

const { Option } = Select;

const EditWorkflowModal = ({
  onClose,
  onSuccess,
  formStatus,
  name,
  appId,
  workflowId
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  formStatus: 'add' | 'edit';
  name?: string;
  appId?: string;
  workflowId?: string;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [appList, setAppList] = useState<TenantAppListItemType[]>([]);
  const { toast } = useToast();
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const filteredApps = appList.filter(
    (app) =>
      app.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      app.appId.toLowerCase().includes(searchValue.toLowerCase())
  );

  useEffect(() => {
    const fetchAppList = async () => {
      const response = await getTenantAppList({});
      setAppList(response);
    };
    fetchAppList();
  }, []);

  const typeMap = useMemo(
    () =>
      formStatus === 'edit'
        ? {
            title: t('编辑工作流')
          }
        : {
            title: t('新增工作流')
          },
    [formStatus, t]
  );

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: async () => {
      const values = await form.validateFields();
      if (formStatus === 'add') {
        const params: CreateWorkflowParams = {
          name: values.name,
          tenantAppId: values.appId
        };
        return createWorkflow(params);
      } else {
        const params: UpdateWorkflowParams = {
          id: workflowId!,
          name: values.name
        };
        return updateWorkflow(params);
      }
    },
    onSuccess: (res) => {
      onClose && onClose();
      onSuccess && onSuccess();
      toast({
        status: 'success',
        title: formStatus === 'add' ? t('新增成功') : t('修改成功')
      });
    }
  });

  return (
    <MyModal
      isOpen={true}
      onClose={onClose}
      closeOnOverlayClick={false}
      iconSrc="common/workflowFill"
      title={typeMap.title}
    >
      <ModalBody>
        <Form form={form} layout="vertical" initialValues={{ name, appId }}>
          {formStatus === 'add' && (
            <Form.Item
              name="appId"
              label={t('选择应用')}
              rules={[{ required: true, message: t('请选择应用') }]}
            >
              <Select
                showSearch
                placeholder={t('请选择应用')}
                dropdownStyle={{ zIndex: 99999 }} // 提高下拉菜单的 z-index
                onSearch={handleSearch}
                filterOption={false} // 禁用默认的 filterOption
                optionLabelProp="label"
              >
                {filteredApps.map((app) => (
                  <Option key={app.id} value={app.id} label={app.name}>
                    <div>
                      {app.name}
                      <span style={{ color: 'grayText', marginLeft: '8px' }}>
                        {`AppId:${app.appId}`}
                      </span>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          <Form.Item
            name="name"
            label={t('工作流名称')}
            rules={[{ required: true, message: t('请输入工作流名称') }]}
          >
            <Input placeholder={t('请输入工作流名称')} maxLength={20} />
          </Form.Item>
        </Form>
      </ModalBody>
      <ModalFooter>
        <Button isLoading={isLoading} onClick={onSave}>
          {t('确定')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditWorkflowModal;
