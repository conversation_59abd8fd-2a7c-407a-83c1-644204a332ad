import type { TenantSubjectItem } from '@/types/api/teacher';

/**
 * 处理学科列表数据，过滤有效数据并去重
 * @param rawList 原始学科数据
 * @returns 处理后的学科列表
 */
export const processSubjectList = (rawList: any[]): TenantSubjectItem[] => {
  console.log('原始学科数据:', rawList);
  
  // 过滤有效数据并去重
  const subjectMap = new Map();
  const list = rawList
    .filter((sub) => sub.subjectId && sub.subjectName)
    .filter((sub) => {
      const key = `${sub.subjectId}-${sub.subjectName}`;
      if (subjectMap.has(key)) {
        console.log(`发现重复学科，跳过: ${sub.subjectName} (ID: ${sub.subjectId})`);
        return false;
      }
      subjectMap.set(key, sub);
      return true;
    });
  
  console.log('过滤并去重后的学科列表:', list);
  console.log('学科列表长度:', list.length);
  
  return list;
};

/**
 * 从API响应中提取并处理学科列表
 * @param res API响应数据
 * @returns 处理后的学科列表
 */
export const extractSubjectListFromResponse = (res: any): TenantSubjectItem[] => {
  // 兼容不同的响应格式
  const rawList = (Array.isArray(res) ? res : res.data || []);
  return processSubjectList(rawList);
}; 