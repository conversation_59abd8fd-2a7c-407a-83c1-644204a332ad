import { IsStructuredPrompt } from './app.d';
import { appointedTypeEnum, AppStatus, IsStructuredPrompt, Source } from '@/constants/api/app';
import { DatasetSearchModeEnum } from '@/constants/api/dataset';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { AppChatConfigType, AppDetailType, AppSchema } from '@/fastgpt/global/core/app/type';
import { StoreNodeItemType } from '@/fastgpt/global/core/workflow/type/node';
import { FileType } from './cloud';
import { FileTypeEnum } from '@/constants/api/cloud';
import { FileMetaType } from './file';
import { DynamicFormValue } from './chat';

export type transitionWorkflowBody = {
  id: string;
  createNew?: boolean;
};

export interface AppLabelType {
  id: string;
  sceneId: string;
  name: string;
  sort: number;
  tenantId?: string;
  tenantSceneId: string;
}

export type AppLabelAppType = AppLabelType & {
  appCount: number;
  isDeleted?: number;
};

export type AppModalDataType = {
  id?: string;
  name?: string;
  avatarUrl?: string;
  sceneId?: string;
  labelId?: string;
  permission?: PermissionTypeEnum;
  intro?: string;
};

export type AppListItemType = {
  id: string;
  name: string;
  avatarUrl: string;
  finalAppId: string;
  tenantId: string;
  intro: string;
  isOwner: boolean;
  canWrite: boolean;
  mode: ModeTypeEnum;
  permission: PermissionTypeEnum;
  appointedType?: appointedTypeEnum;
  tenantSceneIds: string[];
  tenantLabelIds: string[];
  tmbId: string;
  appId: string;
  source: DataSource;
  permission: number;
  updateTime: string;
  labelList?: LabelItemType[];

  type: AppTypeEnum;
  sceneId?: string;
  labelId?: string;
  homePageUse?: number;
  appTaskTypeId?: string | null;
  linkUrl?: string;
  config?: number;
  isCommonApp?: number;
  isPublished?: string | number;
};

export type LabelItemType = {
  id: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: number;
  tenantLabelId: string;
  tenantSceneId: string;
  tenantSceneName: string;
  tenantLabelName: string;
  isDisplayed?: number;
};

export interface AppListParams {
  tenantLabelId?: string;
  tenantSceneId?: string;
  permission?: number;
}

export type CreateAppParams = {
  name: string;
  avatarUrl?: string;
  type?: AppTypeEnum;
  modules?: Appsc;
  edges?: Edge;
  chatConfig?: AppChatConfigType;
  mode?: ModeTypeEnum;
  intro?: string;
  /** 值为1就是AI创建，为0就是标准创建 */
  isAICreated?: number;
};

export type appCenterCopyParams = {
  id: string;
  name: string;
  avatarUrl: string;
  intro: string;
};

export type CreateAppType = {
  id: string;
  name: string;
  sceneId?: string;
  labelId?: string;
  type?: AppTypeEnum;
  simpleTemplateId?: string;
  avatarUrl?: string;
  intro?: string;
};
export interface DeleteAppParams {
  id: string;
  tmbId: string;
}

export interface AppUpdateParams {
  id?: string;
  name: string;
  sceneId?: string;
  labelId?: string;
  type?: AppTypeEnum;
  simpleTemplateId?: string;
  avatarUrl?: string;
  intro?: string;

  mode?: ModeTypeEnum;
  filesList?: TenantAppKnowledgeFile[];
  modules?: AppSchema['modules'];
  edges?: AppSchema['edges'];
  nodes?: AppSchema['modules'];
  chatConfig?: AppSchema['chatConfig'];

  permission?: PermissionTypeEnum;
  isStructuredPrompt?: IsStructuredPrompt;
  promptList?: SimpleAppPrompt[];
  filesList?: TenantAppKnowledgeFile[];
}

export type AppLabelListParams = {
  tenantSceneId: string;
};

export type AppLabelDetailParams = {
  id: string;
};

export interface AppLabelUpdateParams {
  id?: string;
  sceneId?: string;
  name?: string;
  sort?: number;
  isDelete?: number;
}

export interface AppLabelReorderParams {
  param: AppLabelUpdateParams[];
}

export type SelectedDatasetType = { datasetId: string; vectorModel: VectorModelItemType }[];

export type PostPublishAppProps = {
  type: AppTypeEnum;
  nodes: AppSchema['modules'];
  edges: AppSchema['edges'];
  chatConfig: AppSchema['chatConfig'];
  id: string;
  name: string;
  isStructuredPrompt: IsStructuredPrompt;
  promptList: SimpleAppPrompt[];
  filesList: TenantAppKnowledgeFile[];
};

export interface TenantAppKnowledgeFile {
  description?: string; // 租户应用背景知识文件表
  createTime?: string; // 创建时间，格式为 date-time
  fileId?: string; // 文件ID，类型为 int32
  fileKey: string; // 文件key
  fileName?: string; // 文件key
  fileSize?: number; // 文件key
  fileUrl?: string; // 文件key
  fileType?: FileTypeEnum; // 文件key
  id?: string; // ID，类型为 int32
  isDeleted?: number; // 是否删除，类型为 int32
  tenantAppId?: string; // 租户应用ID，类型为 int32
  updateTime?: string; // 更新时间，格式为 date-time
  type?: number;
  authority?: AuthorityTypeEnum;
}

export enum AuthorityTypeEnum {
  Invalid = 0,
  Valid = 1
}

export type countChatInputGuideTotalQuery = { appId: string };

export type countChatInputGuideTotalBody = {};
export type countChatInputGuideTotalResponse = { total: number };

export type getLatestVersionQuery = {
  appId: string;
};

export type getLatestVersionBody = {};

export type getLatestVersionResponse = {
  nodes: StoreNodeItemType[];
  edges: StoreEdgeItemType[];
  chatConfig: AppChatConfigType;
};

export interface SimpleAppPrompt {
  id?: number;
  title: string;
  content: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  tenantAppId?: number;
  isEditing?: boolean;
}

interface AppDetailInfo {
  appId: string;
  avatarUrl: string;
  config: number;
  createTime: string;
  createUsername: string;
  finalAppId: string;
  id: string;
  intro: string;
  isDeleted: number;
  labelList: any[]; // Replace with actual type if known
  mode: number;
  name: string;
  permission: number;
  promptList: SimpleAppPrompt[]; // Replace with actual type if known
  sceneList: any[]; // Replace with actual type if known
  sort: number;
  sortNum: number;
  source: DataSource;
  status: AppStatus;
  tenantId: string;
  tenantLabelId: string;
  tenantLabelIds: string[];
  tenantName: string;
  tenantSceneIds: string[];
  tmbId: string;
  type: AppTypeEnum;
  updateTime: Date;
  updateUsername: string;
  userName: string;
  isStructuredPrompt: IsStructuredPrompt;
  filesList: TenantAppKnowledgeFile[];
  appTaskTypeId: string;
}

export type AppDetailMerge = Partial<AppDetailInfo> & AppDetailType;

interface Option {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  componentId: string;
  content: string;
  sort: number;
}

interface Component {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  formId: string;
  title: string;
  placeholder: string;
  type: number;
  isRequired: number;
  isMultiselect: number;
  isUploadPic: number;
  isUploadAv: number;
  isUploadText: number;
  maxFiles: number;
  sort: number;
  isCallOcr?: 1 | 0;
  options: Option[];
  key?: string;
  value?: DynamicFormValue;
  isTiled?: number; // 默认值
  isWideWindow?: number; // 默认值
  isSideBar?: number; // 默认值
  children?: Component[]; // 添加 children 属性
}

export type ClientAppFormDetailType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: string;
  status: number;
  appName: string;
  components: Component[];
};

export type ClientAppFormCompletionsParams = {
  appId: string;
  chatAppId: number;
  chatId: string;
  content: string;
  dataIds: string[];
  detail: boolean;
  fileKeys: string[];
  files: string[];
  messages: Array<Record<string, unknown>>;
  ocrFileContent: string;
  ocrFileKey?: string;
  responseChatItemId: string;
  responseData: string;
  stream: boolean;
  tenantAppId: number;
  tenantId: number;
  title: string;
  tmbId: number;
  value: string;
  variables: Record<string, unknown>;
};
export type SystemTenantGuideValidFinishType = boolean;

export type ClientUseGuidanceDetailType =
  | {
      id: number;
    }
  | undefined
  | boolean;

export type DynamicRadioFormListFromFieldParams = {
  fieldId: string;
  grade?: string;
  discipline?: string;
  edition?: string;
};

export type CloudFileGetContentStatusType = {
  fileParseStatus: number;
  fileId: number;
};
