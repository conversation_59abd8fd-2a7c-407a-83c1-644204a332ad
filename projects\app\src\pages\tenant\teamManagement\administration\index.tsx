import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { Tabs, Card, Table, Space, Empty, Segmented } from 'antd';
import SearchBar from './components/SearchBar';
import { Box, useDisclosure, Button, border, Flex } from '@chakra-ui/react';
import { vwDims } from '@/utils/chakra';
import PageContainer from '@/components/PageContainer';
import {
  setClientSchoolDeptManageBind,
  setClientSchoolDeptSubjectManageBind
} from '@/api/tenant/teamManagement/teach';
import SettingModal from './components/SettingModal/index';
import SynchronizationModal from './components/SynchronizationModal/index';
import ImportPanel from '@/components/ImportPanel';
import AdministrationManagement from './components/AdministrationManagment/index';
import SubjectTeachingManagement from './components/SubjectTeachingManagement/index';
import SubjectHeadManagement from './components/SubjectHeadManagement/index';
import ClassManagement from './components/ClassManagement/index';
import AddClassModal from './components/AddClassModal/index';
import SubjectConfiguration from './components/SubjectConfiguration/index';
import { TmbUser, DepartmentNode, Department } from '@/types/api/tenant/teamManagement/teach';
import SvgIcon from '@/components/SvgIcon';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import { ColumnType } from 'antd/es/table';
import MyTooltip from '@/components/MyTooltip';
import styles from '../index.module.scss';
import { getClientSchoolManageTeacherTree } from '@/api/tenant/teamManagement/administration';
import ImportPanelV2 from '@/components/ImportPanelV2';
import { useOverlayManager } from '@/hooks/useOverlayManager';

const { TabPane } = Tabs;

interface Semester {
  semesterId: string;
  semester: {
    id: string;
    year: string;
    type: 1 | 2;
    startDate: string;
    endDate: string;
    isCurrent: 0 | 1;
    // 可以添加其他属性如 createTime, updateTime 等
  };
}

interface Grade {
  [subjectName: string]: Department;
}

const Teach: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('1');
  const [activeOrgTab, setActiveOrgTab] = useState<'teaching' | 'subject'>('teaching');
  const [currentSemester, setCurrentSemester] = useState<Semester | null>(null);
  const [semesterId, setSemesterId] = useState<string>('');
  const [treeData, setTreeData] = useState<DepartmentNode[]>([]);

  // 移除学科教师管理相关状态
  const [copiedUsers, setCopiedUsers] = useState<TmbUser[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const lastHoveredIdRef = useRef<string | null>(null);
  const lastCopyTimeRef = useRef<number>(0);

  const [semesterData, setSemesterData] = useState({
    year: '',
    type: 1 as 1 | 2
  });
  const [highlightedTmbIds, setHighlightedTmbIds] = useState<number[]>([]);
  const [hoveredCell, setHoveredCell] = useState<{
    level: string;
    id: string;
    current: string;
    users: TmbUser[];
  } | null>(null);
  const [classTreeData, setClassTreeData] = useState<any>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [semesterStatus, setSemesterStatus] = useState<0 | 1 | 2 | null>(null);
  const [showSubjectConfiguration, setShowSubjectConfiguration] = useState(false);

  const {
    isOpen: isOpenSettingModal,
    onOpen: onOpenSettingModal,
    onClose: onCloseSettingModal
  } = useDisclosure();

  const {
    isOpen: isOpenAddClassModal,
    onOpen: onOpenAddClassModal,
    onClose: onCloseAddClassModal
  } = useDisclosure();

  // 移除 SettingSubjectsModal 的 useDisclosure

  const {
    isOpen: isOpenSynchronizationModal,
    onOpen: onOpenSynchronizationModal,
    onClose: onCloseSynchronizationModal
  } = useDisclosure();

  const { openOverlay } = useOverlayManager();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && hoveredCell) {
        e.preventDefault();
        const { users } = hoveredCell;
        setCopiedUsers(users);
        handleCopy(users);
      }
    };

    const handlePaste = (e: ClipboardEvent) => {
      if (hoveredCell) {
        e.preventDefault();
        handlePasteAction(hoveredCell.current, copiedUsers);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('paste', handlePaste);
    };
  }, [hoveredCell, copiedUsers]);

  const handlePasteAction = async (id: string, users: TmbUser[]) => {
    if (!id || users.length === 0) {
      // Toast.error('无法粘贴，没有复制的教师或无效的目标位置');
      return;
    }

    try {
      // 获取目标位置已经存在的教师列表
      const existingUsers = treeData.find((node) => node.id === id)?.tmbUserList || [];
      // 过滤掉已经存在的教师
      const newUsers = users.filter(
        (user) => !existingUsers.some((existingUser) => existingUser.tmbId === user.tmbId)
      );

      if (newUsers.length === 0) {
        Toast.info('目标位置已经包含所有要粘贴的教师');
        return;
      }

      const tmbIds = newUsers.map((user) => user.tmbId).join(',');
      if (activeTab === '1') {
        await setClientSchoolDeptManageBind({ id, tmbIds });
      } else {
        await setClientSchoolDeptSubjectManageBind({ id, tmbIds });
      }
      Toast.success('粘贴成功');
      refreshList();
    } catch (error) {
      console.error('粘贴失败:', error);
      Toast.error('粘贴失败，请重试');
    }
  };

  const handleCopy = (users: TmbUser[]) => {
    const currentTime = Date.now();
    const timeSinceLastCopy = currentTime - lastCopyTimeRef.current;

    // 如果距离上次复制的时间小于 500 毫秒，则不显示提示
    if (timeSinceLastCopy < 500) {
      return;
    }

    lastCopyTimeRef.current = currentTime;

    const copiedText = users.map((user) => user.userName).join(', ');
    navigator.clipboard
      .writeText(copiedText)
      .then(() => {
        Toast.success(`已复制 ${users.length} 个教师: ${copiedText}`);
        setCopiedUsers(users); // 确保在复制操作后更新 copiedUsers 状态
      })
      .catch((err) => {
        console.error('复制失败:', err);
        Toast.error('复制失败，请重试');
      });
  };

  const renderTmbUsers = (
    users: TmbUser[],
    level: string,
    id: string,
    isTableCell: boolean = false
  ) => {
    const renderUserSpans = (users: TmbUser[]): JSX.Element[] => {
      return users.map((user) => (
        <span
          key={user.tmbId}
          style={{
            color: highlightedTmbIds.includes(Number(user.tmbId)) ? 'red' : '#000'
          }}
        >
          {user.userName}
        </span>
      ));
    };

    const combineUserSpans = (userSpans: JSX.Element[]): JSX.Element => {
      return userSpans.reduce(
        (prev, curr, index) => (
          <>
            {prev}
            {index > 0 && ', '}
            {curr}
          </>
        ),
        <></>
      );
    };

    return (
      <span
        onMouseEnter={() => {
          if (isTableCell) {
            setHoveredCell({ level, id, current: id, users });
            lastHoveredIdRef.current = id;
          }
        }}
        onMouseLeave={() => {
          if (isTableCell) {
            setHoveredCell(null);
          }
        }}
        tabIndex={isTableCell ? 0 : undefined}
        style={{ cursor: isTableCell ? 'pointer' : 'default' }}
      >
        {users.length > 0 ? combineUserSpans(renderUserSpans(users)) : '未设置'}
      </span>
    );
  };

  const onSynchronization = () => {
    onOpenSynchronizationModal();
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换tab时重置学科配置显示状态
    setShowSubjectConfiguration(false);
    // 移除这里对getClientSchoolDeptSubjectManageTree的调用
  };

  const TooltipContent = () => {
    return (
      <MyTooltip
        label={
          <Box>
            <Box>• 用途：教学管理设置主要用于分配教师的教学数据可见范围及管理；</Box>
            <Box>
              •
              快捷操作：可复制（Ctrl+C）已设置的教师，粘贴（Ctrl+V）至其他未设置或已设置的格子中，或鼠标右击复制粘贴。
            </Box>
          </Box>
        }
        placement="top"
      >
        <SvgIcon name="doubt" w="20px" h="20px" cursor="pointer" ml="15px" />
      </MyTooltip>
    );
  };

  // 简化handleEditSubjectTeachers函数，只处理sync角色
  const handleEditSubjectTeachers = (
    role: 'leader' | 'phase_leader' | 'teacher' | 'sync',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string
  ) => {
    if (role === 'sync') {
      onOpenSynchronizationModal();
    }
    // 移除其他角色的处理逻辑
  };

  // 保留handleEditTeachers函数
  const handleEditTeachers = (
    level: string,
    id: string,
    gradeName?: string,
    className?: string,
    tmbIds?: string[]
  ) => {
    console.log('父组件接收到编辑请求:', { level, id, gradeName, className, tmbIds });
  };

  const handleSemesterChange = (semesterData: any | null) => {
    setCurrentSemester(semesterData);
    if (semesterData && semesterData.semester && semesterData.semesterId) {
      const semester = semesterData.semester;

      // 设置学期状态
      setSemesterStatus(semester.isCurrent);

      // 检查学期状态
      if (semester.isCurrent === 0) {
        // 未开始学期
        MessageBox.info({
          title: '提示',
          content: '请到学期管理中开始该学期并完成相关初始化',
          onOk: () => {
            // 可以选择跳转到学期管理页面
            // router.push('/tenant/teamManagement/semester');
          }
        });
      } else if (semester.isCurrent === 2) {
        // 已结束学期
        MessageBox.info({
          title: '提示',
          content: '该学期已结束，无法修改相关信息',
          onOk: () => {}
        });
        // 已结束的学期可以查看，但不能修改，所以继续执行后续逻辑
      }

      const newSemesterData = {
        year: semester.year,
        type: semester.type
      };
      setSemesterData(newSemesterData);

      setSemesterId(semesterData.semesterId);
    } else {
      setSemesterStatus(null);
    }
  };

  const refreshList = useCallback(() => {
    if (!currentSemester || !currentSemester.semesterId) return;

    setHighlightedTmbIds([]);

    // 触发ClassManagement组件刷新
    setRefreshTrigger((prev) => prev + 1);
  }, [currentSemester]);

  // 处理树形数据变化的回调
  const handleTreeDataChange = useCallback((treeData: any) => {
    setClassTreeData(treeData);
  }, []);

  // 处理添加班级按钮点击
  const handleAddClass = () => {
    onOpenAddClassModal();
  };

  const handleImportStudent = () => {
    openOverlay({
      Overlay: ImportPanelV2,
      props: {
        title: '批量导入班级学生',
        templateUrl: '/client/student/downloadTemplate',
        importUrl: '/huayun-ai/client/student/import',
        appendParams: {},
        onUploadSuccess: () => {}
      }
    });
  };

  // 批量导入行政组织教师
  const handleImportAdministrationTeacher = () => {
    openOverlay({
      Overlay: ImportPanelV2,
      props: {
        title: '批量导入行政组织教师',
        templateUrl: '/client/schoolManageTeacher/downloadTemplate',
        importUrl: '/huayun-ai/client/schoolManageTeacher/import',
        appendParams: {},
        onUploadSuccess: () => {
          refreshList(); // 导入成功后刷新列表
        }
      }
    });
  };

  // 批量导入教学组织信息
  const handleImportSubjectTeacher = () => {
    openOverlay({
      Overlay: ImportPanelV2,
      props: {
        title: '批量导入教学组织信息',
        templateUrl: '/client/schoolSubjectTeacher/downloadTemplate',
        importUrl: '/huayun-ai/client/schoolSubjectTeacher/import',
        appendParams: {},
        onUploadSuccess: () => {
          refreshList(); // 导入成功后刷新列表
        }
      }
    });
  };

  // 处理修改学科配置按钮点击
  const handleSubjectConfiguration = () => {
    setShowSubjectConfiguration(true);
  };

  // 处理关闭学科配置
  const handleCloseSubjectConfiguration = () => {
    setShowSubjectConfiguration(false);
  };

  return (
    <PageContainer p="24px">
      <Box fontSize="16px" fontWeight="700">
        行政与教学管理
      </Box>
      <Box ref={containerRef}>
        <Box
          display="flex"
          alignItems="flex-start"
          justifyContent="space-between"
          position="relative"
          flexDirection="column"
          height="calc(100vh - 100px)" // 增加减去的高度，为页面标题和padding留出空间
          overflow="hidden" // 防止内容溢出
        >
          <Box
            display="flex"
            width="100%"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            zIndex="10"
            bg="white" // 确保搜索区域有背景色
          >
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              style={{ width: '100%', paddingTop: '22px' }}
            >
              <TabPane tab="班级管理" key="1"></TabPane>
              <TabPane tab="行政组织管理" key="2"></TabPane>
              <TabPane tab="教学组织管理" key="3"></TabPane>
            </Tabs>
            <Box display="flex" alignItems="center" position="absolute" top="25px" right="12px">
              <SearchBar onSemesterChange={handleSemesterChange} />
            </Box>
          </Box>

          {/* 内容区域，设置滚动条 */}
          <Box
            flex="1"
            width="100%"
            position="relative"
            pt="10px"
            pb="20px"
            display="flex"
            flexDirection="column"
            height="0" // 强制flex子元素计算高度
            className="scrollable-content" // 添加类名方便定位
          >
            {activeTab === '1' && (
              <Box width="100%" display="flex" flexDirection="column" height="100%">
                {/* 固定头部按钮区域 */}
                <Box
                  position="sticky"
                  top="0"
                  bg="white"
                  zIndex="10"
                  pb={vwDims(20)}
                  borderBottom="1px solid #f0f0f0"
                  mb={vwDims(20)}
                >
                  <Flex gap="16px" width="100%" justifyContent="flex-end">
                    <Button
                      variant="outline"
                      colorScheme="primary"
                      isDisabled={semesterStatus !== 1} // 只有进行中的学期才能批量导入
                      onClick={handleImportStudent}
                    >
                      批量导入班级学生
                    </Button>
                    <Button
                      variant="outline"
                      colorScheme="primary"
                      onClick={handleAddClass}
                      leftIcon={<SvgIcon name="plus" />}
                      isDisabled={semesterStatus !== 1} // 只有进行中的学期才能添加班级
                    >
                      添加班级
                    </Button>
                  </Flex>
                </Box>

                {/* 可滚动内容区域 */}
                <Box
                  flex="1"
                  overflow="auto"
                  minHeight="0" // 确保flex子元素可以收缩
                  maxHeight="100%" // 限制最大高度
                >
                  <ClassManagement
                    semesterId={semesterId}
                    onRefresh={refreshList}
                    onTreeDataChange={handleTreeDataChange}
                    refreshTrigger={refreshTrigger}
                    semesterStatus={semesterStatus}
                  />
                </Box>
              </Box>
            )}

            {activeTab === '2' && (
              <Box width="100%" display="flex" flexDirection="column" height="100%">
                {/* 固定头部按钮区域 */}
                <Box
                  position="sticky"
                  top="0"
                  bg="white"
                  zIndex="10"
                  pb={vwDims(20)}
                  borderBottom="1px solid #f0f0f0"
                  mb={vwDims(20)}
                >
                  <Flex width="100%" justifyContent="flex-end">
                    <Button
                      variant="outline"
                      colorScheme="primary"
                      isDisabled={semesterStatus !== 1} // 只有进行中的学期才能批量导入
                      onClick={handleImportAdministrationTeacher}
                    >
                      批量导入行政组织教师
                    </Button>
                  </Flex>
                </Box>

                {/* 可滚动内容区域 */}
                <Box
                  flex="1"
                  overflow="auto"
                  minHeight="0" // 确保flex子元素可以收缩
                  maxHeight="100%" // 限制最大高度
                >
                  <AdministrationManagement
                    onEditTeachers={handleEditTeachers}
                    semesterData={semesterData}
                    semesterId={semesterId}
                    highlightedTmbIds={highlightedTmbIds}
                    refreshList={refreshList}
                  />
                </Box>
              </Box>
            )}

            {activeTab === '3' && (
              <Box width="100%" display="flex" flexDirection="column" height="100%">
                {/* 固定头部按钮区域 - 只在不显示学科配置时显示 */}
                {!showSubjectConfiguration && (
                  <Box
                    position="sticky"
                    top="0"
                    bg="white"
                    zIndex="10"
                    pb={vwDims(20)}
                    borderBottom="1px solid #f0f0f0"
                    mb={vwDims(20)}
                  >
                    <Box display="flex" alignItems="center">
                      <Box
                        sx={{
                          '.ant-segmented-item-selected': {
                            backgroundColor: '#7D4DFF !important',
                            color: 'white !important'
                          },
                          '.ant-segmented-thumb': {
                            backgroundColor: '#7D4DFF !important'
                          }
                        }}
                      >
                        <Segmented
                          options={[
                            { label: '班级学科教师设置', value: 'teaching' },
                            { label: '学科总负责人设置', value: 'subject' }
                          ]}
                          size="large"
                          value={activeOrgTab}
                          onChange={(value) => setActiveOrgTab(value as 'teaching' | 'subject')}
                        />
                      </Box>
                      <Flex gap="16px" marginLeft="auto" marginRight={vwDims(10)}>
                        <Button
                          colorScheme="primary"
                          variant="outline"
                          isDisabled={semesterStatus !== 1} // 只有进行中的学期才能修改
                          onClick={handleSubjectConfiguration}
                        >
                          修改学科配置
                        </Button>
                        <Button
                          isDisabled={semesterStatus !== 1}
                          onClick={handleImportSubjectTeacher}
                        >
                          批量导入教学组织信息
                        </Button>
                      </Flex>
                    </Box>
                  </Box>
                )}

                {/* 可滚动内容区域 */}
                <Box
                  flex="1"
                  overflow="auto"
                  minHeight="0" // 确保flex子元素可以收缩
                  maxHeight="100%" // 限制最大高度
                >
                  {showSubjectConfiguration ? (
                    <SubjectConfiguration
                      semesterId={semesterId}
                      refreshList={refreshList}
                      semesterData={semesterData}
                      onBack={handleCloseSubjectConfiguration}
                    />
                  ) : activeOrgTab === 'teaching' ? (
                    <SubjectTeachingManagement
                      semesterId={semesterId}
                      onEditTeachers={handleEditSubjectTeachers}
                      refreshList={refreshList}
                      semesterData={semesterData}
                      highlightedTmbIds={highlightedTmbIds}
                    />
                  ) : (
                    <SubjectHeadManagement
                      semesterId={semesterId}
                      onEditTeachers={handleEditSubjectTeachers}
                      refreshList={refreshList}
                      semesterData={semesterData}
                      highlightedTmbIds={highlightedTmbIds}
                    />
                  )}
                </Box>
              </Box>
            )}
          </Box>
        </Box>
      </Box>

      {isOpenSynchronizationModal && (
        <SynchronizationModal
          activeTab={activeTab}
          semesterData={semesterData}
          semesterId={semesterId}
          onClose={onCloseSynchronizationModal}
          onSuccess={refreshList}
        />
      )}

      {/* 添加班级弹窗 */}
      {isOpenAddClassModal && (
        <AddClassModal
          isOpen={isOpenAddClassModal}
          onClose={onCloseAddClassModal}
          semesterId={semesterId}
          treeData={classTreeData}
          onRefresh={refreshList}
        />
      )}

      <style jsx global>{`
        .scrollable-content::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        .scrollable-content::-webkit-scrollbar-thumb {
          background: #ddd;
          border-radius: 3px;
        }
        .scrollable-content::-webkit-scrollbar-thumb:hover {
          background: #bbb;
        }
      `}</style>
    </PageContainer>
  );
};

export default Teach;
