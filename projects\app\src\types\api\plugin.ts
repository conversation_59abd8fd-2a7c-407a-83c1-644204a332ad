import { ParentIdType } from '@/fastgpt/global/common/parentFolder/type';
import { FlowNodeTemplateTypeEnum } from '@/fastgpt/global/core/workflow/constants';
import { FlowNodeTypeEnum } from '@/fastgpt/global/core/workflow/node/constant';
import { FlowNodeCommonType, HandleType } from '@/fastgpt/global/core/workflow/type/node';

export type GetPreviewNodeQuery = { appId: string };

export type FlowNodeTemplateType = FlowNodeCommonType & {
  id: string; // node id, unique
  templateType: FlowNodeTemplateTypeEnum;

  // show handle
  sourceHandle?: HandleType;
  targetHandle?: HandleType;

  // info
  isTool?: boolean; // can be connected by tool

  // action
  forbidDelete?: boolean; // forbid delete
  unique?: boolean;
};

export type GetSystemPluginTemplatesBody = {
  searchKey?: string;
  parentId: ParentIdType;
};

export type NodeTemplateListItemType = {
  id: string; // 系统节点-系统节点的 id， 系统插件-插件的 id，团队应用的 id
  flowNodeType: FlowNodeTypeEnum; // render node card
  parentId?: ParentIdType;
  isFolder?: boolean;
  templateType: FlowNodeTemplateTypeEnum;
  avatar?: string;
  name: string;
  intro?: string; // template list intro
  isTool?: boolean;
  author?: string;
  unique?: boolean; // 唯一的
  currentCost?: number; // 当前积分消耗
};
