import type { RequestPageParams } from '@/types';
// 学生信息分页查询-请求参数
export interface StudentPageParams extends RequestPageParams {
  name?: string;
  status?: number;
  code?: string;
  clazzIds?: number[];
  sex?: number;
  account?: string;
}

// 学生信息分页查询-单条学生信息
export interface StudentPageItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number | null;
  tenantId: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  name: string;
  avatarUrl: string;
  code: string;
  sex: number;
  birthday: string;
  enrollmentDate: string;
  status: number;
  gradeName: string;
  clazzName: string;
  familyRelation: any[];
}

// 学生信息分页查询-响应数据
export interface StudentPageResponse {
  records: StudentPageItem[];
  total: number;
  size: number;
  current: number;
  [key: string]: any;
}

// 查询学生详情-请求参数
export interface StudentDetailParams {
  id: string | number;
}

// 查询学生详情-响应数据
export interface StudentDetailResponse {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  name: string;
  avatarUrl: string;
  code: string;
  sex: number;
  birthday: string;
  enrollmentDate: string;
  status: number;
  experienceIntroduction: string;
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  districtCode: string;
  districtName: string;
  address: string;
  hukouProvinceCode: string;
  hukouProvinceName: string;
  hukouCityCode: string;
  hukouCityName: string;
  hukouDistrictCode: string;
  hukouDistrictName: string;
  hukouAddress: string;
  familyCondition: string;
  studyIntroduction: string;
  changeCondition: string;
  gradeName: string;
  clazzName: string;
  familyRelation: any[];
  account?: string;
}

// 创建学生-请求参数
export interface StudentCreateParams {
  name: string;
  avatarUrl?: string;
  code: string;
  sex: number;
  birthday?: string;
  enrollmentDate?: string;
  experienceIntroduction?: string;
  provinceCode?: string;
  provinceName?: string;
  cityCode?: string;
  cityName?: string;
  districtCode?: string;
  districtName?: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  address?: string;
}

// 编辑学生-请求参数
export interface StudentUpdateParams {
  id: string | number;
  name: string;
  avatarUrl?: string;
  code: string;
  sex: number;
  birthday?: string;
  experienceIntroduction?: string;
  provinceCode?: string;
  provinceName?: string;
  cityCode?: string;
  cityName?: string;
  districtCode?: string;
  districtName?: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  address?: string;
}

// 学生状态变更-请求参数
export interface StudentChangeStatusParams {
  id: string | number;
  status: number; // 1正常 2失效 3毕业
}

// 删除学生-请求参数
export interface StudentDeleteParams {
  id: string | number;
}

// 学生调班-请求参数
export interface StudentChangeClazzParams {
  id: string | number;
  stageId: string | number;
  gradeId: string | number;
  clazzId: string | number;
}

// 班级树节点
export interface SchoolDeptTreeItem {
  id: string | number;
  name: string;
  parentId?: string | number;
  children?: SchoolDeptTreeItem[];
  [key: string]: any;
}

// 班级树型接口-响应数据
export interface SchoolDeptTreeResponse {
  data: SchoolDeptTreeItem[];
}

// 班级树型接口-请求参数
export interface SchoolDeptTreeParams {
  semesterId?: string | number;
}

export type SystemRegionSelectItem = {
  ancestors: string;
  children: SystemRegionSelectItem[];
  cityCode: string;
  cityName: string;
  code: string;
  districtCode: string;
  districtName: string;
  hasChildren: boolean;
  level: number;
  name: string;
  parentCode: string;
  provinceCode: string;
  provinceName: string;
  remark: string;
  sort: number;
  townCode: string;
  townName: string;
  villageCode: string;
  villageName: string;
  loading?: boolean;
  value?: string;
};

export type SystemRegionSelectType = SystemRegionSelectItem[];

export type SchoolDeptTreeType = any[];

export interface homeworkNotification {
  content: string;
  createTime: string;
  extensionParams: {
    homeworkId?: string;
    resourceId?: string;
  };
  id: string;
  title: string;
  notificationTime: string;
  senderId: number;
  readStatus: number;
  status: number;
  typeModuleId: number;
}

// 下载模板-请求参数
export interface DownloadTemplateParams {
  // 根据图片显示，body参数为空对象 {}
  [key: string]: any;
}

// 下载模板-响应数据
export interface DownloadTemplateResponse {
  // 文件流或下载链接
  data: Blob | string;
}

// 导入学生-请求参数
export interface ImportStudentParams {
  file?: File; // 根据API文档，参数为可选
}

// 导入学生-响应数据
export interface ImportStudentResponse {
  code: number;
  success: boolean;
  message?: string;
  msg?: string;
  data?: {
    studentIds?: string[]; // 导入成功的学生ID列表
    valid?: number; // 有效数据数量
    msg?: string; // 数据层消息
    errMsgs?: string[]; // 错误信息数组
    errmsgs?: string[]; // 错误信息数组（小写，兼容后端）
    [key: string]: any;
  };
  // 兼容直接返回 data 的情况
  valid?: number;
  errMsgs?: string[];
  errmsgs?: string[];
  [key: string]: any;
}

// 重置学生密码-请求参数
export interface StudentResetPasswordParams {
  id: string | number;
}
