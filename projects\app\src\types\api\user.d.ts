export type UpdateUserProps = {
  username?: string;
  avatar?: string;
  oldPassword?: string;
  password?: string;
  phone?: string;
  email?: string;
  account?: string;
  gender?: number;
};

export type ClientUserSmsCodeType = {
  bizType: number;
  mobile: string;
  code: number;
};

export type ClientUserValidType = {
  bizType: number;
  mobile: string;
  code: number;
};

export type WxBindUserLoginParams = {
  phone: string;
  unionId: string;
};

export type ResponseType = {
  code: number;
};

export type UserGetDeptType = {
  deptName: string;
  username: string;
};

export type UserDetailParams = {
  id: string;
};

export type updateUserPhoneNumParams = {
  bizType: number;
  mobile: string;
};

export type UserDetailType = {
  username: string;
  avatar: string;
  account: string;
};

export type UserDeptUserTreeType = {
  id: string;
  type: string;
  tmbId: string;
  children: UserDeptUserTreeType[];
  name: string;
};

export type SpaceTransferParams = {
  id: string;
  newTmbId: string;
};

export type ClientAuthResetPwdParams = {
  code: string;
  mobile: string;
  password: string;
  password1: string;
};

export interface CaptchaType {
  ticket: string;
  value: string;
  canvasSrc: string;
  canvasWidth: number;
  canvasHeight: number;
  blockSrc: string;
  blockWidth: number;
  blockHeight: number;
  blockRadius: number;
  blockX: number;
  blockY: number;
  place: number;
}

export interface FirstLoginUpdatePwdParams {
  password: string;
}

export interface FirstLoginUpdatePwdResponse {
  code: number;
  success: boolean;
  msg: string;
  data: boolean;
}
