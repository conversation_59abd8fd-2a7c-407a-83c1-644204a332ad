import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 资源用户信息接口
export interface ResourceUserInfo {
  username?: string;
  account?: string;
  avatar?: string;
  stageId?: number;
  subjectId?: number[]; //
}

// 资源用户信息状态管理接口
interface ResourceUserInfoState {
  resourceUserInfo: ResourceUserInfo;
  setResourceUserInfo: (info: Partial<ResourceUserInfo>) => void;
  updateStageId: (stageId: number) => void;
  updateSubjectId: (subjectId: number[]) => void;
  resetResourceUserInfo: () => void;
}

// 默认值（根据API响应格式：小学语文）
const defaultResourceUserInfo: ResourceUserInfo = {
  stageId: 2, // 小学
  subjectId: [1] // 语文（数组第一个元素）
};

// 创建资源用户信息状态管理器
export const useResourceUserInfoStore = create<ResourceUserInfoState>()(
  persist(
    (set, get) => ({
      // 初始状态
      resourceUserInfo: defaultResourceUserInfo,

      // 设置资源用户信息（支持部分更新）
      setResourceUserInfo: (info: Partial<ResourceUserInfo>) => {
        set((state) => ({
          resourceUserInfo: {
            ...state.resourceUserInfo,
            ...info
          }
        }));
      },

      updateStageId: (stageId: number) => {
        set((state) => ({
          resourceUserInfo: {
            ...state.resourceUserInfo,
            stageId
          }
        }));
      },

      // 更新学科ID
      updateSubjectId: (subjectId: number[]) => {
        set((state) => ({
          resourceUserInfo: {
            ...state.resourceUserInfo,
            subjectId
          }
        }));
      },

      // 重置为默认值
      resetResourceUserInfo: () => {
        set({ resourceUserInfo: defaultResourceUserInfo });
      }
    }),
    {
      name: 'resource-user-info-storage', // 本地存储的key
      // 可以选择性地持久化某些字段
      partialize: (state) => ({
        resourceUserInfo: state.resourceUserInfo
      })
    }
  )
);

// 导出便捷的 hooks
export const useResourceUserInfo = () => {
  const store = useResourceUserInfoStore();
  return store.resourceUserInfo;
};

export const useResourceUserInfoActions = () => {
  const store = useResourceUserInfoStore();
  return {
    setResourceUserInfo: store.setResourceUserInfo,
    updateStageId: store.updateStageId,
    updateSubjectId: store.updateSubjectId,
    resetResourceUserInfo: store.resetResourceUserInfo
  };
};
