import { PromptExternalTypeEnum } from '@/constants/api/prompt';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';

// 提示语表
export type PromptType = {
  id: string; // 提示语ID
  tenantId: string;
  tmbId: string;
  appId: string; // 应用ID
  promptTitle: string; // 标题
  description: string; // 说明
  inputContent: string; // 引导语
  proContent?: string; // 引导语专业版
  hiddenContent: string; // 隐藏提示词
  externalType: PromptExternalTypeEnum; // 外链类型
  sort: number;
  source: DataSource;
  permission: number;
  promptId: string;
  createTime: string; // 创建时间
};

export type PromptListParams = {
  source?: DataSource;
  permission?: number;
  tenantAppId: string;
  searchKey?: string;
  tenantId?: string;
  tmbId?: string;
};
export type PromptPersonalListParams = {
  source?: DataSource;
  permission?: number;
  searchKey?: string;
  tmbId?: string;
  tenantAppId: string;

  tenantId?: string;
};

export type TenantPromptCreateParams = {
  promptTitle: string; // 标题
  description?: string; // 说明
  tenantAppId: string;
  proContent?: string; // 用户输入框内容
  inputContent?: string; // 用户输入框内容
  hiddenContent?: string; // 隐藏提示词
  externalType?: PromptExternalTypeEnum;
  permission?: PermissionTypeEnum;
};

export type TenantPromptDeleteParams = {
  id: string;
};

export type TenantPromptPageParams = {
  appName?: string;
  promptTitle?: string;
  inputContent?: string;
  status?: number;
  source: DataSource;
};

export type TenantPromptPageType = {
  id: string;
  appName: string;
  createUsername: string;
  description: string;
  hiddenContent: string;
  externalType: string;
  inputContent: string;
  permission: number;
  proContent: string;
  promptTitle: string;
  source: number;
  status: number;
  tenantName: string;
  updateTime: string;
};

export type TenantPromptUpdateParams = {
  id: string;
  promptTitle: string; // 标题
  tmbId: string;
  description?: string; // 说明
  tenantAppId: string;
  proContent?: string; // 用户输入框内容
  inputContent?: string; // 用户输入框内容
  hiddenContent?: string; // 隐藏提示词
  externalType?: PromptExternalTypeEnum;
  permission?: PermissionTypeEnum;
  type?: number;
};

export type TenantPromptUpdateStatusParams = {
  status: number;
  id: string;
};
export type PromptDeleteValidParams = {
  id: string;
};
