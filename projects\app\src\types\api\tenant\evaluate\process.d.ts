import { IndactorTypeEnum, SexEnum } from '@/constants/api/tenant/evaluate/process';
import { RequestPageParams } from '@/types';
import { Indicator } from './rule';
import { EvaluateType } from '@/constants/api/tenant/evaluate/rule';

export type EvaluateSubjectType = {
  id: string;
  name: string;
};

export type EvaluateIndactorType = {
  indactorType: IndactorTypeEnum;
  subs?: EvaluateIndactorType[];
} & Indicator;

export interface indactorListType {
  id: string;
  indactorName: string;
  score: number;
}

export type ClazzEvaluateStatType = {
  studentId?: string;
  indactorId?: string;
  indactorName: string;
  iconFile?: iconFileParams;
  score: number;
};
type iconFileParams = {
  fileUrl: string;
};

export type ClazzEvaluateRecordType = {
  id: string;
  indactorId: string;
  indactorName: string;
  studentId: string;
  studentName: string;
  evaluateType: EvaluateType;
  evaluatorId?: string;
  evaluatorName?: string;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
  remark?: string;
  createTime?: string;
  iconFile?: iconFileParams;
  scores?: scoresListParams[];
};

export type scoresListParams = {
  indactorName: string;
  score: number;
};

export type HomeworkEvaluateStatType = {
  homeworkId?: string;
  studentId?: string;
  indactorId?: string;
  evaluateType: EvaluateType;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
};

export type EvaluateHomeworkType = {
  id: string;
  homeworkName: string;
  clazzId?: string;
  num?: number;
  semesterId?: string;
  subjectId?: string;
  tenantId?: string;
  year?: string;
  term?: number;
  updateTime?: string;
};

export type EvaluateClazzType = {
  id: string;
  deptName: string;
  parentId?: string;
  parentName?: string;
  teacherNames?: string;
  teachers?: { id: string }[];
  studentNum?: number;
  clazzEvaluaStatises?: ClazzEvaluateStatType[];
  homeworkNum?: number;
  homeworkEvaluaStatises?: HomeworkEvaluateStatType[];
  sort?: number;

  // 以下为前端补充字段
  clazzName: string;
  good?: string;
  bad?: string;
  score?: string;
};

export type EvaluateStudentType = {
  id: string;
  code: string;
  name: string;
  sex?: SexEnum;
  avatarUrl?: string;
  seatId?: string;
  rowNo?: number;
  colNo?: number;
  groupId?: string;
  clazzEvaluaStatises?: ClazzEvaluateStatType[];
  homeworkEvaluas?: HomeworkEvaluateStatType[];

  // 以下为前端补充字段
  good?: string;
  bad?: string;
  score?: string;
};

export type EvaluateGroupType = {
  id: string;
  groupName: string;
  clazzId?: string;
  gradeId?: string;
  studentNum?: number;
  studentIds?: string[];
  tenantId?: string;
  code?: string;
  evaluaStaties?: ClazzEvaluateStatType[];

  // 以下为前端补充字段
  good?: string;
  bad?: string;
  students?: EvaluateStudentType[];
};

export type GetEvaluateSubjectListProps = {
  deptId: string;
};

export type GetClazzStudentPageProps = {
  clazzId: string;
} & RequestPageParams;

export type SubmitSeatsProps = {
  clazzId: string;
  clazzSeats: {
    id?: string;
    studentId: string;
    rowNo: number;
    colNo: number;
  }[];
};

export type GetAllGroupListProps = {
  clazzId: string;
};

export type GetGroupListProps = {
  clazzId: string;
  subjectId?: string;
};

export type SubmitGroupsProps = {
  clazzId: string;
  groups: {
    id?: string;
    groupName: string;
    studentIds?: string[];
  }[];
};

export type GetClazzStudentListByCodeProps = {
  clazzId: string;
  subjectId?: string;
};

export type GetClazzStudentListBySeatProps = {
  clazzId: string;
  subjectId?: string;
};

export type GetClazzStudentListByGroupProps = {
  clazzId: string;
  groupIds: string[];
};

export type GetClazzIndactorListProps = {
  gradeId: string;
  clazzId: string;
  subjectId: string;
};

export type AddClazzEvaluateProps = {
  studentIds: string[];
  clazzId: string;
  subjectId: string;
  indactorId: string;
  evaluateType: EvaluateType;
  score: number;
};

export type RemoveClazzEvaluateProps = {
  id?: string;
  ids?: string[];
};

export type ResetClazzEvaluateProps = {
  studentIds: string[];
  gradeId: string;
  clazzId: string;
  subjectId: string;
};

export type GetClazzEvaluateStatsProps = {
  clazzId: string;
  subjectId?: string;
  studentId?: string;
  startTime?: string;
  endTime?: string;
};

export type GetClazzEvaluateIndactorStatListProps = {
  clazzId: string;
  subjectId?: string;
  studentId?: string;
  startTime?: string;
  endTime?: string;
};

export type GetClazzEvaluateRecordPageProps = {
  clazzId: string;
  subjectId?: string;
  studentId?: string;
  startTime?: string;
  endTime?: string;
  indactorIds?: string[];
} & RequestPageParams;

export type GetClazzEvaluateRankListProps = {
  clazzId: string;
  subjectId?: string;
  startTime?: string;
  endTime?: string;
};

export type AddHomeworkProps = {
  clazzId: string;
  subjectId: string;
  homeworkName: string;
};

export type UpdateHomeworkProps = {
  id: string;
  clazzId: string;
  subjectId: string;
  homeworkName: string;
};

export type RemoveHomeworkProps = {
  id: string;
};

export type GetHomeworkPageProps = {
  clazzId?: string;
  subjectId?: string;
} & RequestPageParams;

export type GetHomeworkListProps = {
  clazzId?: string;
  subjectId?: string;
};

export type GetHomeworkStudentsListByClazzProps = {
  clazzId: string;
};

export type GetHomeworkStudentListByCodeProps = {
  clazzId: string;
  homeworkId: string;
};

export type GetHomeworkStudentListBySeatProps = {
  clazzId: string;
  homeworkId: string;
};

export type GetHomeworkIndactorListProps = {
  gradeId: string;
  clazzId: string;
  subjectId: string;
};

export type AddHomeworkEvaluateProps = {
  clazzId: string;
  subjectId: string;
  homeworkId: string;
  indactorId: string;
  evaluateType: EvaluateType;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
  studentIds: string[];
};

export type RemoveHomeworkEvaluateProps = {
  id?: string;
  ids?: string[];
};

export type ResetHomeworkEvaluateProps = {
  gradeId: string;
  clazzId: string;
  subjectId: string;
  homeworkId: string;
  studentIds: string[];
};

export type GetHomeworkRecordListByHomeworkIdProps = {
  homeworkId: string;
};

export type GetHomeworkRecordListByStudentIdProps = {
  studentId: string;
  subjectId?: string;
};
