import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Box, Button, Flex, Text } from '@chakra-ui/react';
import { Table, Empty } from 'antd';
import { ColumnType } from 'antd/es/table';
import { Toast } from '@/utils/ui/toast';
import { getSubjectList } from '@/api/tenant/teamManagement/subjectConfiguration';
import EditModal from './EditModal';

interface SubjectConfigurationProps {
  semesterId: string;
  refreshList: () => void;
  semesterData: {
    year: string;
    type: 1 | 2;
  };
  onBack?: () => void; // 添加返回回调
}

// 本地显示用的数据结构
interface DisplaySubjectItem {
  id: string;
  stage: string; // 学段
  grade: string; // 年级
  subjects: string; // 科目
}

const SubjectConfiguration: React.FC<SubjectConfigurationProps> = ({
  semesterId,
  refreshList,
  semesterData,
  onBack
}) => {
  const [loading, setLoading] = useState(false);
  const [subjectData, setSubjectData] = useState<DisplaySubjectItem[]>([]);

  // 编辑弹窗相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<DisplaySubjectItem | null>(null);

  // 模拟数据 - 根据图片内容
  const mockData = useMemo<DisplaySubjectItem[]>(
    () => [
      { id: '1', stage: '小学', grade: '二年级', subjects: '语文、数学、英语、科学' },
      { id: '2', stage: '小学', grade: '三年级', subjects: '语文、数学、英语、科学' },
      { id: '3', stage: '小学', grade: '四年级', subjects: '语文、数学、英语、科学' },
      { id: '4', stage: '小学', grade: '二年级', subjects: '语文、数学、英语、科学' },
      { id: '5', stage: '小学', grade: '三年级', subjects: '语文、数学、英语、科学' },
      { id: '6', stage: '小学', grade: '四年级', subjects: '语文、数学、英语、科学' },
      { id: '7', stage: '初中', grade: '七年级', subjects: '语文、数学、英语、化学' },
      { id: '8', stage: '初中', grade: '七年级', subjects: '语文、数学、英语、化学' },
      { id: '9', stage: '初中', grade: '八年级', subjects: '语文、数学、英语、化学' }
    ],
    []
  );

  // 获取学科配置数据
  const fetchSubjectConfig = useCallback(async () => {
    if (!semesterId) return;

    try {
      setLoading(true);
      // 调用真实API
      const response = await getSubjectList({ semesterId: parseInt(semesterId) });

      // 将API数据转换为显示格式
      const displayData: DisplaySubjectItem[] = [];
      response.forEach((item) => {
        // 根据stageType转换学段名称
        const stageName =
          item.stageType === '2'
            ? '小学'
            : item.stageType === '3'
              ? '初中'
              : item.stageType === '4'
                ? '高中'
                : '未知';

        const subjectNames = item.subjectNames.join('、');
        displayData.push({
          id: item.gradeId,
          stage: stageName,
          grade: item.deptName,
          subjects: subjectNames
        });
      });

      setSubjectData(displayData);
    } catch (error) {
      console.error('获取学科配置数据失败:', error);
      Toast.error('获取学科配置数据失败');
      // 如果API调用失败，使用模拟数据
      setSubjectData(mockData);
    } finally {
      setLoading(false);
    }
  }, [semesterId, mockData]);

  // 初始化和学期ID变化时获取数据
  useEffect(() => {
    fetchSubjectConfig();
  }, [semesterId, fetchSubjectConfig]);

  // 处理编辑按钮点击
  const handleEdit = (record: DisplaySubjectItem) => {
    setCurrentEditItem(record);
    setEditModalVisible(true);
  };

  // 处理弹窗取消
  const handleModalCancel = () => {
    setEditModalVisible(false);
    setCurrentEditItem(null);
  };

  // 处理弹窗确认
  const handleModalConfirm = async () => {
    // API调用已经在EditModal内部处理，这里只需要处理弹窗关闭和数据刷新
    setEditModalVisible(false);
    setCurrentEditItem(null);
    fetchSubjectConfig();
    refreshList();
  };

  // 表格列定义
  const columns: ColumnType<DisplaySubjectItem>[] = [
    {
      title: '学段',
      dataIndex: 'stage',
      key: 'stage',
      width: 120
    },
    {
      title: '年级',
      dataIndex: 'grade',
      key: 'grade',
      width: 150
    },
    {
      title: '科目',
      dataIndex: 'subjects',
      key: 'subjects',
      width: 300
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          size="sm"
          variant="outline"
          colorScheme="primary"
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>
      )
    }
  ];

  if (loading && subjectData.length === 0) {
    return (
      <Box w="100%" textAlign="center" py="50px">
        正在加载数据...
      </Box>
    );
  }

  if (subjectData.length === 0) {
    return (
      <Box w="100%" textAlign="center" py="50px">
        <Empty description="暂无学科配置数据" />
        <Text mt={4}>
          {semesterData.year && semesterData.type
            ? `当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂无学科配置数据。`
            : '请选择学期后进行操作。'}
        </Text>
      </Box>
    );
  }

  return (
    <Box>
      {/* 顶部操作按钮 */}
      <Flex justify="space-between" align="center" mb={4} px={4}>
        <Text fontSize="16px" fontWeight="600" color="#333">
          学科配置
        </Text>
        <Flex gap="12px">
          <Button variant="outline" onClick={onBack} colorScheme="primary">
            返回
          </Button>
        </Flex>
      </Flex>

      {/* 表格 */}
      <Box px={4} pb={4}>
        <Table
          dataSource={subjectData}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={false}
          bordered
          style={{
            backgroundColor: 'white',
            overflow: 'hidden'
          }}
          components={{
            header: {
              cell: (props: any) => (
                <th
                  {...props}
                  style={{
                    ...props.style
                    // backgroundColor: '#f8f9fa',
                    // color: '#333',
                    // fontWeight: '500',
                    // fontSize: '14px',
                    // padding: '12px 16px',
                    // borderBottom: '1px solid #e5e7eb'
                  }}
                />
              )
            },
            body: {
              cell: (props: any) => (
                <td
                  {...props}
                  style={{
                    ...props.style
                    // padding: '12px 16px',
                    // fontSize: '14px',
                    // color: '#333',
                    // borderBottom: '1px solid #f0f0f0'
                  }}
                />
              )
            }
          }}
        />
      </Box>

      {/* 编辑弹窗 */}
      <EditModal
        visible={editModalVisible}
        onCancel={handleModalCancel}
        onConfirm={handleModalConfirm}
        title={currentEditItem ? `${currentEditItem.grade}学科` : '编辑学科'}
        semesterId={semesterId}
        gradeId={currentEditItem ? currentEditItem.id : ''}
      />
    </Box>
  );
};

export default SubjectConfiguration;
