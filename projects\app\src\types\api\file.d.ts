export type FileMetaType = {
  id: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileParseStatus?: number;
  fileUrl: string;
  fileJson?: string;
  createTime?: string;
  updateTime?: string;
  rowKey?: string;
  source?: 'upload' | 'cloud';
  fileContent?: string;
};

export type cloudFilePdf2txtObtainUrlType = {
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  id: string;
  fileJson: string;
};
