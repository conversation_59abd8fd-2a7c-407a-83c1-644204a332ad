import MobileDetect from 'mobile-detect';
import { theme } from '@/styles/theme';

export const mobileDetect = new MobileDetect(
  typeof window !== 'undefined' ? window.navigator.userAgent : ''
);

export const os = mobileDetect.os();

export const mobile = mobileDetect.mobile();

export const isMobile = !!mobile;

export const phone = mobileDetect.phone();

export const isPhone = !!phone;

export const tablet = mobileDetect.tablet();

export const isTablet = !!tablet;

export const mobileBreakpoint = parseInt(theme.breakpoints?.sm) || 900;
