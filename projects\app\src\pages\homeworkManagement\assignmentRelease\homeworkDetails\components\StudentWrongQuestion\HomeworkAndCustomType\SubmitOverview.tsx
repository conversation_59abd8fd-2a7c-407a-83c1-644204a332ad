import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { Box, Button, Flex, Tab, TabList, TabPanel, TabPanels, Tabs, Text } from '@chakra-ui/react';
import { Select, Avatar, Input, DatePicker, Button as AntButton, TableProps, Modal } from 'antd';
import styled from '@emotion/styled';
import { useEffect, useRef, useState, useMemo } from 'react';
import { ClockCircleOutlined } from '@ant-design/icons';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import ClassSelect from '../../ClassSelect';
import EChartsReact from 'echarts-for-react';
import { useHomeworkStore } from '@/store/useHomework';
import {
  getStudentErrorReviseStats,
  getStudentErrorWrongStats,
  getStudentErrorPage,
  getStudentErrorDistribution,
  batchRejectStudentError,
  batchAddToErrorBook,
  batchUrgeStudentSubmit
} from '@/api/homeworkDetail';
import type {
  StudentErrorReviseStatsResponse,
  StudentErrorWrongStatsResponse,
  StudentErrorPageRequest,
  StudentErrorPageResponse,
  StudentErrorRecord,
  ErrorDistributionResponse,
  LearningAnalysisRequest
} from '@/types/api/homeworkDetail';
import type { PagingData } from '@/types';
import { Toast } from '@/utils/ui/toast';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import RejectRevisionModal from '@/components/RejectRevisionModal';

// MyTable不需要自定义样式，因为它有内置的样式处理

const SubmitCard = styled(Box)<{ width?: string; height?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: ${({ width }) => width || vwDims(146)};
  height: ${({ height }) => height || vwDims(86)};
  border: 1px solid #e7deff;
  border-radius: 14px;
  background: #fff;
`;

interface SubmitCardItemProps {
  count: number;
  label: string;
  width?: string;
  height?: string;
  suffix?: string;
}

const SubmitCardItem = ({ count, label, width, height, suffix = '个' }: SubmitCardItemProps) => {
  return (
    <SubmitCard width={width} height={height}>
      <Box display={'flex'} alignItems={'baseline'} justifyContent={'center'}>
        <Text fontSize={vwDims(30)} fontWeight={'500'} color={'#303133'}>
          {count}
        </Text>
        <Text>{suffix}</Text>
      </Box>
      <Box textAlign={'center'}>
        <Text>{label}</Text>
      </Box>
    </SubmitCard>
  );
};

interface CompletionCardProps {
  minutes: number;
  time: number;
  label: string;
}

// 完成情况卡片
const CompletionCard = (props: CompletionCardProps) => {
  const { minutes, time, label } = props;
  return (
    <SubmitCard width={vwDims(202)} height={vwDims(86)}>
      <Box display={'flex'} alignItems={'baseline'} justifyContent={'center'}>
        <Text fontSize={vwDims(30)} fontWeight={'500'} color={'#303133'}>
          {minutes}
        </Text>
        <Text>分</Text>
        <Text fontSize={vwDims(30)} fontWeight={'500'} color={'#303133'}>
          {time}
        </Text>
        <Text>秒</Text>
      </Box>
      <Box textAlign={'center'}>
        <Text>{label}</Text>
      </Box>
    </SubmitCard>
  );
};

interface ClassComparisonBarProps {
  item: {
    id: number;
    className: string;
    submissionRate: number;
  };
}
const ClassComparisonBar = ({ item }: ClassComparisonBarProps) => {
  const DESIRED_SEGMENT_WIDTH = 30; // 设计参数：期望每30px左右有一个刻度
  const [numberOfSegments, setNumberOfSegments] = useState(0);
  const progressBarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      if (entries[0]) {
        const { width } = entries[0].contentRect;
        if (width > 0) {
          const calculatedSegments = Math.max(2, Math.floor(width / DESIRED_SEGMENT_WIDTH));
          setNumberOfSegments(calculatedSegments);
        }
      }
    });

    const currentRef = progressBarRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <Flex alignItems="center">
      <Flex fontSize={vwDims(16)} fontWeight="500" color="#606266" whiteSpace="nowrap">
        {item.className}
      </Flex>
      {/* 灰色轨道背景 */}
      <Flex
        ref={progressBarRef}
        flex="0.99"
        h={vwDims(14)}
        borderRadius="30px"
        bgColor="#ECECF5"
        flexShrink="0"
        ml={vwDims(17)}
        pos="relative"
      >
        {/* 1. 紫色填充层，只负责背景和圆角 */}
        <Flex
          pos="absolute"
          top="1px"
          left="1px"
          h="calc(100% - 2px)"
          w={item.submissionRate === 100 ? 'calc(100% - 2px)' : `${item.submissionRate}%`}
          borderRadius={item.submissionRate === 100 ? '30px' : '30px 10px 10px 30px'}
          bg="linear-gradient(90deg, #707EFF 0%, #9257FF 100%)"
        />

        {/* 2. 刻度线层，总在最上层，并被精确裁剪 */}
        <Flex
          pos="absolute"
          top="1px"
          left="1px"
          right="1px"
          bottom="1px"
          clipPath={`inset(0 ${100 - item.submissionRate}% 0 0 round 30px)`}
        >
          {/* 使用独立的 div 元素渲染刻度线，以获得更清晰的渲染效果 */}
          <Flex position="relative" w="100%" h="100%">
            {numberOfSegments > 1 &&
              Array.from({ length: numberOfSegments - 1 }).map((_, i) => (
                <Box
                  key={i}
                  pos="absolute"
                  left={`calc(${((i + 1) * 100) / numberOfSegments}% - 0.5px)`}
                  top="0"
                  bottom="0"
                  w="1px"
                  bgColor="#e9e9f3"
                />
              ))}
          </Flex>
        </Flex>
      </Flex>
      <Flex ml={vwDims(16)} fontSize={vwDims(18)} fontWeight="600" color="#303133">
        {item.submissionRate}%
      </Flex>
    </Flex>
  );
};

// 按钮样式配置
const getActionButtonStyle = (variant: 'primary' | 'secondary') => {
  const baseStyle = {
    display: 'flex',
    height: vwDims(30),
    padding: vwDims(3),
    justifyContent: 'center',
    alignItems: 'center',
    gap: vwDims(6),
    borderRadius: vwDims(7),
    background: '#FFF'
  };

  const variants = {
    primary: {
      border: `1px solid #7D4DFF`,
      color: '#7D4DFF'
    },
    secondary: {
      border: `1px solid #D1D5DB`,
      color: '#4E5969'
    }
  };

  return { ...baseStyle, ...variants[variant] };
};

function SubmitOverview() {
  const { homeworkDetail } = useHomeworkStore();
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const handleChange = (value: unknown) => {};

  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedClass, setSelectedClass] = useState('全部');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [gradingClass, setGradingClass] = useState<number | null>(null); // null表示"全部"
  const [answerClass, setAnswerClass] = useState<number | null>(null);

  // API数据状态
  const [reviseStatsData, setReviseStatsData] = useState<StudentErrorReviseStatsResponse>({
    reviseCount: 0,
    completedCount: 0
  });
  const [wrongStatsData, setWrongStatsData] = useState<StudentErrorWrongStatsResponse>({
    errorStudentCount: 0,
    avgErrorCount: 0,
    avgErrorRate: 0
  });
  const [loading, setLoading] = useState(false);

  // 错题分布数据状态
  const [distributionData, setDistributionData] = useState<ErrorDistributionResponse>({
    knowledgePointErrorList: [],
    questionTypeErrorList: []
  });
  const [distributionLoading, setDistributionLoading] = useState(false);

  // MyTable相关
  const actionRef = useRef<MyTableRef<StudentErrorPageRequest, StudentErrorRecord>>(null);

  // 筛选条件状态
  const [searchKey, setSearchKey] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [reviseEndTime, setReviseEndTime] = useState<string>('');

  // 表格选中状态 (已存在，添加selectedRows)
  const [selectedRows, setSelectedRows] = useState<StudentErrorRecord[]>([]);

  // 知识点错题分布图表配置
  const knowledgePointChartOption = useMemo(() => {
    const colors = ['#4A90E2', '#50C8C8', '#F5A623', '#9013FE', '#E74C3C'];
    const xAxisData = distributionData.knowledgePointErrorList.map(
      (item) => item.knowledgePointName
    );
    const seriesData = distributionData.knowledgePointErrorList.map((item, index) => ({
      value: item.errorStudentCount,
      itemStyle: { color: colors[index % colors.length] }
    }));
    const maxValue = Math.max(
      ...distributionData.knowledgePointErrorList.map((item) => item.errorStudentCount),
      10
    );

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: { name: string; value: number }[]) {
          const data = params[0];
          return `${data.name}<br/>错题人数: ${data.value}人`;
        }
      },
      grid: { left: 30, right: 60, top: 30, bottom: 20 },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: '知识点名称',
        nameLocation: 'end',
        nameGap: 60,
        nameTextStyle: {
          fontSize: vwDims(12),
          color: '#666666',
          align: 'right',
          padding: [50, 0, 0, 0]
        },
        axisLabel: {
          fontSize: vwDims(12),
          color: '#666666',
          interval: 0,
          rotate: 0
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '错题人数',
        nameTextStyle: {
          fontSize: vwDims(12),
          color: '#666666',
          padding: [0, 0, 0, -10]
        },
        axisLabel: {
          fontSize: vwDims(12),
          color: '#666666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#F0F0F0',
            type: 'solid',
            width: 1
          }
        },
        max: Math.ceil(maxValue * 1.2)
      },
      series: [
        {
          name: '错题人数',
          type: 'bar',
          data: seriesData,
          barWidth: 32,
          label: {
            show: true,
            position: 'top',
            fontSize: vwDims(12),
            color: '#333333',
            fontWeight: '500'
          }
        }
      ]
    };
  }, [distributionData.knowledgePointErrorList]);

  // 题型错题分布图表配置
  const questionTypeChartOption = useMemo(() => {
    const colors = ['#4A90E2', '#50C8C8', '#F5A623', '#9013FE', '#E74C3C'];
    const xAxisData = distributionData.questionTypeErrorList.map((item) => item.questionTypeName);
    const seriesData = distributionData.questionTypeErrorList.map((item, index) => ({
      value: item.errorStudentCount,
      itemStyle: { color: colors[index % colors.length] }
    }));
    const maxValue = Math.max(
      ...distributionData.questionTypeErrorList.map((item) => item.errorStudentCount),
      10
    );

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: { name: string; value: number }[]) {
          const data = params[0];
          return `${data.name}<br/>错题人数: ${data.value}人`;
        }
      },
      grid: { left: 30, right: 60, top: 30, bottom: 20 },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: '题型',
        nameLocation: 'end',
        nameGap: 60,
        nameTextStyle: {
          fontSize: vwDims(12),
          color: '#666666',
          align: 'right',
          padding: [50, 0, 0, 0]
        },
        axisLabel: {
          fontSize: vwDims(12),
          color: '#666666',
          interval: 0,
          rotate: 0
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '错题人数',
        nameTextStyle: {
          fontSize: vwDims(12),
          color: '#666666',
          padding: [0, 0, 0, -10]
        },
        axisLabel: {
          fontSize: vwDims(12),
          color: '#666666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#F0F0F0',
            type: 'solid',
            width: 1
          }
        },
        max: Math.ceil(maxValue * 1.2)
      },
      series: [
        {
          name: '错题人数',
          type: 'bar',
          data: seriesData,
          barWidth: 32,
          label: {
            show: true,
            position: 'top',
            fontSize: vwDims(12),
            color: '#333333',
            fontWeight: '500'
          }
        }
      ]
    };
  }, [distributionData.questionTypeErrorList]);

  const [compareClasses, setCompareClasses] = useState<
    Array<{
      id: number;
      className: string;
      submissionRate: number;
    }>
  >([
    { id: 1, className: '一年级1班', submissionRate: 95 },
    { id: 2, className: '一年级2班', submissionRate: 88 },
    { id: 3, className: '一年级3班', submissionRate: 76 }
  ]);

  const needsExpansion = compareClasses.length > 4;

  const [dataSource, setDataSource] = useState<any[]>([
    {
      key: '1',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '熟练掌握层',
      scoreInfo: '-',
      status: 'submitted',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['查看']
    },
    {
      key: '2',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '基本掌握层',
      scoreInfo: '-',
      status: 'not_submitted',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['查看']
    },
    {
      key: '3',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '基本掌握层',
      scoreInfo: '-',
      status: 'ai_correcting',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['查看']
    },
    {
      key: '4',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '熟练掌握层',
      scoreInfo: '-',
      status: 'completed',
      submitTime: '-',
      correctionTime: '2025-06-03 10:56',
      actions: []
    },
    {
      key: '5',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '初步学习层',
      scoreInfo: '订正 219/30分',
      status: 'pending_confirm',
      submitTime: '2025-06-21 06:59',
      correctionTime: '2025-06-03 10:56',
      actions: ['查看']
    },
    {
      key: '6',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '初步学习层',
      scoreInfo: '249/30分',
      status: 'pending_confirm',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['确认', '重新批改']
    },
    {
      key: '7',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '初步学习层',
      scoreInfo: '249/30分',
      status: 'pending_confirm',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['确认', '重新批改']
    },
    {
      key: '8',
      studentId: '20250701',
      studentName: '李白',
      class: '一年级1班',
      layer: '初步学习层',
      scoreInfo: '249/30分',
      status: 'pending_confirm',
      submitTime: '2025-06-21 06:59',
      correctionTime: '-',
      actions: ['确认', '重新批改']
    }
  ]);

  const columns: TableProps<StudentErrorRecord>['columns'] = useMemo(
    () => [
      {
        title: '学生',
        key: 'student',
        width: 120,
        render: (_: unknown, record: StudentErrorRecord) => (
          <Flex alignItems="center" gap={vwDims(8)}>
            <Avatar size={32} style={{ backgroundColor: '#7D4DFF' }} src={record.avatarUrl}>
              {record.studentName?.charAt(0) || 'N'}
            </Avatar>
            <Box>
              <Text fontSize={vwDims(12)} color="#909399">
                {record.code || '-'}
              </Text>
              <Text fontSize={vwDims(14)} fontWeight="500" color="#303133">
                {record.studentName || '-'}
              </Text>
            </Box>
          </Flex>
        )
      },
      {
        title: '班级',
        key: 'class',
        width: 130,
        render: (_: unknown, record: StudentErrorRecord) => (
          <Box
            display={'inline-block'}
            fontSize={vwDims(14)}
            color="#606266"
            height={vwDims(27)}
            lineHeight={vwDims(27)}
            background={'#F2F3F5'}
            padding={`${vwDims(0)} ${vwDims(8)}`}
            borderRadius={'2px'}
          >
            {record.gradeName || '-'}
            {record.clazzName || '-'}
          </Box>
        )
      },
      // 只有当taskType为2时才显示分层列
      ...(homeworkDetail?.taskType === 2
        ? [
            {
              title: '分层',
              key: 'layer',
              width: 120,
              render: (_: unknown, record: StudentErrorRecord) => {
                const levelMap = {
                  1: '熟练掌握层',
                  2: '基本掌握层',
                  3: '初步学习层'
                };
                const levelText = levelMap[record.level as keyof typeof levelMap] || '-';
                return <Box>{levelText}</Box>;
              }
            }
          ]
        : []),
      {
        title: '已归纳错题/答错题数',
        key: 'scoreInfo',
        render: (_: unknown, record: StudentErrorRecord) => {
          return (
            <Box>
              <Flex gap={vwDims(4)}>
                <Text fontSize={vwDims(14)} color="#7D4DFF" fontWeight="500">
                  {record.errorCount || 0}
                </Text>
                <Text fontSize={vwDims(14)} color="#303133" fontWeight="500">
                  /{record.wrongCount || 0}题
                </Text>
              </Flex>
            </Box>
          );
        }
      },
      {
        title: '状态',
        key: 'status',
        width: 120,
        render: (_: unknown, record: StudentErrorRecord) => {
          const statusMap = {
            0: { text: '待提交', background: '#F2F3F5', color: '#4E5969' },
            1: { text: '未提交', background: '#FFEFEF', color: '#F53F3F' },
            2: { text: '待批改', background: '#FFF6E3', color: '#B77D00' },
            3: { text: '批改中', background: '#F3F0FF', color: '#7D4DFF' },
            4: { text: '待确认', background: '#FFF6E3', color: '#B77D00' },
            5: { text: '已完成', background: '#E8FFEA', color: '#00B42A' },
            6: { text: '待订正', background: '#EFF4FF', color: '#1F7AFF' },
            7: { text: '批改失败', background: '#FFEFEF', color: '#F53F3F' }
          };
          const status = statusMap[record.status as keyof typeof statusMap] || statusMap[0];
          return (
            <Box
              display="inline-flex"
              padding="1px 8px"
              justifyContent="center"
              alignItems="center"
              borderRadius="50px"
              background={status.background}
              fontSize={vwDims(14)}
              fontWeight="500"
              width="fit-content"
            >
              <Box color={status.color}>
                <span style={{ marginRight: '4px' }}>•</span>
                {status.text}
              </Box>
            </Box>
          );
        }
      },
      {
        title: '订正完成时间',
        key: 'reviseTime',
        width: 140,
        render: (_: unknown, record: StudentErrorRecord) => (
          <Text fontSize={vwDims(14)} color="#606266">
            {record.reviseTime || '-'}
          </Text>
        )
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_: unknown, record: StudentErrorRecord) => {
          // 根据订正状态显示不同的操作按钮
          // reviseStatus: 0-无状态，1-待订正，2-已完成
          const isWaitingRevise = record.reviseStatus === 1; // 待订正
          const isCompletedOrNoStatus = record.reviseStatus === 0 || record.reviseStatus === 2; // 无状态/已完成

          return (
            <Flex gap={vwDims(8)}>
              {/* 查看按钮 - 所有状态都显示 */}
              <Button
                variant={'outline'}
                sx={getActionButtonStyle('secondary')}
                onClick={() => handleView(record)}
              >
                查看
              </Button>

              {/* 待订正状态显示催交按钮 */}
              {isWaitingRevise && (
                <Button
                  variant={'outline'}
                  sx={getActionButtonStyle('primary')}
                  onClick={() => handleUrgeSubmit(record)}
                >
                  催交
                </Button>
              )}

              {/* 无状态/已完成状态显示打回订正和归纳至错题本按钮 */}
              {isCompletedOrNoStatus && (
                <>
                  <Button
                    variant={'outline'}
                    sx={getActionButtonStyle('primary')}
                    onClick={() => handleRejectRevision(record)}
                  >
                    打回订正
                  </Button>
                  <Button
                    variant={'outline'}
                    sx={getActionButtonStyle('primary')}
                    onClick={() => handleAddToErrorBook(record)}
                  >
                    归纳至错题本
                  </Button>
                </>
              )}
            </Flex>
          );
        }
      }
    ],
    [homeworkDetail?.taskType]
  );

  // 获取错题占比数据
  const fetchWrongStats = async () => {
    if (!homeworkDetail?.id) return;

    try {
      const response = await getStudentErrorWrongStats({
        homeworkId: homeworkDetail.id,
        clazzId: gradingClass,
        level: homeworkDetail.taskType === 2 ? 3 : null
      });
      setWrongStatsData(response);
    } catch (error) {}
  };

  // 获取订正情况数据
  const fetchReviseStats = async () => {
    if (!homeworkDetail?.id) return;

    try {
      const response = await getStudentErrorReviseStats({
        homeworkId: homeworkDetail.id,
        clazzId: answerClass,
        level: homeworkDetail.taskType === 2 ? 3 : null
      });
      setReviseStatsData(response);
    } catch (error) {}
  };

  // 获取错题分布数据
  const fetchDistributionData = async () => {
    if (!homeworkDetail?.id) return;

    try {
      setDistributionLoading(true);
      const params: LearningAnalysisRequest = {
        homeworkId: homeworkDetail.id,
        level: homeworkDetail.taskType === 2 ? 3 : null
      };
      const response = await getStudentErrorDistribution(params);
      setDistributionData(response);
    } catch (error) {
    } finally {
      setDistributionLoading(false);
    }
  };

  // 班级选择变化处理函数
  const handleGradingClassChange = (value: unknown) => {
    const classId = value as number | null;
    setGradingClass(classId);
  };

  const handleAnswerClassChange = (value: unknown) => {
    const classId = value as number | null;
    setAnswerClass(classId);
  };

  // 操作函数
  const handleView = (record: StudentErrorRecord) => {};

  const handleRejectRevision = async (record: StudentErrorRecord) => {
    if (!record.submitId || !record.code || !record.studentName) {
      Toast.error('学生信息不完整');
      return;
    }

    openOverlay({
      Overlay: RejectRevisionModal,
      props: {
        students: [
          {
            code: record.code,
            studentName: record.studentName,
            submitId: record.submitId
          }
        ],
        isBatch: false,
        onClose: () => {},
        onConfirm: async (params) => {
          try {
            await batchRejectStudentError(params);
            Toast.success('打回订正成功');
            actionRef.current?.reload();
          } catch (error) {
            Toast.error('打回订正失败');
          }
        }
      }
    });
  };

  const handleAddToErrorBook = async (record: StudentErrorRecord) => {
    Modal.confirm({
      title: '归纳错题',
      content: '归纳后，学生错题目将直接接入错题本，无需订正，确认一键归纳错题？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await batchAddToErrorBook({ submitIds: [record.submitId!] });
          Toast.success('操作成功');
          actionRef.current?.reload();
        } catch (error) {
          Toast.error('操作失败');
        }
      }
    });
  };

  const handleUrgeSubmit = async (record: StudentErrorRecord) => {
    try {
      await batchUrgeStudentSubmit({ ids: [record.id!] });
      Toast.success(`操作成功`);
      actionRef.current?.reload();
    } catch (error) {
      Toast.error('催交失败');
    }
  };

  // MyTable API函数
  const fetchStudentErrorListApi = async (
    params: StudentErrorPageRequest
  ): Promise<PagingData<StudentErrorRecord>> => {
    if (!homeworkDetail?.id) {
      return {
        records: [],
        total: 0,
        size: params.size || 10,
        current: params.current || 1,
        pages: 0
      };
    }

    // 获取选中班级的ID
    let clazzId = 0;
    if (selectedClass !== '全部' && homeworkDetail.clazzList) {
      const targetClass = homeworkDetail.clazzList.find(
        (item: { gradeName: string; clazzName: string; clazzId: number }) =>
          `${item.gradeName}${item.clazzName}` === selectedClass
      );
      clazzId = targetClass?.clazzId || 0;
    }

    const requestParams: StudentErrorPageRequest = {
      ...params,
      homeworkId: homeworkDetail.id,
      clazzId,
      level: homeworkDetail.taskType === 2 ? selectedLevel : null,
      status: selectedStatus !== null ? selectedStatus : null,
      searchKey,
      reviseEndTime
    };
    const response = await getStudentErrorPage(requestParams);
    // 将API返回的数据转换为MyTable期望的格式
    return {
      records: response.records as StudentErrorRecord[],
      total: response.total,
      current: response.current,
      size: response.size,
      pages: response.pages
    };
  };

  // 判断学生是否可以进行批量打回订正/归纳至错题本操作
  const canBatchRejectOrAddToErrorBook = (record: StudentErrorRecord) => {
    // reviseStatus: 0-无状态，2-已完成
    return record.reviseStatus === 0 || record.reviseStatus === 2;
  };

  // 判断学生是否可以进行批量催交操作
  const canBatchUrge = (record: StudentErrorRecord) => {
    // reviseStatus: 1-待订正
    return record.reviseStatus === 1;
  };

  // 批量打回订正
  const handleBatchRejectRevision = async () => {
    if (selectedRows.length === 0) {
      Toast.error('请先选择学生');
      return;
    }

    // 筛选出符合条件的学生（无状态或已完成）
    const validRows = selectedRows.filter(canBatchRejectOrAddToErrorBook);
    const invalidRows = selectedRows.filter((row) => !canBatchRejectOrAddToErrorBook(row));

    if (validRows.length === 0) {
      Toast.error('请选择无状态或已完成状态的学生');
      return;
    }

    if (invalidRows.length > 0) {
      Toast.warning(
        `已过滤掉${invalidRows.length}名不符合条件的学生，将对${validRows.length}名学生执行打回订正操作`
      );
    }

    // 准备学生信息
    const studentList = validRows
      .filter((row) => row.submitId && row.code && row.studentName && row.id)
      .map((row) => ({
        code: row.code!,
        studentName: row.studentName!,
        submitId: row.submitId!,
        id: row.id!
      }));

    if (studentList.length === 0) {
      Toast.error('选中学生信息不完整');
      return;
    }

    openOverlay({
      Overlay: RejectRevisionModal,
      props: {
        students: studentList,
        isBatch: true,
        onClose: () => {},
        onConfirm: async (params) => {
          try {
            await batchRejectStudentError(params);
            Toast.success(`操作成功`);
            actionRef.current?.reload();
            setSelectedRowKeys([]);
            setSelectedRows([]);
          } catch (error) {
            Toast.error('批量打回订正失败');
          }
        }
      }
    });
  };

  // 批量归纳至错题本
  const handleBatchAddToErrorBook = async () => {
    if (selectedRows.length === 0) {
      Toast.error('请先选择学生');
      return;
    }

    // 筛选出符合条件的学生（无状态或已完成）
    const validRows = selectedRows.filter(canBatchRejectOrAddToErrorBook);
    const invalidRows = selectedRows.filter((row) => !canBatchRejectOrAddToErrorBook(row));

    if (validRows.length === 0) {
      Toast.error('请选择无状态或已完成状态的学生');
      return;
    }

    let content = '归纳后，学生错题目将直接接入错题本，无需订正，确认一键归纳错题？';
    if (invalidRows.length > 0) {
      content = `已过滤掉${invalidRows.length}名不符合条件的学生，将对${validRows.length}名符合条件的学生执行归纳操作。\n\n归纳后，学生错题目将直接接入错题本，无需订正，确认一键归纳错题？`;
    }

    Modal.confirm({
      title: '归纳错题',
      content,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const submitIds = validRows.map((row) => row.submitId!).filter((id) => id);
          await batchAddToErrorBook({ submitIds });
          Toast.success(`操作成功`);
          actionRef.current?.reload();
          setSelectedRowKeys([]);
          setSelectedRows([]);
        } catch (error) {
          Toast.error('批量归纳至错题本失败');
        }
      }
    });
  };

  // 批量催交
  const handleBatchUrgeSubmit = async () => {
    if (selectedRows.length === 0) {
      Toast.error('请先选择学生');
      return;
    }

    // 筛选出符合条件的学生（待订正状态）
    const validRows = selectedRows.filter(canBatchUrge);
    const invalidRows = selectedRows.filter((row) => !canBatchUrge(row));

    if (validRows.length === 0) {
      Toast.error('请选择待订正状态的学生');
      return;
    }

    if (invalidRows.length > 0) {
      Toast.warning(
        `已过滤掉${invalidRows.length}名不符合条件的学生，将对${validRows.length}名学生发送催交通知`
      );
    }

    try {
      const ids = validRows.map((row) => row.id!).filter((id) => id);
      await batchUrgeStudentSubmit({ ids });
      Toast.success(`操作成功`);
      actionRef.current?.reload();
    } catch (error) {
      Toast.error('批量催交失败');
    }
  };

  // 数据初始化和条件变化时重新获取数据
  useEffect(() => {
    fetchWrongStats();
  }, [homeworkDetail?.id, gradingClass]);

  useEffect(() => {
    fetchReviseStats();
  }, [homeworkDetail?.id, answerClass]);

  useEffect(() => {
    fetchDistributionData();
  }, [homeworkDetail?.id]);

  // 当筛选条件变化时重新加载数据
  useEffect(() => {
    actionRef.current?.reload();
  }, [selectedClass, selectedLevel, selectedStatus, searchKey, reviseEndTime]);

  // 批量重新修改（打回订正）
  const handleBatchReModify = () => {
    handleBatchRejectRevision();
  };
  // 批量确认（归纳至错题本）
  const handleBatchConfirm = () => {
    if (selectedRowKeys.length === 0) {
      Toast.error('请选择学生');
      return;
    }
    handleBatchAddToErrorBook();
  };
  // 批量催交
  const handleBatchUrge = () => {
    handleBatchUrgeSubmit();
  };

  return (
    <Box w="100%" h="100%" paddingBottom={vwDims(45)}>
      <Box display={'flex'} h={vwDims(342)} borderRadius={'8px'} mt={vwDims(28)} gap={vwDims(14)}>
        {/* 提交情况  作业批改-智慧作业/自定义作业-线上答题提交/拍照提交 和 作业批改-分层作业-提交情况*/}
        <Box display={'none'} flexDirection={'column'} gap={vwDims(14)}>
          <Box
            flex={1}
            padding={`${vwDims(16)} ${vwDims(25)}`}
            backgroundColor={'#F6F5FF'}
            borderRadius={'20px'}
          >
            <Box
              fontSize={vwDims(15)}
              fontWeight="500"
              color="#303133"
              mb={vwDims(8)}
              display={'flex'}
              alignItems={'center'}
            >
              <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
              提交情况
              <ClassSelect onChange={handleChange} />
            </Box>

            <Box display={'flex'} gap={vwDims(20)} color={'#606266'}>
              <SubmitCardItem count={68} label="应提交" />
              <SubmitCardItem count={68} label="已提交" />
              <SubmitCardItem count={68} label="待提交" />
              <SubmitCardItem count={68} label="未提交" />
            </Box>
          </Box>
          <Box
            flex={1}
            padding={`${vwDims(16)} ${vwDims(25)}`}
            backgroundColor={'#F6F5FF'}
            borderRadius={'20px'}
          >
            <Box
              fontSize={vwDims(15)}
              fontWeight="500"
              color="#303133"
              mb={vwDims(8)}
              display={'flex'}
              alignItems={'center'}
            >
              <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
              完成情况
              <ClassSelect onChange={handleChange} />
            </Box>

            <Box display={'flex'} gap={vwDims(20)} color={'#606266'}>
              <CompletionCard minutes={10} time={10} label="平均完成时长" />
              <CompletionCard minutes={10} time={10} label="最长完成时长" />
              <CompletionCard minutes={10} time={10} label="最短完成时长" />
            </Box>
          </Box>
        </Box>

        <Box display={'flex'} flexDirection={'column'} gap={vwDims(14)}>
          <Box
            flex={1}
            padding={`${vwDims(16)} ${vwDims(25)}`}
            backgroundColor={'#F6F5FF'}
            borderRadius={'20px'}
          >
            <Box
              fontSize={vwDims(15)}
              fontWeight="500"
              color="#303133"
              mb={vwDims(8)}
              display={'flex'}
              alignItems={'center'}
            >
              <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
              错题占比
              <ClassSelect value={gradingClass} onChange={handleGradingClassChange} />
            </Box>

            <Box display={'flex'} gap={vwDims(20)} color={'#606266'}>
              <SubmitCardItem
                count={wrongStatsData.errorStudentCount}
                width={vwDims(180)}
                suffix="人"
                label="错题人数"
              />
              <SubmitCardItem
                count={wrongStatsData.avgErrorCount}
                width={vwDims(180)}
                suffix="道"
                label="平均错题数"
              />
              <SubmitCardItem
                width={vwDims(180)}
                count={wrongStatsData.avgErrorRate ? wrongStatsData.avgErrorRate / 100 : 0}
                label="平均答错率"
                suffix="%"
              />
            </Box>
          </Box>
          <Box
            flex={1}
            padding={`${vwDims(16)} ${vwDims(25)}`}
            backgroundColor={'#F6F5FF'}
            borderRadius={'20px'}
          >
            <Box
              fontSize={vwDims(15)}
              fontWeight="500"
              color="#303133"
              mb={vwDims(8)}
              display={'flex'}
              alignItems={'center'}
            >
              <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
              订正情况
              <ClassSelect value={answerClass} onChange={handleAnswerClassChange} />
            </Box>

            <Box display={'flex'} gap={vwDims(20)} color={'#606266'}>
              <SubmitCardItem
                count={reviseStatsData.reviseCount}
                width={vwDims(280)}
                suffix="人"
                label="待订正"
              />
              <SubmitCardItem
                count={reviseStatsData.completedCount}
                width={vwDims(280)}
                suffix="人"
                label="已完成"
              />
            </Box>
          </Box>
        </Box>

        {/* 班级对比 */}
        <Box background={'#F8FBFF'} borderRadius={'20px'} w={vwDims(634)}>
          <Box
            height={vwDims(342)}
            borderRadius={'20px'}
            border={'1px solid #F3F3F3'}
            background={'#FFF'}
            boxShadow={'0px 0px 16.3px 0px rgba(0, 0, 0, 0.07)'}
            padding={`${vwDims(24)}`}
          >
            <Tabs display="flex" flexDirection="column" height="100%">
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={vwDims(20)}
                flexShrink={0}
              >
                <Text fontSize={vwDims(18)} fontWeight="600" color="#303133">
                  错题分布
                </Text>
                <TabList border="none">
                  <Tab
                    fontSize={vwDims(14)}
                    color="#909399"
                    _selected={{
                      color: '#7D4DFF',
                      borderBottom: '2px solid #7D4DFF'
                    }}
                    _focus={{ boxShadow: 'none' }}
                    px={vwDims(16)}
                  >
                    知识点错题分布
                  </Tab>
                  <Tab
                    fontSize={vwDims(14)}
                    color="#909399"
                    _selected={{
                      color: '#7D4DFF',
                      borderBottom: '2px solid #7D4DFF'
                    }}
                    _focus={{ boxShadow: 'none' }}
                    px={vwDims(16)}
                  >
                    题型错题分布
                  </Tab>
                </TabList>
              </Box>

              <TabPanels flex={1} display="flex" flexDirection="column">
                <TabPanel p={0} flex={1} display="flex" flexDirection="column">
                  {distributionLoading ? (
                    <Box
                      height={vwDims(240)}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      color="#666666"
                    >
                      加载中...
                    </Box>
                  ) : distributionData.knowledgePointErrorList.length === 0 ? (
                    <Box
                      height={vwDims(240)}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      color="#666666"
                    >
                      暂无知识点错题数据
                    </Box>
                  ) : (
                    <EChartsReact
                      option={knowledgePointChartOption}
                      style={{ height: vwDims(240) }}
                    />
                  )}
                </TabPanel>

                <TabPanel p={0} flex={1} display="flex" flexDirection="column">
                  {distributionLoading ? (
                    <Box
                      height={vwDims(240)}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      color="#666666"
                    >
                      加载中...
                    </Box>
                  ) : distributionData.questionTypeErrorList.length === 0 ? (
                    <Box
                      height={vwDims(240)}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                      color="#666666"
                    >
                      暂无题型错题数据
                    </Box>
                  ) : (
                    <EChartsReact
                      option={questionTypeChartOption}
                      style={{ height: vwDims(240) }}
                    />
                  )}
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>
        </Box>
      </Box>
      {/* 错题详情 */}
      <Box mt={vwDims(45)}>
        <Box mb={vwDims(13)} display={'flex'} alignItems={'center'}>
          <Box fontSize={vwDims(18)} fontWeight={500} color={'#1D2129'}>
            错题详情
          </Box>
          <Box ml={'auto'} display={'flex'} gap={vwDims(10)}>
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              onClick={handleBatchUrge}
            >
              批量催交
            </Button>
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              onClick={handleBatchConfirm}
            >
              批量归纳至错题本
            </Button>
            <Button colorScheme={'#7D4DFF'} onClick={handleBatchReModify}>
              批量打回订正
            </Button>
          </Box>
        </Box>

        <Box
          h={vwDims(567)}
          borderRadius={'10px'}
          border={'1px solid #e7e7e7'}
          padding={`${vwDims(20)} ${vwDims(16)} ${vwDims(20)}`}
        >
          <Box>
            {/* 班级选择标签 */}
            <Box display="flex" gap={vwDims(12)} mb={vwDims(20)}>
              {[
                { label: '全部', value: '全部' },
                ...(homeworkDetail?.clazzList?.map(
                  (item: { gradeName: string; clazzName: string; clazzId: number }) => ({
                    label: `${item.gradeName}${item.clazzName}`,
                    value: `${item.gradeName}${item.clazzName}`
                  })
                ) || [])
              ].map((classItem) => (
                <Box
                  minW={vwDims(102)}
                  key={classItem.value}
                  padding={`${vwDims(7)} ${vwDims(20)}`}
                  borderRadius="16px"
                  border={
                    selectedClass === classItem.value ? '1px solid #7D4DFF' : '1px solid #E5E7EB'
                  }
                  background="#FFF"
                  height={vwDims(32)}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  cursor="pointer"
                  fontSize={vwDims(14)}
                  fontWeight="500"
                  color={selectedClass === classItem.value ? '#7D4DFF' : '#606266'}
                  onClick={() => setSelectedClass(classItem.value)}
                  _hover={{
                    borderColor: '#7D4DFF',
                    color: '#7D4DFF'
                  }}
                >
                  {classItem.label}
                </Box>
              ))}
            </Box>
          </Box>

          <Box>
            {/* 筛选条件行 */}
            <Flex
              mb={vwDims(16)}
              alignItems="center"
              gap={vwDims(16)}
              color={'#1D2129'}
              sx={{
                '.ant-input-affix-wrapper': {
                  border: 'none !important',
                  borderRadius: '8px !important',
                  background: '#F2F3F5 !important',
                  padding: `0px ${vwDims(11)} !important`
                },
                'input::placeholder': {
                  fontSize: `${vwDims(14)} !important`,
                  color: '#86909C'
                },
                '.ant-select-selector': {
                  border: 'none !important',
                  borderRadius: '8px !important',
                  background: '#F2F3F5 !important',
                  fontSize: `${vwDims(14)} !important`
                },
                '.ant-select-selection-placeholder': {
                  color: '#1D2129 !important',
                  fontSize: `${vwDims(14)} !important`
                },
                '.ant-picker-outlined': {
                  border: 'none !important',
                  borderRadius: '8px !important',
                  background: '#F2F3F5 !important',
                  padding: `0px ${vwDims(11)} !important`
                }
              }}
            >
              <Flex alignItems="center" gap={vwDims(5)}>
                <Text fontSize={vwDims(14)} fontWeight={500}>
                  学生：
                </Text>
                <Input
                  placeholder="请输入学生学号、学生姓名"
                  style={{ width: vwDims(260), height: vwDims(32) }}
                  allowClear
                  value={searchKey}
                  onChange={(e) => setSearchKey(e.target.value)}
                />
              </Flex>

              {/* 只有当taskType为2时才显示分层筛选 */}
              {homeworkDetail?.taskType === 2 && (
                <Flex alignItems="center" gap={vwDims(5)}>
                  <Text fontSize={vwDims(14)} fontWeight={500}>
                    分层：
                  </Text>
                  <Select
                    value={selectedLevel}
                    placeholder="全部分层"
                    style={{ width: vwDims(170), height: vwDims(32) }}
                    options={[
                      { value: null, label: '全部分层' },
                      { value: 1, label: '熟练掌握层' },
                      { value: 2, label: '基本掌握层' },
                      { value: 3, label: '初步学习层' }
                    ]}
                    onChange={(value) => setSelectedLevel(value)}
                  />
                </Flex>
              )}

              <Flex alignItems="center" gap={vwDims(5)}>
                <Text fontSize={vwDims(14)} fontWeight={500}>
                  状态：
                </Text>
                <Select
                  value={selectedStatus}
                  placeholder="全部状态"
                  style={{ width: vwDims(170), height: vwDims(32) }}
                  options={[
                    { value: null, label: '全部状态' },
                    { value: 5, label: '已完成' },
                    { value: 6, label: '待订正' }
                  ]}
                  onChange={(value) => setSelectedStatus(value)}
                />
              </Flex>

              <Flex alignItems="center" gap={vwDims(5)}>
                <Text fontSize={vwDims(14)} fontWeight={500}>
                  订正完成时间：
                </Text>
                <DatePicker
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                  placeholder="选择结束时间"
                  style={{ width: vwDims(340), height: vwDims(32) }}
                  suffixIcon={
                    <ClockCircleOutlined style={{ color: '#4E5969', fontSize: vwDims(14) }} />
                  }
                  onChange={(date, dateString) => setReviseEndTime(dateString)}
                />
              </Flex>
            </Flex>

            <Flex flex={1} overflow="auto" h="23vw">
              {/* 表格 */}
              <MyTable<StudentErrorPageRequest, StudentErrorRecord>
                columns={columns}
                api={fetchStudentErrorListApi}
                rowKey="id"
                ref={actionRef}
                defaultQuery={{
                  current: 1,
                  size: 10,
                  homeworkId: homeworkDetail?.id || 0,
                  clazzId: 0
                }}
                headerConfig={{
                  showHeader: true,
                  showIfEmpty: true
                }}
                rowSelection={{
                  selectedRowKeys,
                  onChange: (selectedRowKeys: React.Key[], selectedRows: StudentErrorRecord[]) => {
                    setSelectedRowKeys(selectedRowKeys);
                    setSelectedRows(selectedRows);
                  },
                  getCheckboxProps: (record: StudentErrorRecord) => ({
                    disabled: false,
                    name: record.studentName || ''
                  })
                }}
              />
            </Flex>
          </Box>
        </Box>
      </Box>
      <OverlayContainer />
    </Box>
  );
}

export default SubmitOverview;
