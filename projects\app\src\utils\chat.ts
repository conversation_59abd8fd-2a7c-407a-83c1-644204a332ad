import { parseCurrentFiles, parseTemplateFile, parseUploadFile } from '@/api/chat';
import {
  ChatAppType,
  FileData,
  fileTypeInfos,
  MessageFileType
} from '@/components/ChatBox/MessageInput';
import { ChatBoxInputType, UserInputFileItemType } from '@/components/ChatBox/type';
import { ComponentType } from '@/constants/api/app';
import { ChatCompletionMessageParam } from '@/fastgpt/global/core/ai/type';
import { AppChatConfigType } from '@/fastgpt/global/core/app/type';
import { chats2GPTMessages } from '@/fastgpt/global/core/chat/adapt';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatStatusEnum,
  IMG_BLOCK_KEY
} from '@/fastgpt/global/core/chat/constants';
import {
  AIChatItemValueItemType,
  ChatItemValueItemType,
  ChatSiteItemType,
  UserChatItemValueItemType
} from '@/fastgpt/global/core/chat/type';
import { ClientAppFormDetailType, Component } from '@/types/api/app';
import { DynamicFormDataType } from '@/types/api/chat';
import { FileMetaType } from '@/types/api/file';
import { FormData2ChatDataType, ParseResultProps } from '@/types/chat';
import { cloneDeep } from 'lodash';
import { nanoid } from 'nanoid';

export function chatContentReplaceBlock(content: string = '') {
  const regex = new RegExp(`\`\`\`(${IMG_BLOCK_KEY})\\n([\\s\\S]*?)\`\`\``, 'g');
  return content.replace(regex, '').trim();
}

export function isImage(value: string, valueType: 'fileUrl' | 'fileName' = 'fileUrl'): boolean {
  // 定义常见图片扩展名的数组
  const imageExtensions = fileTypeInfos.filter((it) => it.type === 'image').map((it) => it.name);
  if (valueType === 'fileUrl') {
    // 使用 URL 对象解析fileUrl
    let pathname: string;
    try {
      const url = new URL(value);
      pathname = url.pathname;
    } catch (error) {
      // 如果 URL 无效，直接返回 false
      return false;
    }

    // 获取文件扩展名
    const fileExtension = pathname.slice(pathname.lastIndexOf('.') + 1).toLowerCase();

    // 检查文件扩展名是否在常见图片扩展名的数组中
    return imageExtensions.includes(fileExtension);
  }
  if (valueType === 'fileName') {
    const fileExtension = value.slice(value.lastIndexOf('.') + 1).toLowerCase();
    return imageExtensions.includes(fileExtension);
  }
  return false;
}

export function processImageAndFileType(files: MessageFileType[], images: MessageFileType[]) {
  const newImages: MessageFileType[] = [];
  const newFiles: MessageFileType[] = [];
  files.forEach((item) => {
    if (isImage(item.fileUrl!)) {
      newImages.push(item);
    } else {
      newFiles.push(item);
    }
  });
  images.forEach((item) => {
    if (isImage(item.fileUrl!)) {
      newImages.push(item);
    } else {
      newFiles.push(item);
    }
  });
  return { newImages, newFiles };
}

export const formatChatValue2InputType = (value: ChatItemValueItemType[]): ChatBoxInputType => {
  if (!Array.isArray(value)) {
    console.error('value is error', value);
    return { text: '', files: [] };
  }
  const text = value
    .map((item) => (item.type === 'text' && item.text?.content ? item.text?.content || '' : ''))
    .join('');

  const files =
    (value
      .map((item) =>
        item.type === 'file' && item.file?.type === 'image'
          ? {
              type: item.file.type,
              url: item.file.url
            }
          : undefined
      )
      .filter(Boolean) as unknown as UserInputFileItemType[]) || [];
  return {
    text,
    files
  };
};

export const getParseResult = async ({
  files,
  images,
  ocrFileKey,
  appId,
  chatId,
  chatConfig
}: {
  files: MessageFileType[];
  images?: MessageFileType[];
  ocrFileKey?: string;
  appId: string;
  chatId: string;
  chatConfig?: AppChatConfigType;
}) => {
  try {
    let parseResult: ParseResultProps | undefined | null;
    console.log(chatConfig?.fileSelectConfig);
    const { canAutoParse, canParseORC, canSelectFile, canSelectImg } =
      chatConfig?.fileSelectConfig || {};

    const parsePromises: Promise<ParseResultProps | undefined | null>[] = [];

    const ocrFile = files?.find((item) => item.fileKey === ocrFileKey);

    // 过滤掉第一个匹配的 ocrFileKey 的文件
    const firstMatchIndex = files.findIndex((item) => item.fileKey === ocrFileKey);
    if (firstMatchIndex !== -1) {
      files.splice(firstMatchIndex, 1);
    }
    const autoParseFiles = files;

    // 文档解析
    if (canAutoParse && files.length) {
      const fileParsePromises = autoParseFiles.map((item) =>
        parseUploadFile({ fileKey: item.fileKey! })
          .then((res) => ({
            FileContent: res.fileContent!,
            FileUrl: res.fileUrl!,
            FileType: item.name?.split('.').pop()?.toLowerCase() || '',
            FileName: item.name!
          }))
          .catch(() => ({
            FileContent: '解析失败',
            FileUrl: '',
            FileType: '',
            FileName: item.name!
          }))
      );

      parsePromises.push(
        Promise.all(fileParsePromises).then((res) => ({
          UploadedFileParsingResult: {
            Files: res
          }
        }))
      );
    }

    // 图片orc解析
    if (canParseORC && canAutoParse && images?.length) {
      const imageFiles = images
        .filter((item) => !!item && !(ocrFileKey && item.fileKey == ocrFileKey))
        .map((item) => {
          return accessFileUrlWithFilename(item.fileUrl!, item.name!);
        });

      parsePromises.push(
        parseCurrentFiles({
          files: imageFiles,
          tenantAppId: appId!,
          chatId: chatId!
        })
      );
    }

    // orc模板解析
    if (ocrFileKey) {
      if (ocrFile) {
        parsePromises.push(
          parseTemplateFile({
            fileKey: ocrFileKey
          })
            .then((res) => ({
              TemplateFileParsingResult: {
                Files: [
                  {
                    FileContent: res.ocrFileContent,
                    FileName: ocrFile.name || ''
                  }
                ]
              }
            }))
            .catch(() => ({
              TemplateFileParsingResult: {
                Files: [
                  {
                    FileContent: '解析失败',
                    FileName: ocrFile.name || ''
                  }
                ]
              }
            }))
        );
      }
    }

    const results = await Promise.all(parsePromises);

    results.forEach((result) => {
      if (result) {
        if (!parseResult) {
          parseResult = result;
        } else {
          if (result.UploadedFileParsingResult) {
            parseResult.UploadedFileParsingResult = {
              Files: [
                ...(parseResult.UploadedFileParsingResult?.Files || []),
                ...(result.UploadedFileParsingResult?.Files || [])
              ]
            };
          }
          if (result.TemplateFileParsingResult) {
            parseResult.TemplateFileParsingResult = {
              Files: [
                ...(parseResult.TemplateFileParsingResult?.Files || []),
                ...(result.TemplateFileParsingResult?.Files || [])
              ]
            };
          }
        }
      }
    });

    return parseResult;
  } catch (error) {
    console.log(error);
  }
};

export const accessFileUrlWithFilename = (fileUrl: string, filename: string) => {
  const url = new URL(fileUrl);
  const params = new URLSearchParams(url.search);
  let urlStr = fileUrl;
  if (!params.has('filename')) {
    if (params.size > 0) {
      urlStr = urlStr + `&filename=${filename.replaceAll(' ', '')}`;
    } else {
      urlStr = urlStr + `?filename=${filename.replaceAll(' ', '')}`;
    }
  }

  return urlStr;
};

export const getChatMessages = ({
  files = [],
  text = ''
}: {
  files?: MessageFileType[];
  text?: string;
}): {
  messages: ChatCompletionMessageParam[];
  newChatList: ChatSiteItemType[];
  aiDataId: string;
  humanDataId: string;
} => {
  const humanDataId = nanoid();
  const aiDataId = nanoid();
  const pureFiles: MessageFileType[] = [];
  const images: MessageFileType[] = [];
  files?.forEach((file) => {
    if (isImage(file.fileUrl!)) {
      images.push(file);
    } else {
      pureFiles.push(file);
    }
  });
  let newChatList: ChatSiteItemType[] = [
    {
      dataId: humanDataId,
      obj: ChatRoleEnum.Human,
      isShareContent: false,
      value: [
        ...(pureFiles?.map((file) => ({
          type: ChatItemValueTypeEnum.file,
          file: {
            type: ChatFileTypeEnum.file,
            name: file.name || '',
            url: file.fileUrl!.includes('?filename=')
              ? file.fileUrl
              : `${file.fileUrl}?filename=${file.name}`,
            fileId: file.fileKey || ''
          }
        })) || []),
        ...(images.map((image) => ({
          type: ChatItemValueTypeEnum.file,
          file: {
            type: ChatFileTypeEnum.image,
            url: image.fileUrl!.includes('?filename=')
              ? image.fileUrl
              : `${image.fileUrl}?filename=${image.name?.replaceAll(' ', '')}`
          }
        })) || []),

        ...(text
          ? [
              {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: text
                }
              }
            ]
          : [])
      ] as UserChatItemValueItemType[],
      status: 'finish'
    },
    {
      dataId: aiDataId,
      obj: ChatRoleEnum.AI,
      isShareContent: false,
      value: [
        {
          type: ChatItemValueTypeEnum.text,
          text: {
            content: ''
          }
        }
      ],
      status: 'loading'
    }
  ];

  const messages = chats2GPTMessages({ messages: newChatList, reserveId: true });
  return { messages, newChatList, aiDataId, humanDataId };
};

export const formData2ChatData = (
  formData: DynamicFormDataType,
  clientAppFormDetail: ClientAppFormDetailType
): FormData2ChatDataType => {
  let newEditorValue = '';
  const newImages: MessageFileType[] = [];
  const newFiles: MessageFileType[] = [];
  let uploadData: FileMetaType[] = [];
  let chatApp: ChatAppType | undefined;
  // 按照 clientAppFormDetail.components 的顺序处理 formData
  const components = cloneDeep(clientAppFormDetail.components);
  let ocrFileKey = '';

  components.forEach((component: Component, index: number) => {
    const key = component.id;
    const value = formData?.[key];

    component.value = value || '';

    if (
      component.type === ComponentType.OptionGroup &&
      component.children &&
      component.children.length > 0
    ) {
      component.children.forEach((child: Component) => {
        child.value = formData?.[child.id];
      });
    }

    if (component.type === ComponentType.Upload) {
      (value as FileMetaType[])?.forEach((file, index) => {
        if (!ocrFileKey) {
          ocrFileKey = component.isCallOcr ? file.fileKey || '' : '';
        }
        newEditorValue += `${component.title}: ${file.fileName}\n`;

        if (isImage(file.fileUrl)) {
          newImages.push({
            fileUrl: file.fileUrl,
            fileKey: file.fileKey || '',
            name: file.fileName
          });
        } else {
          try {
            const url = new URL(file.fileUrl);
            const extension = url.pathname.split('.').pop()?.toLowerCase() || '';
            console.log(extension, 'extension');
            // 查找文件类型信息
            const info = fileTypeInfos.find((it) => it.name == extension);

            if (info) {
              newFiles.push({
                fileKey: file.fileKey || '',
                fileUrl: file.fileUrl,
                name: file.fileName
              });
            }
          } catch (error) {
            console.log(error);
          }
        }
      });
    } else {
      // 添加文件信息到 newEditorValue
      if (Array.isArray(value)) {
        newEditorValue += `${component.title}: ${value.join(',')}\n`;
      } else {
        if (component.type === ComponentType.OptionGroup) {
          newEditorValue += `${component.title}: \n`;
          component.children?.forEach((child) => {
            newEditorValue += `    ${child.title}: ${child.value}\n`;
          });
        } else {
          newEditorValue += `${component.title}: ${component.value}\n`;
        }
      }
    }
  });

  return {
    text: newEditorValue,
    images: newImages,
    files: newFiles,
    ocrFileKey: ocrFileKey || '',
    components: components
  };
};

export const replacePreWithDiv = (node: Node) => {
  if (node.nodeName === 'PRE') {
    const div = document.createElement('div');
    const preElement = node as HTMLElement;

    for (let i = 0; i < preElement.attributes.length; i++) {
      div.setAttribute(preElement.attributes[i].name, preElement.attributes[i].value);
    }

    const computedStyle = window.getComputedStyle(preElement);
    for (let i = 0; i < computedStyle.length; i++) {
      const key = computedStyle[i] as string;
      div.style.setProperty(key, computedStyle.getPropertyValue(key));
    }

    div.className = preElement.className;
    div.classList.add('code-content');

    while (preElement.firstChild) {
      div.appendChild(preElement.firstChild);
    }

    preElement.parentNode?.replaceChild(div, preElement);
  } else {
    for (let i = 0; i < node.childNodes.length; i++) {
      replacePreWithDiv(node.childNodes[i]);
    }
  }
};

export const checkIsInteractiveByHistories = (chatHistories: ChatSiteItemType[]) => {
  const lastAIHistory = chatHistories[chatHistories.length - 1];
  if (!lastAIHistory) return false;

  const lastMessageValue = lastAIHistory.value[
    lastAIHistory.value.length - 1
  ] as AIChatItemValueItemType;

  if (
    lastMessageValue &&
    lastMessageValue.type === ChatItemValueTypeEnum.interactive &&
    !!lastMessageValue?.interactive?.params
  ) {
    const params = lastMessageValue.interactive.params;
    // 如果用户选择了，则不认为是交互模式（可能是上一轮以交互结尾，发起的新的一轮对话）
    if ('userSelectOptions' in params) {
      return !params.userSelectedVal;
    } else if ('inputForm' in params) {
      return !params.submitted;
    }
  }

  return false;
};

export const setUserSelectResultToHistories = (
  histories: ChatSiteItemType[],
  interactiveVal: string
): ChatSiteItemType[] => {
  if (histories.length === 0) return histories;

  // @ts-ignore
  return histories.map((item, i) => {
    if (i !== histories.length - 1) return item;

    const value = item.value.map((val, i) => {
      if (
        i !== item.value.length - 1 ||
        val.type !== ChatItemValueTypeEnum.interactive ||
        !val.interactive
      )
        return val;

      if (val.interactive.type === 'userSelect') {
        return {
          ...val,
          interactive: {
            ...val.interactive,
            params: {
              ...val.interactive.params,
              userSelectedVal: val.interactive.params.userSelectOptions.find(
                (item) => item.value === interactiveVal
              )?.value
            }
          }
        };
      }

      if (val.interactive.type === 'userInput') {
        return {
          ...val,
          interactive: {
            ...val.interactive,
            params: {
              ...val.interactive.params,
              submitted: true
            }
          }
        };
      }
    });

    return {
      ...item,
      status: ChatStatusEnum.loading,
      value
    };
  });
};

export const getJsonFromText = async (text: string) => {
  try {
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    return JSON.parse(jsonMatch?.[0] || '{}');
  } catch (error) {
    return Promise.resolve('json解析失败，请检查返回内容');
  }
};

// 检验json字段是否符合要求
export const checkJsonFields = (json: any, schema: any): boolean => {
  // 基础类型检查
  if (typeof json !== 'object' || json === null) {
    return typeof schema !== 'object' || schema === null;
  }

  if (typeof schema !== 'object' || schema === null) {
    return false;
  }

  // 数组类型检查
  if (Array.isArray(schema)) {
    if (!Array.isArray(json)) return false;
    if (schema.length === 0) return true; // 空数组schema允许任意数组

    // 检查数组中每个元素是否符合schema[0]的格式
    const elementSchema = schema[0];
    return json.every((item) => checkJsonFields(item, elementSchema));
  }

  if (Array.isArray(json)) {
    return false; // json是数组但schema不是数组
  }

  // 对象类型检查
  for (const key in schema) {
    if (!schema.hasOwnProperty(key)) continue;

    // 检查必需字段是否存在
    if (!(key in json)) {
      return false;
    }

    const schemaValue = schema[key];
    const jsonValue = json[key];

    // 递归检查嵌套对象
    if (typeof schemaValue === 'object' && schemaValue !== null) {
      if (!checkJsonFields(jsonValue, schemaValue)) {
        return false;
      }
    } else {
      // 检查基础类型是否匹配
      if (typeof jsonValue !== typeof schemaValue) {
        return false;
      }
    }
  }

  return true;
};

// 如果字段不存在，赋值模板字段
export const setTemplateFields = (json: any, schema: any): any => {
  // 基础类型检查
  if (typeof json !== 'object' || json === null) {
    return json;
  }

  if (typeof schema !== 'object' || schema === null) {
    return json;
  }

  // 数组类型处理
  if (Array.isArray(schema)) {
    if (!Array.isArray(json)) return json;
    if (schema.length === 0) return json;

    // 处理数组中每个元素
    const elementSchema = schema[0];
    return json.map((item) => setTemplateFields(item, elementSchema));
  }

  if (Array.isArray(json)) {
    return json;
  }

  // 对象类型处理
  const result = { ...json };

  for (const key in schema) {
    if (!schema.hasOwnProperty(key)) continue;

    const schemaValue = schema[key];

    if (!(key in result)) {
      // 如果字段不存在，赋值模板字段
      if (typeof schemaValue === 'object' && schemaValue !== null) {
        // 对于对象或数组类型，需要深拷贝避免引用问题
        result[key] = Array.isArray(schemaValue) ? [...schemaValue] : { ...schemaValue };
      } else {
        // 基本类型直接赋值
        result[key] = schemaValue;
      }
    }

    // 递归处理嵌套对象
    if (typeof schemaValue === 'object' && schemaValue !== null && result[key]) {
      result[key] = setTemplateFields(result[key], schemaValue);
    }
  }

  return result;
};
