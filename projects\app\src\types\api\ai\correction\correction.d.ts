import {
  CorrectionSubject,
  OriginContentType,
  StudentCorrectionStatus
} from '@/constants/ai/correction';

export type StudentInfoType = {
  compositionResultId: string;
  studentId: string;
  studentName: string;
  studentCode: string;
  id: string;
  status: StudentCorrectionStatus;
  score: string;
  fileKeys: string;
  files: FileMetaType[];
  content: string;
  updateTime: string;
};

export type StudentCorrectionDetailType = {
  id: string;
  createTime: string;
  updateTime: string;
  correctionResultFileKey: string;
  studentName: string;
  studentCode: string;
  status: StudentCorrectionStatus;
  compositionContent: string;
  compositionId: string;
  compositionFiles: FileMetaType[];
  correctionResultFileKey: string;
  scoreText: string;
  score: string;
  fullScore: string;
  overallComment: string;
  errorAndSuggestion: string;
  paragraphSuggestions: string;
  // 原文亮点提炼
  originalHighlights: string;
  // 词汇提升建议
  vocabSuggest: string;
  // 润色范文
  polishedText: string;
  subject: '英语' | '语文';
  originContentWay?: OriginContentType;
};

export type StudentListItemType = {
  studentName: string;
  status: StudentCorrectionStatus;
  studentId: string;
  compositionId: string;
};
