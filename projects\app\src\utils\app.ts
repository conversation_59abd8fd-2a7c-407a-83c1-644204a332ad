import { parseUploadFile } from '@/api/chat';
import {
  AppDetailInfo,
  AppDetailMerge,
  AppListItemType,
  TenantAppKnowledgeFile
} from '@/types/api/app';
import { BackgroundFileType } from '@/types/api/chat';
import { TenantAppListItemType } from '@/types/api/tenant/app';
import { AppSimpleEditFormTypeMegre } from '@/types/app';

export const compareCustomAttrs = (
  origin: Partial<AppDetailInfo>,
  formData: AppSimpleEditFormTypeMegre
): boolean => {
  // Check if isStructuredPrompt is the same
  if (origin.isStructuredPrompt !== formData.isStructuredPrompt) {
    return false;
  }

  // Check if promptList arrays have the same length
  if (origin.promptList?.length !== formData.promptList.length) {
    return false;
  }

  // Check if each SimpleAppPrompt's title and content are the same
  for (let i = 0; i < origin?.promptList.length; i++) {
    const originPrompt = origin?.promptList[i];
    const formPrompt = formData.promptList[i];

    if (originPrompt.title !== formPrompt.title || originPrompt.content !== formPrompt.content) {
      return false;
    }
  }

  if (origin.filesList?.length !== formData.filesList.length) {
    return false;
  }

  if (origin.filesList?.length === formData.filesList.length) {
    for (let i = 0; i < origin?.filesList.length; i++) {
      const originFile = origin?.filesList[i];
      const formFile = formData.filesList[i];

      if (originFile.id !== formFile.id) {
        return false;
      }
    }
  }

  return true;
};

export const getChatBackGroundFiles = async (
  filesList: TenantAppKnowledgeFile[]
): Promise<BackgroundFileType[]> => {
  const fileParsePromises = filesList.map((item) =>
    parseUploadFile({ fileKey: item.fileKey! }).then((res) => {
      const file: BackgroundFileType = {
        fileContent: res.fileContent!,
        fileUrl: res.fileUrl!,
        fileType: item.fileName?.split('.').pop()?.toLowerCase() || '',
        fileName: item.fileName!,
        fileSize: item.fileSize,
        fileKey: item.fileKey
      };
      return file;
    })
  );

  const fileInfos = await Promise.all(fileParsePromises);
  return fileInfos;
};

export const getSampleAppInfo = (
  appDetail: AppListItemType | Partial<AppDetailInfo> | TenantAppListItemType
) => {
  let data = {
    id: appDetail.id,
    name: appDetail.name,
    source: appDetail.source,
    tmbId: appDetail.tmbId,
    finalAppId: appDetail.finalAppId,
    type: appDetail.type,
    intro: appDetail.intro
  };

  return data;
};
