import React, { useState, useEffect } from 'react';
import { Select, message } from 'antd';
import { getClientSemesterPage } from '@/api/tenant/teamManagement/semester';
import { ClientSemesterPageType } from '@/types/api/tenant/teamManagement/semester';

const { Option } = Select;

// 使用API返回类型，避免类型不兼容问题
interface SemesterChangeInfo {
  semesterId: string;
  semester: ClientSemesterPageType | null;
}

interface SearchBarProps {
  onSemesterChange: (info: SemesterChangeInfo) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSemesterChange }) => {
  const [semesters, setSemesters] = useState<ClientSemesterPageType[]>([]);
  const [currentSemester, setCurrentSemester] = useState<string | undefined>(undefined);
  const [defaultSemester, setDefaultSemester] = useState<ClientSemesterPageType | null>(null);

  useEffect(() => {
    fetchSemesters();
  }, []);

  const fetchSemesters = async () => {
    try {
      const response = await getClientSemesterPage({ current: 1, size: 999 });
      setSemesters(response.records || []);
      const current = response.records.find((sem) => sem.isCurrent === 1);
      if (current) {
        setCurrentSemester(current.id);
        setDefaultSemester(current);
        onSemesterChange({ semesterId: current.id, semester: current });
      }
    } catch (error) {
      message.error('获取学期列表失败');
    }
  };

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);
    const selectedSemester = value ? semesters.find((sem) => sem.id === value) || null : null;
    onSemesterChange({ semesterId: value || '', semester: selectedSemester });
  };

  const getSemesterLabel = (sem: ClientSemesterPageType) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  return (
    <Select
      value={currentSemester}
      variant="filled"
      style={{
        width: 250,
        border: 'none',
        borderRadius: '8px'
      }}
      placeholder="请选择学期"
      onChange={handleSemesterChange}
    >
      {semesters.map((sem) => (
        <Option key={sem.id} value={sem.id}>
          {getSemesterLabel(sem)}
        </Option>
      ))}
    </Select>
  );
};

export default SearchBar;
