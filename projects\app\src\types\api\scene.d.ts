import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { SimpleAppPrompt, TenantAppKnowledgeFile } from './app';
import { DataSource } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';

export interface SceneType {
  id: string;
  tenantId?: string;
  tmbId: string;
  name: string;
  avatarUrl: string;
  sort: number;
  sceneId: string;
  createTime: string;
}

export interface SceneUpdateParams {
  id?: string;
  avatar?: string;
  name?: string;
  sort?: number;
  isDelete?: number;
}

export interface SceneReorderParams {
  param: SceneUpdateParams[];
}
export interface tenantLabeParams {
  tenantSceneId: string;
}
export interface SubSceneType {
  id: string;
  isDeleted: string;
  tenantId?: string;
  tmbId: string;
  name: string;
  avatarUrl: string;
  sort: number;
  tenantSceneId: string;
  createTime: string;
}

type appsParams = {
  name: string;
  id: string;
  avatarUrl: string;
  sort: number;
  source: number;
};

type labelsType = {
  name: string;
  id: string;
  sort: number;
  apps: appsParams[];
};
export interface TenantSceneNavbarType {
  name: string;
  id: string;
  avatarUrl: string;
  sort: number;
  labels: labelsType[];
}

export type TenantAppDetailParams = {
  id: string;
};

export interface sceneListType {
  tenantSceneId: string;
  isDisplayed?: number;
}
export type TenantAppDetailType = {
  id?: string;
  name: string;
  type?: AppTypeEnum;
  avatarUrl: string;
  intro?: string;
  source?: DataSource;
  mode?: number;
  filesList?: TenantAppKnowledgeFile[];
  modules?: AppSchema['modules'];
  edges?: AppSchema['edges'];
  permission?: PermissionTypeEnum;
  sceneList: sceneListType[];
  promptList?: SimpleAppPrompt[];
};

export type CommonAppType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tmbId: number;
  tenantAppId: number;
  sort: number;
  isNavbar: number;
  navbarOrder: number;
  tenantApp: AppListItemType;
  /** typd=1 为应用 */
  type: number;
};
