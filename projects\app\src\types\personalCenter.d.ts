/** 年级列表 */
export interface GradeItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  name: string;
  code: string;
  stageId: number;
  sortOrder: number;
}
export interface GradeListParams {
  stageId: number;
}
export interface GradeListResponse {
  data: GradeItem[];
}

/** 资源分页列表 */
export interface ResourcePageParams {
  attribution?: number;
  fileFormatId?: number;
  gradeId?: number;
  pageNum: number;
  pageSize: number;
  resourceTypeId?: number;
  stageId?: number;
  subjectId?: number;
  title?: string;
  source?: number; // 来源：0全部，1官方资源（attribution=3），2校本资源（attribution=2）
}

/** 我的资源分页列表参数 */
export interface MyResourcePageParams {
  attribution?: number;
  fileFormatId?: number;
  gradeId: number;
  includeCollectionStatus: boolean;
  includeFileInfo: boolean;
  includeKnowledgePoints: boolean;
  includeTags: boolean;
  pageNum: number;
  pageSize: number;
  resourceTypeId?: number;
  stageId?: number;
  status?: number;
  shareStatus?: number; // 添加 shareStatus 参数支持
  subjectId?: number;
  title?: string;
  syncStatus: number;
}

/** 资源项详细信息 */
export interface ResourceItem {
  id: string | number;
  title?: string;
  description?: string;
  resourceTypeId?: number;
  fileFormatId?: number;
  fileFormatName?: string;
  subjectId?: number;
  gradeId?: number;
  stageId?: number;
  textbookVersionId?: number;
  textbookVolumeId?: number;
  textbookChapterId?: number;
  areaId?: number;
  areaCode?: string;
  createUserName?: string;
  attribution?: number;
  attributionToId?: number;
  vintages?: number;
  shareStatus?: number;
  downloadCount?: number;
  viewCount?: number;
  curation?: number;
  fileKey?: string;
  fileName?: string;
  fileUrl?: string;
  fileSize?: number;
  fileJson?: string;
  status?: number;
  resourceTypeName?: string;
  subjectName?: string;
  stageName?: string;
  gradeName?: string;
  textbookVersionName?: string;
  textbookVolumeName?: string;
  textbookChapterName?: string;
  areaName?: string;
  isCollected?: boolean;
  isOwned?: number;
  tmbName?: string;

  // 兼容旧字段
  name?: string;
  fileFormat?: string;
  format?: string;
  updateTime?: number;
  createTime?: number;
  tags?: string[];
  isFeatured?: boolean;
  featured?: boolean;
  isOfficial?: boolean;
  official?: boolean;
  isFavorite?: boolean;

  favorite?: boolean;
  resourceType?: string;
  taskType?: string;
  subject?: string;
  textbookVersion?: string;
  version?: string;
  region?: string;
  area?: string;
  year?: string;
  knowledgePoints?: string[];
  createMethod?: 'upload' | 'create';
}

export interface ResourcePageResponse {
  records: ResourceItem[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

/** 作业资源搜索接口 */
export interface SearchHomeworkResourcesParams {
  areaId?: number; // 地区ID
  chapterIds?: number[]; // 章节ID列表
  fileFormatId?: number; // 资源格式ID
  keyword?: string; // 关键词（资源标题模糊搜索）
  knowledgePointIds?: number[]; // 知识点ID列表
  pageNum: number; // 页码
  pageSize: number; // 每页数量
  resourceTypeId?: number; // 资源类型ID
  source?: number; // 来源：1官方资源，2校本资源，3我的资源，4我的收藏
  stageId?: number; // 学段ID
  subjectId?: number; // 学科ID
  supplierId?: number; // 供应商ID
  textbookVersionId?: number; // 教材版本ID
  textbookVolumeId?: number; // 教材册别ID
  year?: number; // 年份
}

export interface ResourcesVO {
  areaCode?: string; // 地区code
  areaId?: number; // 地区ID
  areaName?: string; // 地区名称
  attribution?: number; // 归属类型
  attributionToId?: number; // 归属关联ID
  attributionToName?: string; // 归属管理名称
  createTime?: number; // 创建时间(时间戳)
  createTimeStr?: string; // 创建时间(格式化)
  createUserName?: string; // 上传者名称
  curation?: number; // 是否精选:0否；1是
  customTags?: string[]; // 自定义标签列表
  cutQuestion?: number; // 切题状态：0未切题；1已切题，未审核；2已审核
  description?: string; // 资源描述
  downloadCount?: number; // 下载量
  fileFormatId?: number; // 文件格式ID
  fileFormatName?: string; // 文件格式名
  fileJson?: string; // 文件详情Json
  fileKey?: string; // 文件Key
  fileName?: string; // 文件名称
  fileSize?: number; // 文件大小
  fileUrl?: string; // 文件路径
  gradeId?: number; // 年级ID
  gradeName?: string; // 年级名称
  homeworkId?: number; // 作业ID
  id: number; // 主键ID
  isCollected?: number; // 是否已收藏：0否，1是
  isOperable?: number; // 是否可操作：0否，1是
  isOwned?: number; // 是否属于当前用户：0否，1是
  isQuestionCollection?: number; // 是否题目合集：0否，1是
  knowledgePointIds?: number[]; // 知识点ID列表
  knowledgePointNames?: string[]; // 知识点名称列表
  questionCount?: number; // 题目关联数量
  resourceTypeId?: number; // 资源类型ID
  resourceTypeName?: string; // 资源类型名称
  shareStatus?: number; // 共享状态：0私有，1共享到学校
  stageId?: number; // 学段ID
  stageName?: string; // 学段名称
  status?: number; // 资源状态：0草稿，1审核中，2被驳回，3已发布，4已下架
  subjectId?: number; // 学科ID
  subjectName?: string; // 学科名称
  textbookChapterIds?: number[]; // 章节ID列表
  textbookChapterNames?: string[]; // 章节名称列表
  textbookVersionId?: number; // 教材版本ID
  textbookVersionName?: string; // 教材版本名称
  textbookVolumeId?: number; // 教材ID
  textbookVolumeName?: string; // 教材名称
  title?: string; // 资源标题
  tmbName?: string; // 租户用户名
  updateTime?: number; // 更新时间(时间戳)
  updateTimeStr?: string; // 更新时间(格式化)
  viewCount?: number; // 预览量
  vintages?: number; // 年份
}

export interface SearchHomeworkResourcesResponse {
  current: number;
  pages: number;
  records: ResourcesVO[];
  searchCount: boolean;
  size: number;
  total: number;
}

/** 资源类型列表 */
export interface ResourceTypeItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  name: string;
  code: string;
  parentId: number;
  collection: number;
  sortOrder: number;
}
export interface ResourceTypeListResponse {
  data: ResourceTypeItem[];
}

/** 学段列表 */
export interface StageItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  name: string;
}
export interface StageListParams {
  stageId: number;
}
export interface StageListResponse {
  data: StageItem[];
}

/** 学科列表 */
export interface SubjectsItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  name: string;
  stageId: number;
}
export interface SubjectsListParams {
  stageId: number;
}
export interface SubjectsListResponse {
  data: SubjectsItem[];
}

/** 资源格式树形列表 */
export interface FileFormatChild {
  id: number;
  formatName: string;
  parentId: number;
}
export interface FileFormatTreeItem {
  id: number;
  formatName: string;
  parentId: number;
  children?: FileFormatChild[];
}
export interface FileFormatTreeResponse {
  data: FileFormatTreeItem[];
}

/** 数据总览 */
export interface OverviewResponse {
  totalResources: number;
  myResources: number;
  myQuestions: number;
  myCollections: number;
}

/** 取消收藏请求参数 */
export interface CancelFavoriteRequest {
  resourceIds: number[];
}

/** 收藏资源请求参数 */
export interface CollectFavoriteRequest {
  resourceIds: number[];
}

/** 我的资源收藏分页列表参数 */
export interface MyCollectionsPageParams {
  source: number;
  stageId?: number;
  gradeId?: number;
  pageNum: number;
  pageSize: number;
  resourceTypeId?: number;
  subjectId?: number;
  title?: string;
}

/** 我的资源收藏分页列表响应 */
export interface MyCollectionsPageResponse {
  records: ResourceItem[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

/** 我的资源收藏分页列表参数 - modal类型 */
export interface MyResourceCollectionsPageParams {
  source: number;
  stageId?: number;
  gradeId?: number;
  pageNum: number;
  pageSize: number;
  resourceTypeId?: number;
  subjectId?: number;
  title?: string;
}

/** 我的资源收藏分页列表响应 - modal类型 */
export interface MyResourceCollectionsPageResponse {
  current: number;
  pages: number;
  records: Array<{
    areaCode: string;
    areaId: number;
    areaName: string;
    attribution: number;
    attributionToId: number;
    attributionToName: string;
    createTime: number;
    createUserName: string;
    curation: number;
    customTags: string[];
    cutQuestion: number;
    description: string;
    downloadCount: number;
    fileFormatId: number;
    fileFormatName: string;
    fileJson: string;
    fileKey: string;
    fileName: string;
    fileSize: number;
    fileUrl: string;
    gradeId: number;
    gradeName: string;
    homeworkId: number;
    id: number;
    isCollected: number;
    isOperable: number;
    isOwned: number;
    knowledgePointIds: number[];
    knowledgePointNames: string[];
    questionCount: number;
    resourceTypeId: number;
    resourceTypeName: string;
    shareStatus: number;
    stageId: number;
    stageName: string;
    status: number;
    subjectId: number;
    subjectName: string;
    textbookChapterIds: number[];
    textbookChapterNames: string[];
    textbookVersionId: number;
    textbookVersionName: string;
    textbookVolumeId: number;
    textbookVolumeName: string;
    title: string;
    tmbName: string;
    updateTime: number;
    viewCount: number;
    vintages: number;
  }>;
  searchCount: boolean;
  size: number;
  total: number;
}

export interface ResourceDetailParams {
  resourceId: number;
}

export interface ResourceDetailResponse {
  areaCode: string;
  areaId: number;
  areaName: string;
  attribution: number;
  attributionToId: number;
  createTime: number;
  updateTime: number;
  createUserName: string;
  curation: number;
  customTags: string[];
  cutQuestion: number; //切题状态：0未切题；1已切题，未审核；2已审核
  isQuestionCollection: number; //是否是题集(题集可切题) 0 否 1 是
  description: string;
  downloadCount: number;
  fileFormatId: number;
  fileFormatName: string;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  gradeId: number;
  gradeName: string;
  id: number;
  isCollected: number;
  isOperable: number;
  isOwned: number;
  knowledgePointIds: number[];
  knowledgePointNames: string[];
  resourceTypeId: number;
  resourceTypeName: string;
  shareStatus: number;
  stageId: number;
  stageName: string;
  status: number;
  subjectId: number;
  subjectName: string;
  textbookChapterIds: number[];
  textbookChapterNames: string[];
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  title: string;
  viewCount: number;
  vintages: number;
  toStudent: number;
  toTeacher: number;
}
export interface AiDisassemblyParams {
  fileUrl: string;
  filename?: string;
  questionTypeList: QuestionTypeWithFormatItem[];
  questionTypeFormat: QuestionTypeFormatItem[];
  subjectInfo: SubjectInfo;
  chapterTree?: ChapterTreeItemResp[];
  knowledgeTree: KnowledgeTreeItemResp[];
}
export interface SubjectInfo {
  subject: string;
  grade: string;
  stage: string;
}
export interface AiDisassemblyQuestion {
  questionIndex;
  string;
  questionTypeName: string;
  selectAnswer: string;
  applicationAnswer: string;
  questionParser: QuestionParser;
  selectOptions: string[];
  replenishAnswer: string[];
  questionTypeFormat: string;
  questionTypeId: string;
  selectAnswerIndex: string;
  stem: string;
  subQuestionList: SubQuestionList[];
  // answer: string;
  // options?: string[];
  // type: string;
  // stem: string;
}
export interface QuestionParser {
  difficulty: string;
  selectAnswer: string;
  applicationAnswer: string;
  chapterList: ChapterList[];
  replenishAnswer: string[];
  analysis: string;
  knowledgeList: KnowledgeTreeItemResp[];
  selectAnswerIndex: string[];
  subQuestionList: SubQuestionParser[];
}
export interface SubQuestionParser {
  difficulty: string;
  selectAnswer: string;
  applicationAnswer: string;
  chapterList: ChapterList[];
  replenishAnswer: string[];
  analysis: string;
  knowledgeList: KnowledgeTreeItemResp[];
  selectAnswerIndex: string[];
  subQuestionList: string[];
}
export interface SubQuestionList {
  questionIndex: string;
  questionTypeName: string;
  questionTypeFormat: string;
  questionTypeId: string;
  selectAnswer: string;
  selectOptions: string[];
  applicationAnswer: string;
  replenishAnswer: string[];
  stem: string;
  selectAnswerIndex: string[];
  subQuestionList: string[];
}
export interface ChapterList {
  chapterId: string;
  chapterName: string;
}

export type AiDisassemblyResponse = AiDisassemblyQuestion[];

/** 简答题相关类型 */
export type SubQuestionType = 'multiple' | 'fillBlank' | 'shortAnswer';

export interface SubQuestion {
  id: string;
  title: string;
  type: SubQuestionType;
  questionTypeId?: string; // 添加题型ID字段
  data: {
    questionContent?: string;
    options?: string[];
    correctAnswer?: number | string;
    answers?: string[];
    answer?: string;
    analysis?: string;
  };
}

export interface ShortAnswerData {
  questionContent: string;
  material: string;
  subQuestions: SubQuestion[];
  totalAnswer: string;
  totalAnalysis: string;
}

/** 题型列表 */
export interface QuestionTypeItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  name: string;
  code: string;
  type: number;
  stageId: number;
  subjectId: number;
  sortOrder: number;
}

export interface QuestionTypesParams {
  pageNum: number;
  pageSize: number;
  stageId: number;
  subjectId: number;
}

export interface QuestionTypesResponse {
  data: QuestionTypeItem[];
}

/** 难度列表 */
export interface DifficultyItem {
  id: number;
  name: string;
}

export interface DifficultyListResponse {
  data: DifficultyItem[];
}

/** 子题参数（不包含子题，子题里面不嵌套子题） */
export interface SubQuestionParams {
  analysis: string; // 解析
  answer: string; // 答案（根据题型不同含义不同）
  areaId: number; // 地区ID
  difficulty: number; // 难度
  gradeId: number; // 年级ID
  knowledgePointIds: number[]; // 知识点ID数组
  options: string; // 选项（根据题型不同含义不同）
  questionTypeId: number; // 题型ID
  shareStatus: number; // 分享状态
  stageId: number; // 学段ID
  stem: string; // 题干
  subjectId: number; // 学科ID
  textbookChapterIds: number[]; // 教材章节ID数组
  textbookVersionId: number; // 教材版本ID
  textbookVolumeId: number; // 教材册次ID
  vintages: number; // 年份
}

/** 新增题目请求参数 */
export interface AddQuestionParams {
  analysis: string; // 解析
  answer: string; // 答案字段（根据题型不同含义不同）
  // - 选择题：答案选项转JSON（如：["A", "B"]）
  // - 填空题：填空答案数组转JSON（如：["答案1", "答案2"]）
  // - 解答题：解答题总答案
  areaId: number; // 地区ID
  children: SubQuestionParams[]; // 子题数组（仅解答题使用，子题不包含children字段）
  difficulty: number; // 难度
  gradeId: number; // 年级ID
  knowledgePointIds: number[]; // 知识点ID数组
  options: string; // 选项字段（根据题型不同含义不同）
  // - 选择题：选项内容数组转JSON（如：["选项内容1", "选项内容2", "选项内容3", "选项内容4"]）
  // - 解答题：材料部分
  // - 填空题：不使用此字段
  questionTypeId: number; // 题型ID
  shareStatus: number; // 分享状态
  stageId: number; // 学段ID
  stem: string; // 题干
  subjectId: number; // 学科ID
  textbookChapterIds: number[]; // 教材章节ID数组
  textbookVersionId: number; // 教材版本ID
  textbookVolumeId: number; // 教材册次ID
  vintages: number; // 年份
  groupId?: string;
}
/** 编辑题目请求参数 */
export interface EditQuestionParams {
  id: number; // 题目id
  analysis: string; // 解析
  answer: string; // 答案字段（根据题型不同含义不同）
  // - 选择题：答案选项转JSON（如：["A", "B"]）
  // - 填空题：填空答案数组转JSON（如：["答案1", "答案2"]）
  // - 解答题：解答题总答案
  areaId: number; // 地区ID
  children: SubQuestionParams[]; // 子题数组（仅解答题使用，子题不包含children字段）
  difficulty: number; // 难度
  gradeId: number; // 年级ID
  knowledgePointIds: number[]; // 知识点ID数组
  options: string; // 选项字段（根据题型不同含义不同）
  // - 选择题：选项内容数组转JSON（如：["选项内容1", "选项内容2", "选项内容3", "选项内容4"]）
  // - 解答题：材料部分
  // - 填空题：不使用此字段
  questionTypeId: number; // 题型ID
  shareStatus: number; // 分享状态
  stageId: number; // 学段ID
  stem: string; // 题干
  subjectId: number; // 学科ID
  textbookChapterIds: number[]; // 教材章节ID数组
  textbookVersionId: number; // 教材版本ID
  textbookVolumeId: number; // 教材册次ID
  vintages: number; // 年份
}
/** 我的题目列表分页查询参数 */
export interface MyQuestionListParams {
  difficulty?: number;
  gradeId?: number;
  pageNum: number;
  pageSize: number;
  questionTypeId?: number;
  subjectId?: number;
  knowledgePointIds?: number[];
  textbookChapterId?: number[];
  textbookVersionId?: number;
  textbookVolumeId?: number;
  tmbId?: number;
  year?: number;
}

/** 我的题目列表项 */
export interface MyQuestionItem {
  id: number;
  stem: string;
  options: string;
  answer: string;
  analysis: string;
  difficulty: number;
  gradeId: number;
  gradeName: string;
  subjectId: number;
  subjectName: string;
  questionTypeId: number;
  questionTypeName: string;
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  textbookChapterId: number | null;
  textbookChapterName: string;
  createTime: string;
  updateTime: string;
  tags: string[];
  knowledgePointIds: number[];
  knowledgePointNames: string[];
  textbookChapterIds: number[];
  textbookChapterNames: string[];
  sourceFiles: SourceFileItem[];
  questionClasses: string[];
  children: MyQuestionItem[]; // 子题，结构与主题相同
  stageId: number;
  stageName: string;
}
export interface SourceFileItem {
  createTime: number;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: number;
  questionId: number;
  updateTime: number;
}
/** 我的题目列表分页响应 */
export interface MyQuestionListResponse {
  records: MyQuestionItem[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

/** 删除题目请求参数 */
export interface DeleteQuestionParams {
  questionId: number;
}

/** 题目详情请求参数 */
export interface QuestionDetailParams {
  questionId: number;
}

// 知识点树结构接口
export interface KnowledgePointTreeParams {
  parentId: number;
  stageId: number;
  subjectId: number;
}

export interface KnowledgePointTreeItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  name: string;
  parentId: number;
  code: string;
  subjectId: number;
  stageId: number;
  level: number;
  children: KnowledgePointTreeItem[];
}

/** 地区树节点 */
export interface AreaTreeItem {
  id: number;
  name: string;
  code: string;
  children: AreaTreeItem[];
}

/** 地区树响应 */
export type AreaTreeResponse = AreaTreeItem[];

/** 年份列表响应 */
export type YearListResponse = number[];
export type BatchDeleteQuestionParams = {
  questionIds: number[];
};
/** 获取教材版本请求参数 */
export interface TextbookVersionsParams {
  stageId: number;
  subjectId: number;
}
export interface TextbookVersionsItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  name: string;
  code: string;
}
export type TextbookVersionsResponse = TextbookVersionsItem[];

/** 添加资源请求参数 */
export interface AddResourceParams {
  /** 地区ID */
  areaId?: number;
  /** 归属类型：0-个人资源，1-校本资源，2-官方资源 */
  attribution?: number;
  /** 归属对象ID */
  attributionToId?: number;
  /** 归属对象名称 */
  attributionToName?: string;
  /** 创建者名称 */
  createUserName?: string;
  /** 精选状态：0-未精选，1-已精选 */
  curation?: number;
  /** 自定义标签列表 */
  customTags?: string[];
  /** 资源描述 */
  description?: string;
  /** 文件Key */
  fileKey: string;
  /** 年级ID */
  gradeId: number;
  /** 资源ID */
  id?: number;
  /** 知识点ID列表 */
  knowledgePointIds?: number[];
  /** 资源类型ID */
  resourceTypeId: number;
  /** 分享状态：0-个人资源，1-分享为校本资源 */
  shareStatus: number;
  /** 学段ID */
  stageId: number;
  /** 状态：0-未发布，1-已发布 */
  status?: number;
  /** 学科ID */
  subjectId: number;
  /** 教材章节ID列表 */
  textbookChapterIds?: number[];
  /** 教材版本ID */
  textbookVersionId?: number;
  /** 教材册别ID */
  textbookVolumeId?: number;
  /** 资源标题 */
  title: string;
  /** 年份 */
  vintages?: number;
  toStudent: number;
  toTeacher: number;
}
export interface UpdateShareStatusParams {
  resourceId: number;
  /** 分享状态：0-个人资源，1-分享为校本资源 */
  shareStatus: number;
}
export interface DownloadParams {
  resourceIds: number[];
}
export interface BatchSaveParams {
  draftId?: number; //草稿箱Id
  draftType: number; //1个人 2租户
  resourceList: AddResourceParams[];
  status: number; //0保存为草稿 1草稿提交为资源
}
export interface DraftsClearParams {
  type: number; // 1个人；2租户
}
export interface DeleteDraftsParams {
  id: number; // 1个人；2租户
}
export interface DraftsListParams {
  pageNum: number;
  pageSize: number;
  type: number; // 1个人；2租户
}
export interface DraftsDetailParams {
  id: number;
}
export interface TeacherUserInfoResponse {
  username: string;
  account: string;
  avatar: string;
  stage: number;
  subjects: number[];
}

export interface DraftsListResponse {
  records: DraftItem[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

export interface DraftItem {
  id: number;
  tenantId: number;
  tmbId: number;
  type: number;
  title: string;
  resourceIds: string;
  resourceIdList: number[];
  resourceCount: number;
  createTime: string;
  updateTime: string;
}

export interface DraftsDetailResponse {
  draftInfo: DraftItem;
  resourceList: DraftResourceItem[];
}

export interface DraftResourceItem {
  id: number;
  tmbName: string;
  title: string;
  description: string;
  resourceTypeId: number;
  fileFormatId: number;
  fileFormatName: string;
  subjectId: number;
  gradeId: number;
  stageId: number;
  textbookVersionId: number;
  textbookVolumeId: number;
  textbookChapterIds: number[];
  textbookChapterNames: string[];
  areaId: number;
  areaCode: string;
  createUserName: string;
  attribution: number;
  attributionToId: number;
  attributionToName: string;
  vintages: number;
  shareStatus: number;
  downloadCount: number;
  viewCount: number;
  curation: number;
  fileKey: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileJson: string;
  cutQuestion: number;
  homeworkId: number;
  status: number;
  resourceTypeName: string;
  subjectName: string;
  stageName: string;
  gradeName: string;
  textbookVersionName: string;
  textbookVolumeName: string;
  areaName: string;
  createTime: number;
  updateTime: number | null;
  isCollected: number | null;
  isOwned: number;
  isOperable: number;
  knowledgePointIds: number[];
  knowledgePointNames: string[];
  customTags: string[];
  questionCount: number | null;
  isQuestionCollection: number;
  toStudent: number;
  toTeacher: number;
}

/** 供应商信息 */
export interface VendorItem {
  id: number;
  name: string;
  code: string;
  type: string;
  description: string;
}

/** 查询题目类型及关联答题形式（拆题）请求参数 */
export interface GetQuestionTypesWithFormatParams {
  stageId: number;
  subjectId: number;
  textbookVolumeId?: number;
}

/** 章节树 */
export interface ChapterTreeLeafItem {
  chapterId: number;
  chapterName: string;
}
export interface ChapterTreeItemResp {
  chapterId: number;
  chapterName: string;
  subChapter?: ChapterTreeLeafItem[];
}

/** 知识点树 */
export interface KnowledgeTreeLeafItem {
  knowledgeId: number;
  knowledgeName: string;
}
export interface KnowledgeTreeItemResp {
  knowledgeId: number;
  knowledgeName: string;
  subKnowledge?: KnowledgeTreeLeafItem[];
}

/** 题型-答题形式键值 */
export interface QuestionTypeFormatItem {
  key: number;
  value: string;
}

/** 题型列表（带 format 字段） */
export interface QuestionTypeWithFormatItem {
  format: number;
  id: number;
  name: string;
}

/** 查询题目类型及关联答题形式（拆题）响应 */
export interface GetQuestionTypesWithFormatResponse {
  chapterTree: ChapterTreeItemResp[];
  knowledgeTree: KnowledgeTreeItemResp[];
  questionTypeFormat: QuestionTypeFormatItem[];
  questionTypeList: QuestionTypeWithFormatItem[];
}
/** 批量增加资源拆题数据 - 章节信息 */
export interface BatchAddChapterItem {
  chapterId: string;
  chapterName: string;
}

/** 批量增加资源拆题数据 - 知识点信息 */
export interface BatchAddKnowledgeItem {
  knowledgeId: string;
  knowledgeName: string;
}

/** 批量增加资源拆题数据 - 题目解析器 */
export interface BatchAddQuestionParser {
  analysis: string;
  applicationAnswer: string;
  chapterList: BatchAddChapterItem[];
  difficulty: string;
  knowledgeList: BatchAddKnowledgeItem[];
  replenishAnswer: string[];
  selectAnswer: string;
  selectAnswerIndex: string[];
  subQuestionList: any[];
}

/** 批量增加资源拆题数据 - 题目信息 */
// export interface BatchAddQuestionItem {
//   applicationAnswer: string;
//   questionIndex: string;
//   questionParser: BatchAddQuestionParser;
//   questionTypeFormat: string;
//   questionTypeId: string;
//   questionTypeName: string;
//   replenishAnswer: string[];
//   selectAnswer: string;
//   selectAnswerIndex: string[];
//   selectOptions: string[];
//   stem: string;
//   subQuestionList: any[];
// }

/** 批量增加资源拆题数据请求参数 */
export interface BatchAddResourceQuestionsParams {
  // areaId?: number;
  // attribution: number;
  // chapterIds: number[];
  // fileKey: string;
  // gradeId: number;
  // knowledgeIds?: number[];

  // resourceId?: number;
  // resourceTypeId: number;
  // shareStatus: number;
  // stageId: number;
  // subjectId: number;
  // textbookVersionId?: number;
  // title: string;
  // vintages?: number;
  questionList: AiDisassemblyQuestion[];
  resourceId: number;
}
export interface UpdateQuestionStatusParams {
  questionStatus: number;
  resourceId: number;
}
/** 批量增加资源拆题数据响应 */
export type BatchAddResourceQuestionsResponse = number;
export interface ResourceQuestionsParams {
  groupId: string;
  resourceId: number;
}

export interface SaveQuestionGroupDraftParams {
  groupId: string;
  resourceId: number;
}
export interface ResourceQuestionsResponse {
  questionList: MyQuestionItem[];
  resource: ResourceInfo;
}
export interface ResourceInfo {
  id: number;
  tenantId: number;
  tmbName: string;
  title: string;
  description: string;
  resourceTypeId: number;
  fileFormatId: number;
  fileFormatName: string;
  subjectId: number;
  gradeId: number;
  stageId: number;
  textbookVersionId: number;
  textbookVolumeId: number;
  textbookChapterIds: number[]; // 空数组示例
  textbookChapterNames: string[]; // 空数组示例
  areaId: number;
  areaCode: string;
  createUserName: string;
  attribution: number;
  attributionToId: number;
  attributionToName: string;
  vintages: number;
  shareStatus: number;
  downloadCount: number;
  viewCount: number;
  curation: number;
  fileKey: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileJson: string; // 或更具体的解析接口
  cutQuestion: number;
  homeworkId: number;
  status: number;
  resourceTypeName: string;
  subjectName: string;
  stageName: string;
  gradeName: string;
  textbookVersionName: string;
  textbookVolumeName: string;
  areaName: string;
  createTime: string; // 或 Date 类型
  updateTime: string; // 或 Date 类型
  isCollected: number; // 或 boolean
  isOwned: number; // 或 boolean
  isOperable: number; // 或 boolean
  knowledgePointIds: number[]; // 空数组示例
  knowledgePointNames: string[]; // 空数组示例
  customTags: string[]; // 根据实际内容调整类型
  questionCount: number | null;
  isQuestionCollection: number; // 或 boolean
}
/** 删除题目请求参数 */
export interface DeleteResourceQuestionParams {
  id: number;
}
export interface ViewTrackParams {
  resourceId: number;
}
export interface CopyResourceQuestionsParams {
  resourceId: number;
}
export interface ResourceQuestionDBParams {
  resourceId: number;
}
