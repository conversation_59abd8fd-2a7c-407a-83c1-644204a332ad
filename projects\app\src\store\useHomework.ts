// 作业详情

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { HomeworkDetailResponse, HomeworkTypeDetailResponse } from '@/types/api/homeworkDetail';

interface HomeworkStore {
  homeworkDetail: HomeworkDetailResponse;
  setHomeworkDetail: (homeworkDetail: any) => void;
  homeworkTypeDetail: HomeworkTypeDetailResponse;
  setHomeworkTypeDetail: (homeworkTypeDetail: any) => void;
}

export const useHomeworkStore = create<HomeworkStore>()(
  devtools(
    immer((set) => ({
      homeworkDetail: {} as HomeworkDetailResponse,
      setHomeworkDetail: (homeworkDetail: any) => {
        set((state) => {
          state.homeworkDetail = homeworkDetail;
        });
      },
      homeworkTypeDetail: {} as HomeworkTypeDetailResponse,
      setHomeworkTypeDetail: (homeworkTypeDetail: any) => {
        set((state) => {
          state.homeworkTypeDetail = homeworkTypeDetail;
        });
      }
    }))
  )
);
