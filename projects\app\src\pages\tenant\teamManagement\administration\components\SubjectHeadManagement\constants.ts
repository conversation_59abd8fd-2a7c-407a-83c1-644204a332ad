// 学部分类关键词
export const DEPARTMENT_KEYWORDS = {
  PRIMARY: ['小学'],
  JUNIOR: ['初中', '七', '八', '九'],
  SENIOR: ['高中']
} as const;

// 表格配置
export const TABLE_CONFIG = {
  COLUMN_WIDTH: 179,
  FIXED_COLUMN_WIDTH: 128,
  ICON_SIZE: '16px'
} as const;

// 角色类型
export const ROLE_TYPES = {
  LEADER: 'leader',
  PHASE_LEADER: 'phase_leader', // 学段学科总负责人
  TEACHER: 'teacher'
} as const;

// 学段类型
export const PHASE_TYPES = {
  SCHOOL: '全校',
  PRIMARY: '小学部',
  JUNIOR: '初中部',
  SENIOR: '高中部',
  OTHER: '其他'
} as const;

// 弹窗标题
export const MODAL_TITLES = {
  SUBJECT_LEADER: '学科总负责人设置',
  PHASE_LEADER: '学段学科总负责人设置',
  TEACHER: '年级学科组长设置'
} as const;

// 存储键名
export const STORAGE_KEYS = {
  CURRENT_NODE_DATA: 'currentNodeData'
} as const;

// 默认值
export const DEFAULT_VALUES = {
  TEACH_TYPE: 0,
  SUBJECT_ID: 0
} as const;

// Next.js 页面组件默认导出
const ConstantsPage = () => {
  return null;
};
export default ConstantsPage;
