import SvgIcon from '@/components/SvgIcon';
import StudentListItem from './components/StudentListItem';
import HomeWorkContent from './components/HomeWorkContent';
import ScoreCardWithStatus from './components/RightCardContent';
import HomeworkInfoDisplay from './components/HomeworkInfoDisplay';
import CompositionCorrectionViewer from './components/CompositionCorrectionViewer';
import CompositionCorrectionResult from './components/CompositionCorrectionResult';
import { useTenantStore } from '@/store/useTenantStore';
import { vwDims } from '@/utils/chakra';
import { Box, Flex, Button, VStack, Image } from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { exportCorrectionResults } from '@/api/homeworkDetail';
import { downloadFile } from '@/utils/file';
import { useToast } from '@/hooks/useToast';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import styles from '@/pages/index.module.scss';

import { serviceSideProps } from '@/utils/i18n';
import {
  HomeworkCorrectionProvider,
  useHomeworkCorrection
} from './context/HomeworkCorrectionContext';
import MyBox from '@/components/common/MyBox';
import MySelect from '@/components/MySelect';
import {
  StudentHomeworkStatusEnum,
  TaskTypeEnum,
  StudentLevelEnum,
  StudentLevelLabelMap
} from '@/constants/api/homeworkManagement/homeworkCorrection';
import { CorrectMethodEnum } from '@/constants/api/homeworkManagement/homeworkCorrection';

// 内部组件，使用 Context
function CompositionCorrectionContent() {
  const { tenant } = useTenantStore();
  const {
    studentList,
    clazzList,
    selectedStudentId,
    selectedClazzId,
    filterStatus,
    filterLevel,
    studentHomeworkDetail,
    homeworkDetail,
    isLoadingStudentSubmission,
    isLoadingStudentHomeworkDetail,
    setSelectedStudent,
    setSelectedClazz,
    setFilterStatus,
    setFilterLevel,
    handleBatchConfirm,
    handleBatchReModify,
    handleBatchAiCorrection,
    mode
  } = useHomeworkCorrection();
  const [activeTab, setActiveTab] = useState<string>('first'); // Tab state
  const { toast } = useToast();
  const [isConfirming, setIsConfirming] = useState(false);

  // 当学生作业详情加载完成时，自动定位到最后一次提交
  useEffect(() => {
    if (studentHomeworkDetail?.submitList && studentHomeworkDetail.submitList.length > 0) {
      const lastSubmit =
        studentHomeworkDetail.submitList[studentHomeworkDetail.submitList.length - 1];
      if (lastSubmit?.id) {
        setActiveTab(lastSubmit.id.toString());
      }
    }
  }, [studentHomeworkDetail?.submitList]);

  // 直接使用原始学生数据，不进行转换
  const students = studentList;
  const noStudentSelected = !selectedStudentId || students.length === 0;

  const handleBatchReModifyWithConfirm = async () => {
    try {
      await promisifyConfirm({
        title: '批量重新批改',
        content:
          '将对当前筛选结果中“待确认”的学生作业重新发起AI批改，状态将变为待批改并重新开始。\n若存在手动评分题目，是否忽略已评分的题目继续？',
        okText: '继续',
        cancelText: '取消'
      });
      await handleBatchReModify();
      toast({ title: '已发起批量重新批改', status: 'success' });
    } catch (error) {
      if (error === false) return;
      toast({
        title: error instanceof Error ? error.message : '操作失败，请重试',
        status: 'error'
      });
    }
  };

  const handleBatchAiCorrectionWithConfirm = async () => {
    try {
      await promisifyConfirm({
        title: '一键AI批改',
        content:
          '将对当前筛选结果中“待批改”的学生发起AI批改任务。\nAI批改完成后，这些学生作业将流转为待确认状态。',
        okText: '开始批改',
        cancelText: '取消'
      });
      await handleBatchAiCorrection();
      toast({ title: '已发起AI批改任务', status: 'success' });
    } catch (error) {
      if (error === false) return;
      toast({
        title: error instanceof Error ? error.message : '操作失败，请重试',
        status: 'error'
      });
    }
  };

  function handleExportCorrectionResult() {
    console.log('导出批改结果', students);

    const studentHomeworkIds = students
      .filter((student) => student.status === StudentHomeworkStatusEnum.Completed)
      .map((student) => Number(student.id));

    if (studentHomeworkIds.length <= 0) {
      return toast({
        title: '没有可导出学生'
      });
    }

    // 使用downloadFile函数来处理文件下载
    downloadFile(() => exportCorrectionResults({ studentHomeworkIds }))
      .then(() => {
        console.log('批改结果导出成功');
      })
      .catch((error) => {
        console.error('导出失败:', error);
      });
  }

  // 批量确认包装函数
  const handleBatchConfirmWithToast = async () => {
    try {
      // 显示确认弹窗
      await promisifyConfirm({
        title: '批量确认',
        content:
          '确认批改结果后，学生作业状态将变为已完成。若学生提交方式为线上答题提交，系统将批改结果下发至学生端。',
        okText: '确认',
        cancelText: '取消'
      });

      setIsConfirming(true);
      await handleBatchConfirm();
      toast({
        title: '批量确认成功',
        status: 'success'
      });
    } catch (error) {
      // 如果是用户取消操作，不显示错误提示
      if (error === false) {
        return;
      }

      console.error('批量确认失败:', error);
      toast({
        title: error instanceof Error ? error.message : '批量确认失败，请重试',
        status: 'error'
      });
    } finally {
      setIsConfirming(false);
    }
  };

  // Handle student selection
  function handleSelectStudent(id: string) {
    setSelectedStudent(id);
  }

  return (
    <Box
      sx={{
        w: '100%',
        h: '100vh',
        backgroundImage: 'url(/imgs/app/homeworkBg.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        padding: `${vwDims(24)} ${vwDims(22)}`,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 作业信息显示组件 - 测试用 */}
      <HomeworkInfoDisplay />
      {/* 头部 */}
      <Box
        position={'relative'}
        display={'flex'}
        justifyContent={'space-between'}
        alignItems={'center'}
        paddingBottom={vwDims(21)}
        borderBottom={`1px solid #E0D5FF`}
        flexShrink={0}
      >
        {/* 这个在左边 */}
        <Box display={'flex'} alignItems={'center'} gap={vwDims(12)}>
          <Image
            src={tenant?.avatarUrl}
            h={vwDims(27)}
            w="auto"
            maxW="100%"
            objectFit="contain"
            alt=""
          />
          <Box
            whiteSpace="nowrap"
            fontSize={vwDims(18)}
            fontWeight="500"
            letterSpacing={vwDims(0.54)}
          >
            {tenant?.fullName || tenant?.name}
          </Box>
        </Box>
        {/* 这个居中 */}
        <Box
          display={'flex'}
          justifyContent={'center'}
          position={'absolute'}
          left={'50%'}
          transform={'translateX(-50%)'}
        >
          {mode === 'confirm' ? (
            <SvgIcon name="ConfirmTheCorrectionResult" width={vwDims(154)} height={vwDims(28)} />
          ) : (
            <SvgIcon name="CheckTheCorrectionResult" width={vwDims(154)} height={vwDims(28)} />
          )}
        </Box>
        {/* 这个在右边 */}
        <Box display={'flex'} gap={vwDims(10)}>
          {/* 根据批改方式显示不同的按钮 */}
          {homeworkDetail?.correctMethod === 2 ? (
            // 手动批改模式：显示一键AI批改
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              width={vwDims(130)}
              height={vwDims(36)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              bg={'#fff'}
              onClick={handleBatchAiCorrectionWithConfirm}
            >
              <SvgIcon name="homeworkEditIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              一键AI批改
            </Button>
          ) : (
            // AI批改模式：显示批量重新批改
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              width={vwDims(130)}
              height={vwDims(36)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              bg={'#fff'}
              onClick={handleBatchReModifyWithConfirm}
            >
              <SvgIcon name="homeworkEditIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              批量重新批改
            </Button>
          )}

          <Button
            variant={'outline'}
            fontWeight={500}
            fontSize={vwDims(14)}
            width={vwDims(130)}
            height={vwDims(36)}
            border={'1px solid #7D4DFF'}
            borderRadius={'8px'}
            color={'#7D4DFF'}
            bg={'#fff'}
            onClick={handleExportCorrectionResult}
          >
            <SvgIcon name="homeworkExportIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
            导出批改结果
          </Button>
          <Button
            colorScheme={'#7D4DFF'}
            onClick={handleBatchConfirmWithToast}
            isLoading={isConfirming}
            width={vwDims(130)}
            height={vwDims(36)}
            fontSize={vwDims(14)}
            loadingText="确认中..."
          >
            <SvgIcon name="homeworkConfirmIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
            批量确认
          </Button>
        </Box>
      </Box>
      {/* 内容 */}
      <Box display="flex" gap="20px" flex={1} mt={vwDims(24)} minHeight={0} minW={0}>
        {/* 左侧区域 */}
        <MyBox
          width={vwDims(247)}
          height="100%"
          bg="#fff"
          borderRadius={'8px'}
          isLoading={isLoadingStudentSubmission}
          text="正在加载学生列表..."
          display="flex"
          flexDirection="column"
        >
          {/* 固定的表单区域 */}
          <Box
            p={`${vwDims(16)} ${vwDims(10)} ${vwDims(16)} ${vwDims(10)}`}
            flexShrink={0}
            className={styles['my-form']}
          >
            <Box
              fontSize={vwDims(15)}
              fontWeight="500"
              color="#303133"
              mb={vwDims(8)}
              display={'flex'}
              alignItems={'center'}
            >
              <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
              学生列表
            </Box>

            <Flex gap={vwDims(10)} flexWrap={'wrap'}>
              {/* 班级选择器 */}
              <MySelect
                placeholder="选择班级"
                width={vwDims(108)}
                h={vwDims(30)}
                borderRadius={vwDims(8)}
                bg="#F2F3F5"
                border="none"
                value={selectedClazzId ?? 'all'}
                list={[
                  { label: '全部班级', value: 'all' },
                  ...clazzList.map((clazz) => ({
                    label: `${clazz.gradeName}${clazz.clazzName}`,
                    value: clazz.clazzId
                  }))
                ]}
                onchange={(val) => setSelectedClazz(val === 'all' ? null : Number(val))}
              />
              {/* 分层筛选器 */}
              {homeworkDetail?.taskType === TaskTypeEnum.LevelHomework && (
                <MySelect
                  placeholder="全部分层"
                  width={vwDims(108)}
                  h={vwDims(30)}
                  borderRadius={vwDims(8)}
                  bg="#F2F3F5"
                  border="none"
                  value={filterLevel ?? 'all'}
                  list={[
                    { label: '全部分层', value: 'all' },
                    {
                      label: StudentLevelLabelMap[StudentLevelEnum.Proficient],
                      value: StudentLevelEnum.Proficient
                    },
                    {
                      label: StudentLevelLabelMap[StudentLevelEnum.Basic],
                      value: StudentLevelEnum.Basic
                    },
                    {
                      label: StudentLevelLabelMap[StudentLevelEnum.Initial],
                      value: StudentLevelEnum.Initial
                    }
                  ]}
                  onchange={(val) => setFilterLevel(val === 'all' ? null : Number(val))}
                />
              )}
              {/* 状态筛选器 */}
              <MySelect
                placeholder="全部状态"
                width={vwDims(108)}
                h={vwDims(30)}
                borderRadius={vwDims(8)}
                bg="#F2F3F5"
                border="none"
                value={filterStatus ?? 'all'}
                list={[
                  { label: '全部状态', value: 'all' },
                  { label: '待提交', value: 0 },
                  { label: '待批改', value: 2 },
                  { label: 'AI批改中', value: 3 },
                  { label: '待确认', value: 4 },
                  { label: '已批改', value: 5 },
                  { label: '待订正', value: 6 },
                  { label: '批改失败', value: 7 }
                ]}
                onchange={(val) => setFilterStatus(val === 'all' ? null : Number(val))}
              />
            </Flex>
            <Box borderTop={'1px solid #E0D5FF'} mt={vwDims(16)}></Box>
          </Box>

          {/* 可滚动的学生列表区域 */}
          <Box
            flex={1}
            overflowY="auto"
            pr={vwDims(10)}
            pl={vwDims(10)}
            pb={vwDims(16)}
            css={{
              '&::-webkit-scrollbar': {
                width: '6px'
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent'
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#C9CDD4',
                borderRadius: '3px'
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#A8ACB3'
              }
            }}
          >
            <VStack spacing={vwDims(10)} align="stretch">
              {students.map((student) => (
                <StudentListItem
                  key={student.id}
                  student={student}
                  isSelected={selectedStudentId === student.id}
                  onClick={handleSelectStudent}
                />
              ))}
            </VStack>
          </Box>
        </MyBox>

        {/* 中间区域 */}
        <MyBox
          flex="1"
          height="100%"
          bg="#fff"
          borderRadius={'8px'}
          isLoading={isLoadingStudentHomeworkDetail && !noStudentSelected}
          text="正在加载作业内容..."
          display="flex"
          flexDirection="column"
          minW={0}
        >
          {noStudentSelected ? (
            <Box
              flex={1}
              display="flex"
              alignItems="center"
              justifyContent="center"
              color="#86909C"
              fontSize={vwDims(14)}
              tabIndex={0}
              aria-label="请选择学生进行查看"
            >
              请选择学生进行查看
            </Box>
          ) : studentHomeworkDetail?.taskType === TaskTypeEnum.WritingTask ? (
            <CompositionCorrectionViewer />
          ) : (
            <HomeWorkContent activeTab={activeTab} onTabChange={setActiveTab} />
          )}
        </MyBox>

        {/* 右侧区域 */}
        <MyBox
          width={vwDims(397)}
          height="100%"
          bg="#fff"
          borderRadius={'8px'}
          p={
            noStudentSelected
              ? vwDims(20)
              : studentHomeworkDetail?.taskType === TaskTypeEnum.WritingTask
                ? vwDims(0)
                : vwDims(20)
          }
          bgImage={
            !noStudentSelected && studentHomeworkDetail?.taskType === TaskTypeEnum.WritingTask
              ? '/imgs/homework/composition_right_bg.png'
              : ''
          }
          bgSize="100% 100%"
          bgPosition="center"
          bgRepeat="no-repeat"
          border={
            !noStudentSelected && studentHomeworkDetail?.taskType === TaskTypeEnum.WritingTask
              ? '1px solid #fff'
              : 'none'
          }
        >
          {noStudentSelected ? (
            <Box
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              color="#86909C"
              fontSize={vwDims(14)}
              tabIndex={0}
              aria-label="请选择学生进行查看"
            >
              请选择学生进行查看
            </Box>
          ) : studentHomeworkDetail?.taskType === TaskTypeEnum.WritingTask ? (
            <CompositionCorrectionResult />
          ) : (
            <ScoreCardWithStatus />
          )}
        </MyBox>
      </Box>
    </Box>
  );
}

// 主组件，包装 Provider
function CompositionCorrection() {
  return (
    <HomeworkCorrectionProvider>
      <CompositionCorrectionContent />
    </HomeworkCorrectionProvider>
  );
}

export default CompositionCorrection;

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
