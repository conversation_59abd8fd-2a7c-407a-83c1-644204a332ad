export interface SceneType {
  id: string;
  tenantId?: string;
  tmbId: string;
  name: string;
  avatarUrl: string;
  sort: number;
  sceneId: string;
  createTime: string;
}

export type GetSceneList = {
  tenantId?: string;
  name?: string;
  permission?: number;
};
export interface SceneCreateParams {
  createUsername?: string;
  tenantId?: string;
  name?: string;
  permission?: number;
  sceneId?: number;
  sort?: number;
}
export interface SceneUpdateParams {
  id: string;
  createUsername?: string;
  tenantId?: string;
  name?: string;
  permission?: number;
  sceneId?: number;
  sort?: number;
}
type sortParam = {
  id: number;
  sort: number;
};
export interface SceneSortParams {
  param: sortParam[];
}

export interface SubSceneType {
  id: string;
  isDeleted: string;
  tenantId?: string;
  tmbId: string;
  name: string;
  avatarUrl: string;
  sort: number;
  tenantSceneId: string;
  createTime: string;
}

export interface GetSubSceneListParams {
  tenantSceneId: string;
  name?: string;
}

export interface SubSceneCreateParams {
  createUsername: string;
  name: string;
  sort: number;
  tenantId: number;
  tenantSceneId: number;
  tenantId: string;
}
export interface SubSceneUpdateParams {
  id: string;
  createUsername: string;
  name: string;
  sort: number;
  tenantId: number;
  tenantSceneId: number;
}
type sortParam = {
  id: number;
  sort: number;
};
export interface SubSceneSortParams {
  param: sortParam[];
}
