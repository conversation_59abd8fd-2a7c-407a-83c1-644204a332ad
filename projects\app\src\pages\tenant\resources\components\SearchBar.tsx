import {
  Box,
  Button,
  Flex,
  Input,
  InputGroup,
  InputRightElement,
  IconButton
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { Select } from 'antd';
import { useState, useEffect } from 'react';
import { SearchBarProps } from '@/components/MyTable/types';
import { ResourceTypeItem } from '@/types/personalCenter';
import { TenantResourcesParams, TenantUser } from '@/types/api/tenant/tenant';
import { getResourceTypeList } from '@/api/personalCenter';
import { getTenantUserPage } from '@/api/tenant';
import DataPick from './dataPick';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import styles from '@/pages/cloud/cloud.module.scss';

// 扩展搜索参数类型
interface ExtendedTenantResourcesParams extends TenantResourcesParams {
  uploadTime?: string;
}

const SearchBar = ({
  onSearch,
  query,
  defaultQuery,
  tableInstance
}: SearchBarProps<ExtendedTenantResourcesParams>) => {
  const [searchValue, setSearchValue] = useState(query?.title || '');
  const [selectedUploader, setSelectedUploader] = useState<number | undefined>();
  const [selectedResourceType, setSelectedResourceType] = useState<number | undefined>();
  const [selectedStatus, setSelectedStatus] = useState<number | 0>();
  const [selectedDateRange, setSelectedDateRange] = useState<[Dayjs, Dayjs] | null>(null);
  const [resourceTypeOptions, setResourceTypeOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [tenantUsers, setTenantUsers] = useState<TenantUser[]>([]);
  const [filteredUploaderOptions, setFilteredUploaderOptions] = useState<
    { label: string; value: number }[]
  >([]);

  // 获取租户用户数据
  const fetchTenantUsers = async () => {
    try {
      const response = await getTenantUserPage({
        current: 1,
        size: 999,
        searchKey: '',
        searchType: '',
        status: ''
      });
      console.log('获取租户用户成功:', response);

      // getTenantUserPage 返回分页数据，需要取 records 字段
      if (response?.records && Array.isArray(response.records) && response.records.length > 0) {
        // 将 TenantUserType 转换为 TenantUser 格式
        const tenantUserList = response.records.map((user) => ({
          attributionId: parseInt(user.id),
          attributionToName: user.username
        }));
        
        setTenantUsers(tenantUserList);
        // 转换为选项格式：显示名称(账号)，值为ID
        const options = tenantUserList.map((user) => ({
          label: `${user.attributionToName}`,
          value: user.attributionId
        }));
        setFilteredUploaderOptions(options);
        console.log('转换后的选项:', options);
      } else {
        console.log('API返回空数组或无效数据');
        setTenantUsers([]);
        setFilteredUploaderOptions([]);
      }
    } catch (error) {
      console.error('获取租户用户失败:', error);
    }
  };

  // 本地搜索过滤上传人选项
  const handleUploaderSearch = (searchText: string) => {
    if (!searchText) {
      // 如果没有搜索文本，显示所有选项
      const options = tenantUsers.map((user) => ({
        label: `${user.attributionToName}(${user.attributionId})`,
        value: user.attributionId
      }));
      setFilteredUploaderOptions(options);
    } else {
      // 根据搜索文本过滤选项
      const filtered = tenantUsers
        .filter(
          (user) =>
            user.attributionToName.toLowerCase().includes(searchText.toLowerCase()) ||
            String(user.attributionId).includes(searchText)
        )
        .map((user) => ({
          label: `${user.attributionToName}(${user.attributionId})`,
          value: user.attributionId
        }));
      setFilteredUploaderOptions(filtered);
    }
  };

  // 状态选项
  const statusOptions = [
    { label: '上架', value: 1 },
    { label: '下架', value: 2 }
  ];

  // 获取资源类型列表
  useEffect(() => {
    const fetchResourceTypes = async () => {
      try {
        const response = await getResourceTypeList();
        const options = response.map((item: ResourceTypeItem) => ({
          label: item.name,
          value: parseInt(item.id)
        }));
        setResourceTypeOptions(options);
      } catch (error) {
        console.error('获取资源类型失败:', error);
      }
    };

    fetchResourceTypes();
    fetchTenantUsers();
  }, []);

  const handleSearch = () => {
    const params: any = {
      title: searchValue,
      resourceTypeId: selectedResourceType || defaultQuery?.resourceTypeId || 0,
      status: selectedStatus || 0,
      // 处理日期范围
      startTime: selectedDateRange ? selectedDateRange[0].format('YYYY-MM-DD') : '',
      endTime: selectedDateRange ? selectedDateRange[1].format('YYYY-MM-DD') : '',
      // 将选择的上传人ID传递给tmbIds
      tmbIds: selectedUploader ? [selectedUploader] : []
      // 移除分页参数，让MyTable自己管理
    };

    console.log('搜索参数:', params);
    if (typeof onSearch === 'function') {
      onSearch(params);
    }
  };

  const handleReset = () => {
    setSearchValue('');
    setSelectedUploader(undefined);
    setSelectedResourceType(undefined);
    setSelectedStatus(0);
    setSelectedDateRange(null);

    const params: any = {
      title: '',
      status: 0,
      resourceTypeId: defaultQuery?.resourceTypeId || 0,
      startTime: '',
      endTime: '',
      // 重置时清空tmbIds
      tmbIds: []
      // 移除分页参数，让MyTable自己管理
    };

    console.log('重置参数:', params);
    if (typeof onSearch === 'function') {
      onSearch(params);
    }
  };

  useEffect(() => {
    setSearchValue(query?.title || '');
    setSelectedResourceType((query as any)?.resourceTypeId || undefined);
    setSelectedStatus((query as any)?.status || 0);

    // 处理上传人选择：从tmbIds数组中取第一个值
    const tmbIds = (query as any)?.tmbIds;
    if (tmbIds && Array.isArray(tmbIds) && tmbIds.length > 0) {
      setSelectedUploader(tmbIds[0]);
    } else {
      setSelectedUploader(undefined);
    }

    // 处理日期范围
    const startTime = (query as any)?.startTime;
    const endTime = (query as any)?.endTime;
    if (startTime && endTime) {
      setSelectedDateRange([dayjs(startTime), dayjs(endTime)]);
    } else {
      setSelectedDateRange(null);
    }
  }, [query]);

  return (
    <Flex gap="0" alignItems="center" mb="16px" flexWrap="wrap" w="100%">
      <Select
        placeholder="请选择上传人"
        value={selectedUploader || undefined}
        onChange={(val) => setSelectedUploader(val)}
        onSearch={handleUploaderSearch}
        showSearch
        filterOption={false}
        style={{
          width: '200px',
          borderRadius: '8px',
          height: '40px',
          marginLeft: '14px',
          background: 'rgba(0,0,0,0.03)',
          border: 'none'
        }}
        className={styles['tenant-select']}
        options={filteredUploaderOptions}
        allowClear
        notFoundContent="暂无数据"
      />

      <Select
        placeholder="请选择资源类型"
        value={selectedResourceType || undefined}
        onChange={(val) => setSelectedResourceType(val)}
        style={{
          width: '200px',
          borderRadius: '8px',
          height: '40px',
          marginLeft: '14px',
          background: 'rgba(0,0,0,0.03)',
          border: 'none'
        }}
        className={styles['tenant-select']}
        options={resourceTypeOptions}
        allowClear
      />

      <Select
        placeholder="请选择状态"
        value={selectedStatus || undefined}
        onChange={(val) => setSelectedStatus(val)}
        style={{
          width: '200px',
          borderRadius: '8px',
          height: '40px',
          marginLeft: '14px',
          background: 'rgba(0,0,0,0.03)',
          border: 'none'
        }}
        className={styles['tenant-select']}
        options={statusOptions}
        allowClear
      />

      <Box ml="14px">
        <DataPick
          value={selectedDateRange || undefined}
          onChange={(dates) => setSelectedDateRange(dates)}
        />
      </Box>

      <InputGroup w="200px" ml="14px">
        <Input
          placeholder="搜索文件"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyDown={(e) => {
            e.key === 'Enter' && handleSearch();
          }}
          style={{
            borderRadius: '8px',
            height: '40px',
            background: 'rgba(0,0,0,0.03)',
            border: 'none'
          }}
        />
        <InputRightElement height="40px">
          <IconButton
            aria-label="搜索"
            icon={<SearchIcon />}
            size="sm"
            variant="ghost"
            onClick={handleSearch}
          />
        </InputRightElement>
      </InputGroup>

      <Button
        ml="14px"
        h="40px"
        px="16px"
        colorScheme="blue"
        borderRadius="8px"
        onClick={handleSearch}
      >
        查询
      </Button>

      <Button
        ml="14px"
        h="40px"
        px="16px"
        variant="outline"
        borderRadius="8px"
        onClick={handleReset}
      >
        重置
      </Button>
    </Flex>
  );
};

export default SearchBar;
