import { getSystemInitData } from '@/api/system';
import { delay } from '@/utils/tools';

import { useSystemStore } from '../store/useSystemStore';
import { FastGPTFeConfigsType } from '@/fastgpt/global/common/system/types';

// 为全局Window对象添加自定义属性类型
declare global {
  interface Window {
    __RUNTIME_CONFIG__?: Partial<FastGPTFeConfigsType>;
  }
}

// 从JSON配置文件加载运行时配置
const loadRuntimeConfig = async (): Promise<Partial<FastGPTFeConfigsType>> => {
  try {
    // 尝试直接请求配置文件
    const response = await fetch('/config/runtime-config.json?t=' + new Date().getTime());
    if (response.ok) {
      const config = await response.json();
      return config;
    } else {
      console.warn('加载运行时配置失败，使用默认配置');
      return {};
    }
  } catch (err) {
    console.error('加载运行时配置失败:', err);
    return {};
  }
};

export const initSystemData = async (
  retry = 3
): Promise<{
  feConfigs: FastGPTFeConfigsType;
}> => {
  try {
    const res = await getSystemInitData();

    // 加载运行时配置并合并
    const runtimeConfig = await loadRuntimeConfig();
    if (Object.keys(runtimeConfig).length > 0) {
      res.feConfigs = {
        ...res.feConfigs,
        ...runtimeConfig
      };
    }

    useSystemStore.getState().initStaticData(res);
    useSystemStore.getState().getSystemConfig();
    return {
      feConfigs: res.feConfigs
    };
  } catch (error) {
    if (retry > 0) {
      await delay(500);
      return initSystemData(retry - 1);
    }
    return Promise.reject(error);
  }
};
