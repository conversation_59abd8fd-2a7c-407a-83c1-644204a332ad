import { RawInputType } from './../components/ChatBox/MessageInputMini';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import {
  delChatHistoryById,
  getChatHistories,
  clearChatHistoryByAppId,
  delChatRecordById,
  chatUpdate,
  cleanChat
} from '@/api/chat';
import {
  ChatFileType,
  CleanChatProps,
  ClearHistoriesProps,
  DelHistoryProps,
  DeleteChatItemProps,
  InitChatResponse,
  UpdateHistoryProps,
  getHistoriesProps,
  DynamicFormDataType
} from '@/types/api/chat';
import { defaultChatData } from '@/constants/chat';
import { ChatHistoryItemType, ChatSiteItemType } from '@/fastgpt/global/core/chat/type';
import { ClientAppFormDetailType } from '@/types/api/app';
import { AppContextDetailType } from '@/fastgpt/global/core/dataset/type';
import { AppListItemType } from '@/types/api/app';

enum ViewType {
  Agent = 'Agent',
  Workflow = 'Workflow'
}

type State = {
  histories: ChatHistoryItemType[];
  historiesProps?: getHistoriesProps;
  historiesTotal?: number;
  loadHistories: (data: getHistoriesProps) => Promise<null>;
  loadMoreHistories: () => Promise<null>;
  delOneHistory: (data: DelHistoryProps) => Promise<void>;
  clearHistories: (data: ClearHistoriesProps) => Promise<void>;
  pushHistory: (history: UpdateHistoryProps) => void;
  updateHistory: (e: UpdateHistoryProps) => Promise<any>;
  chatData: InitChatResponse;
  setChatData: (e: InitChatResponse | ((e: InitChatResponse) => InitChatResponse)) => void;
  lastChatAppId: string;
  setLastChatAppId: (id: string) => void;
  lastChatId: string;
  setLastChatId: (id: string) => void;
  delOneHistoryItem: (e: DeleteChatItemProps) => Promise<boolean>;
  chatFiles: ChatFileType[];
  setChatFiles: (data: ChatFileType[]) => void;
  chatHistory: ChatSiteItemType[];
  setChatHistory: (data: ChatSiteItemType[]) => void;
  cleanChat: (data: CleanChatProps) => Promise<void>;

  chatId: string | undefined;
  setChatId: (id: string | undefined) => void;

  formData: DynamicFormDataType;
  setFormData: (data: DynamicFormDataType) => void;
  clientAppFormDetail: ClientAppFormDetailType;
  setClientAppFormDetail: (data: ClientAppFormDetailType) => void;
  isAppListVisible: boolean;
  setIsAppListVisible: (visible: boolean) => void;
  questionFormGuideStep: number;
  setQuestionFormGuideStep: (step: number) => void;
  viewType: ViewType; // 添加 viewType 属性
  setViewType: (viewType: ViewType) => void; // 添加 setViewType 方法
  currentStep: number; // 添加新状态
  getCurrentStep: () => number; // 添加获取方法
  setCurrentStep: (step: number) => void; // 添加设置方法
  hasCompletedGuidance: boolean | null;
  setHasCompletedGuidance: (value: boolean | null) => void;
  allIsDisplayeZero: boolean; //判断sceneList的isDisplayed是否都为0
  setAllIsDisplayeZero: (value: boolean) => void;
  initChatInputs?: RawInputType | string;
  setInitChatInputs: (chatInputs?: RawInputType) => void;
  appContextDetail: AppContextDetailType | undefined;
  setAppContextDetail: (appContextDetail: AppContextDetailType) => void;
  currentChatApp: AppListItemType | undefined;
  setCurrentChatApp: (app: AppListItemType | undefined) => void;
};

export const useChatStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        lastChatAppId: '',
        setLastChatAppId(id: string) {
          set((state) => {
            state.lastChatAppId = id;
          });
        },
        lastChatId: '',
        setLastChatId(id: string) {
          set((state) => {
            state.lastChatId = id;
          });
        },
        chatId: undefined,
        setChatId(id: string | undefined) {
          set((state) => {
            state.chatId = id;
          });
        },
        histories: [],
        chatFiles: [],
        chatHistory: [],
        initChatInputs: undefined,
        setInitChatInputs(chatInputs?: RawInputType) {
          set((state) => {
            state.initChatInputs = chatInputs;
          });
        },
        formData: {}, // 初始化 formData
        setFormData(data: DynamicFormDataType) {
          set((state) => {
            state.formData = data;
          });
        },
        clientAppFormDetail: {} as ClientAppFormDetailType, // 初始化 clientAppFormDetail
        setClientAppFormDetail(data: ClientAppFormDetailType) {
          set((state) => {
            state.clientAppFormDetail = data;
          });
        },
        isAppListVisible: true, // 初始化 isAppListVisible
        setIsAppListVisible(visible: boolean) {
          set((state) => {
            state.isAppListVisible = visible;
          });
        },
        questionFormGuideStep: 1, // 初始化 questionFormGuideStep
        setQuestionFormGuideStep(step: number) {
          set((state) => {
            state.questionFormGuideStep = step;
          });
        },

        viewType: ViewType.Agent, // 初始化 viewType
        setViewType(viewType: ViewType) {
          set((state) => {
            state.viewType = viewType;
          });
        },
        currentStep: 0,
        getCurrentStep: () => get().currentStep,
        setCurrentStep: (step: number) => {
          set((state) => {
            state.currentStep = step;
          });
        },
        async loadHistories(e: getHistoriesProps): Promise<null> {
          const props = { ...e, current: 1, size: e.size || 20 };
          const data = await getChatHistories(props);
          set((state) => {
            state.histories = data.records || [];
            state.historiesProps = props;
            state.historiesTotal = data.total;
          });
          return null;
        },
        async loadMoreHistories(): Promise<null> {
          const total = get().historiesTotal;
          if (total !== undefined && get().histories.length >= total) {
            return null;
          }
          const state = get();
          const oldProps = state.historiesProps || { size: 20 };
          const props = {
            ...oldProps,
            current: Math.floor(state.histories.length / oldProps.size!) + 1
          };
          const data = await getChatHistories(props);
          set((state) => {
            const histories = Array.isArray(state.histories) ? [...state.histories] : [];
            data.records.length > 0
              ? data.records.forEach(
                  (it) =>
                    !histories?.some(({ chatId }) => it.chatId === chatId) && histories.push(it)
                )
              : [];
            state.histories = histories;
            state.historiesTotal = data.total;
            state.historiesProps = props;
          });
          return null;
        },
        async delOneHistory(props: DelHistoryProps): Promise<void> {
          set((state) => {
            state.histories = state.histories.filter((item) => item.chatId !== props.chatId);
          });
          await delChatHistoryById(props);
        },
        async clearHistories(data: ClearHistoriesProps): Promise<void> {
          set((state) => {
            state.histories = data
              ? state.histories.filter((item) => item.chatId !== data.appId)
              : [];
          });
          await clearChatHistoryByAppId(data);
        },
        pushHistory(history: UpdateHistoryProps) {
          set((state) => {
            state.histories = [history, ...state.histories];
          });
        },
        setChatFiles(chatFiles: ChatFileType[]) {
          set((state) => {
            state.chatFiles = chatFiles;
          });
        },
        setChatHistory(data: ChatSiteItemType[]) {
          set((state) => {
            state.chatHistory = data;
          });
        },
        async updateHistory(props: UpdateHistoryProps): Promise<any> {
          const { chatId, title } = props;

          if (title !== undefined) {
            try {
              await chatUpdate({ chatId, title });
            } catch {}
          }

          const index = get().histories.findIndex((item) => item.chatId === chatId);

          if (index > -1) {
            const newHistory = {
              ...get().histories[index],
              ...(title && { title })
            };
            set((state) => {
              const newHistories = (() => {
                return [
                  newHistory,
                  ...get().histories.slice(0, index),
                  ...get().histories.slice(index + 1)
                ];
              })();

              state.histories = newHistories;
            });
          } else {
            get().loadHistories(get().historiesProps || {});
          }
        },
        chatData: defaultChatData,
        setChatData(e = defaultChatData) {
          if (typeof e === 'function') {
            set((state) => {
              state.chatData = e(state.chatData);
            });
          } else {
            set((state) => {
              state.chatData = e;
            });
          }
        },
        async delOneHistoryItem({ ...props }: DeleteChatItemProps): Promise<boolean> {
          const { chatId, contentId } = props;
          if (!chatId || !contentId) return false;

          try {
            get().setChatData((state) => ({
              ...state,
              history: state.history.filter((item) => item.dataId !== contentId)
            }));
            return (await delChatRecordById(props)) === null;
          } catch (err) {
            console.log(err);
          }
          return false;
        },
        async cleanChat(data: CleanChatProps): Promise<void> {
          await cleanChat(data);
          get().setChatData((state) =>
            state.chatId === data.chatId ? { ...state, history: [] } : state
          );
        },
        hasCompletedGuidance: null,
        setHasCompletedGuidance(value: boolean | null) {
          set((state) => {
            state.hasCompletedGuidance = value;
          });
        },
        allIsDisplayeZero: false,
        setAllIsDisplayeZero(value: boolean) {
          set((state) => {
            state.allIsDisplayeZero = value;
          });
        },
        appContextDetail: undefined,
        setAppContextDetail(appContextDetail: AppContextDetailType) {
          set((state) => {
            state.appContextDetail = appContextDetail;
          });
        },
        currentChatApp: undefined,
        setCurrentChatApp(app: AppListItemType | undefined) {
          set((state) => {
            state.currentChatApp = app;
          });
        }
      })),
      {
        name: 'chatStore',
        partialize: (state) => ({
          lastChatAppId: state.lastChatAppId,
          lastChatId: state.lastChatId,
          formData:
            state.formData && Object.keys(state.formData).length > 0
              ? Object.fromEntries(Object.entries(state.formData).slice(0, 10))
              : {},
          isAppListVisible: state.isAppListVisible,
          questionFormGuideStep: state.questionFormGuideStep,
          viewType: state.viewType,
          currentStep: state.currentStep,
          hasCompletedGuidance: state.hasCompletedGuidance,
          chatId: state.chatId,
          allIsDisplayeZero: state.allIsDisplayeZero
        })
      }
    )
  )
);
