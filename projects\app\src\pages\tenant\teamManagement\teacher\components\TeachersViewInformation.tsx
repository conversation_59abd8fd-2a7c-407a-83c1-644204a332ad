import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Text,
  Divider
} from '@chakra-ui/react';
import type { TeacherPageItem } from '@/types/api/teacher';
import {
  SCHOOL_MANAGE_TEACHER_TYPE_MAP,
  SCHOOL_SUBJECT_TEACHER_TYPE_MAP
} from '@/constants/teacher';
import MyModal from '@/components/MyModal';
import MyInput from '@/components/MyInput';
import MyBox from '@/components/common/MyBox';

interface TeachersViewInformationProps {
  teacher?: TeacherPageItem;
  onClose?: () => void;
  isOpen?: boolean;
}

const TeachersViewInformation = ({
  teacher,
  onClose = () => {},
  isOpen = true
}: TeachersViewInformationProps) => {
  if (!teacher) return null;

  // 处理性别显示
  const getGenderText = (gender: number) => {
    if (gender === 1) return '男';
    if (gender === 2) return '女';
    return '未定义';
  };

  // 处理行政职务显示
  const getAdminJobsText = (jobs: string[]) => {
    if (!jobs || jobs.length === 0) return '无';
    return jobs
      .map(job => {
        if (typeof job === 'string') return job;
        if (typeof job === 'number') return SCHOOL_MANAGE_TEACHER_TYPE_MAP[String(job)] || `职务类型${job}`;
        return String(job);
      })
      .join('、');
  };

  // 处理教学职务显示
  const getTeachJobsText = (jobs: string[]) => {
    if (!jobs || jobs.length === 0) return '无';
    return jobs
      .map(job => {
        if (typeof job === 'string') return job;
        if (typeof job === 'number') return SCHOOL_SUBJECT_TEACHER_TYPE_MAP[job] || `职务类型${job}`;
        return String(job);
      })
      .join('、');
  };

  return (
    <MyModal isOpen={isOpen} onClose={onClose} title="查看信息" w="684px" maxH="748px">
      <MyBox p={6} overflow="auto" w="100%" flex={1} maxH="calc(748px - 80px)">
        {/* 基本信息 */}
        <Flex gap={4} mb={3}>
          <FormControl flex={1}>
            <FormLabel fontSize="14px" mb={1} color="#4E5969">
              * 姓名：
            </FormLabel>
            <MyInput
              value={teacher.name || ''}
              isDisabled
              bg="#F2F3F5"
              size="sm"
              borderRadius="md"
              border="none"
            />
          </FormControl>
          <FormControl flex={1}>
            <FormLabel fontSize="14px" mb={1} color="#4E5969">
              * 账号：
            </FormLabel>
            <MyInput
              value={teacher.account || ''}
              isDisabled
              bg="#F2F3F5"
              size="sm"
              borderRadius="md"
              border="none"
            />
            <Text color="gray.400" fontSize="12px" mt={1}>
              需从现有成员管理账号中选取
            </Text>
          </FormControl>
        </Flex>

        <Flex gap={4} mb={4}>
          <FormControl flex={1}>
            <FormLabel fontSize="14px" mb={1} color="#4E5969">
              * 手机号：
            </FormLabel>
            <MyInput
              value={teacher.phone || ''}
              isDisabled
              bg="#F2F3F5"
              size="sm"
              borderRadius="md"
              border="none"
            />
          </FormControl>
          <FormControl flex={1}>
            <FormLabel fontSize="14px" mb={1} color="#4E5969">
              * 性别：
            </FormLabel>
            <MyInput
              value={getGenderText(teacher.gender)}
              isDisabled
              bg="#F2F3F5"
              size="sm"
              borderRadius="md"
              border="none"
            />
          </FormControl>
        </Flex>

        {/* 行政职务 */}
        <Text fontWeight="bold" mt={6} mb={3} fontSize="16px" color="#1D2129">
          行政职务
        </Text>
        <FormControl mb={4}>
          <MyInput
            value={getAdminJobsText(teacher.schoolManageTeachers || [])}
            isDisabled
            bg="#F2F3F5"
            size="sm"
            borderRadius="md"
            border="none"
            minH="40px"
          />
        </FormControl>

        {/* 教学职务 */}
        <Text fontWeight="bold" mt={6} mb={3} fontSize="16px" color="#1D2129">
          教学职务
        </Text>
        <FormControl mb={4}>
          <MyInput
            value={getTeachJobsText(teacher.schoolSubjectTeachers || [])}
            isDisabled
            bg="#F2F3F5"
            size="sm"
            borderRadius="md"
            border="none"
            minH="40px"
          />
        </FormControl>

        {/* 底部按钮 */}
        <Divider my={6} />
        <Flex justify="flex-end" gap={4}>
          <Button colorScheme="purple" onClick={onClose}>
            确认
          </Button>
        </Flex>
      </MyBox>
    </MyModal>
  );
};

export default TeachersViewInformation;
