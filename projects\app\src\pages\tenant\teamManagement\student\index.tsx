import React, { useState, useRef } from 'react';
import { Button, Flex, useDisclosure } from '@chakra-ui/react';
import { serviceSideProps } from '@/utils/i18n';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { getStudentPage, changeStudentStatus } from '@/api/student';
import type { StudentPageParams, StudentPageItem } from '@/types/student';
import PageContainer from '@/components/PageContainer';
import MyBox from '@/components/common/MyBox';
import StudentList from './components/StudentList';
import ImportPanel from '@/components/ImportPanel';
import SearchBar from './components/SearchBar';
import AddStudentModal from './components/AddStudentModal/index';
import ChangeClassModal from './components/ChangeClassModal/index';
import StudentEdit from './components/StudentEdit';
import { vwDims } from '@/utils/chakra';
import type { MyTableRef } from '@/components/MyTable/types';
import SvgIcon from '@/components/SvgIcon';

const Student = () => {
  const [studentId, setStudentId] = useState('');
  const [mode, setMode] = useState('');
  const [studentInfo, setStudentInfo] = useState({
    name: '',
    gradeName: '',
    clazzName: ''
  });

  // 弹窗状态管理
  const {
    isOpen: isOpenAddStudentModal,
    onOpen: onOpenAddStudentModal,
    onClose: onCloseAddStudentModal
  } = useDisclosure();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // 搜索和刷新状态
  const [searchParams, setSearchParams] = useState({});
  const [refreshKey, setRefreshKey] = useState(0);
  const tableInstanceRef = useRef<MyTableRef<StudentPageParams, StudentPageItem>>(null);

  // 搜索处理
  const handleSearch = (params: any) => {
    setSearchParams(params);
  };

  // 刷新处理
  const handleRefresh = () => {
    console.log('=== STUDENT REFRESH TRIGGERED ===');
    setRefreshKey((k) => {
      const newKey = k + 1;
      console.log('=== STUDENT REFRESH KEY UPDATED ===', newKey);
      return newKey;
    });
  };

  // 添加学生
  const onAdd = () => {
    setStudentId('');
    onOpenAddStudentModal();
    setMode('');
  };

  // 编辑学生
  const onEdit = (id: string) => {
    setStudentId(id);
    setMode('edit');
    setIsEditModalOpen(true);
  };

  // 查看学生
  const onView = (id: string) => {
    setStudentId(id);
    onOpenAddStudentModal();
    setMode('view');
  };

  // 转移资源
  const onTransferResources = (row: any) => {
    setStudentId(row.id);
    setStudentInfo({
      name: row.name,
      gradeName: row.gradeName,
      clazzName: row.clazzName
    });
    onOpen();
  };

  // 关闭添加学生弹窗
  const handleCloseAddStudentModal = (submited: boolean, tenantId?: string) => {
    onCloseAddStudentModal();
    if (submited) {
      handleRefresh();
    }
  };

  // 关闭编辑弹窗
  const handleEditClose = (submited: boolean) => {
    setIsEditModalOpen(false);
    if (submited) {
      handleRefresh();
    }
  };

  // 设置学生状态
  const onSetStatus = (id: string, status: number) => {
    let title, content;
    switch (status) {
      case 1:
        title = '恢复提示';
        content = '确定要恢复该学生的状态吗？';
        break;
      case 2:
        title = '失效提示';
        content = '学生设置失效后，将不参与评价，确定将学生设置为失效吗？';
        break;
      case 3:
        title = '毕业提示';
        content = '学生设置毕业状态后，将不可恢复，确定设置为毕业状态？';
        break;
      default:
        return;
    }

    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        try {
          await changeStudentStatus({ id, status });
          Toast.success('操作成功');
          handleRefresh();
        } catch (error) {
          Toast.error('操作失败');
          console.error(error);
        }
      }
    });
  };

  // 上传成功处理
  const handleUploadSuccess = () => {
    console.log('=== STUDENT UPLOAD SUCCESS - REFRESHING LIST ===');
    handleRefresh();
  };

  return (
    <PageContainer
      w={vwDims(1668)}
      h={vwDims(1048)}
      top={vwDims(14)}
      left={vwDims(236)}
      opacity={1}
      borderRadius={vwDims(16)}
      pageBgColor="#FFFFFF"
      boxShadow="0px 0px 18.7px 0px #0000000F"
      overflow="hidden"
      p={4}
    >
      {/* 页面标题 */}
      <MyBox
        w={vwDims(72)}
        h={vwDims(22)}
        position="absolute"
        top={vwDims(41)}
        left={vwDims(46)}
        opacity={1}
        fontFamily="PingFang SC"
        fontWeight={500}
        fontStyle="normal"
        fontSize={vwDims(18)}
        lineHeight={vwDims(22)}
        letterSpacing="0%"
        verticalAlign="middle"
        color="#000000"
      >
        学生管理
      </MyBox>

      {/* 批量导入按钮 */}
      <MyBox position="absolute" top={vwDims(34)} left={vwDims(1242)} zIndex={20}>
        <ImportPanel
          type="student"
          typeName="学生"
          onUploadSuccess={handleUploadSuccess}
        />
      </MyBox>

      {/* 添加学生按钮 */}
      <Button
        colorScheme="purple"
        size="lg"
        w={vwDims(130)}
        h={vwDims(32)}
        position="absolute"
        top={vwDims(34)}
        left={vwDims(1421)}
        opacity={1}
        borderRadius={vwDims(8)}
        gap={vwDims(8)}
        pt={vwDims(7)}
        pr={vwDims(20)}
        pb={vwDims(7)}
        pl={vwDims(20)}
        bg="var(--purple-500, #7D4DFF)"
        zIndex={20}
        onClick={onAdd}
      >
        + 添加学生
      </Button>

      {/* 搜索栏 */}
      <MyBox position="absolute" top={vwDims(100)} left={vwDims(26)} right={vwDims(46)} zIndex={10}>
        <SearchBar
          onSearch={handleSearch}
          query={searchParams}
          tableInstance={tableInstanceRef.current as any}
        />
      </MyBox>

      {/* 学生列表 */}
      <MyBox
        position="absolute"
        top={vwDims(240)}
        left={vwDims(46)}
        right={vwDims(46)}
        bottom={vwDims(20)}
        overflow="hidden"
        zIndex={1}
      >
        <StudentList onEdit={onEdit} searchParams={searchParams} refreshKey={refreshKey} />
      </MyBox>

      {/* 弹窗组件 */}
      <StudentEdit
        studentId={studentId}
        mode={mode === 'view' ? 'view' : 'edit'}
        isOpen={isEditModalOpen}
        onClose={handleEditClose}
        onSuccess={handleRefresh}
      />

      {isOpenAddStudentModal && (
        <AddStudentModal
          studentId={studentId}
          mode={mode}
          onClose={handleCloseAddStudentModal}
          onSuccess={handleRefresh}
        />
      )}

      {isOpen && (
        <ChangeClassModal
          studentId={studentId}
          studentInfo={studentInfo}
          isOpen={isOpen}
          onClose={onClose}
          onSuccess={handleRefresh}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Student;
