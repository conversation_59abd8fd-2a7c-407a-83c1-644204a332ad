export enum EventNameEnum {
  sendQuestion = 'sendQuestion',
  editQuestion = 'editQuestion',
  historyCleared = 'historyCleared',

  // flow
  requestFlowEvent = 'requestFlowEvent',
  requestFlowStore = 'requestFlowStore',
  receiveFlowStore = 'receiveFlowStore',

  // space tree
  refreshTree = 'refreshTree',

  // human
  stopSpeak = 'stopSpeak',

  // 应用中心 To 常用导航栏
  commonAppListToNavbar = 'commonAppListToNavbar',
  // 常用导航栏 To 应用中心
  navbarToCommonAppList = 'navbarToCommonAppList'
}
type EventNameType = `${EventNameEnum}`;

// 单一监听器的事件总线（同一时间只会存在最后创建的一个事件,但不需要过多考虑销毁问题）
export const eventBus = {
  list: new Map<EventNameType, Function>(),
  on: function (name: EventNameType, fn: Function) {
    this.list.set(name, fn);
  },
  emit: function (name: EventNameType, data: Record<string, any> = {}) {
    const fn = this.list.get(name);
    fn && fn(data);
  },
  off: function (name: EventNameType) {
    this.list.delete(name);
  }
};

// 多监听器的事件总线（支持多个事件同时存在,但要更多考虑需要销毁的事件）
export const multiEventBus = {
  list: new Map<EventNameType, Function[]>(),
  on: function (name: EventNameType, fn: Function) {
    if (!this.list.has(name)) {
      this.list.set(name, []);
    }
    this.list.get(name)?.push(fn);
  },
  emit: function (name: EventNameType, data: Record<string, any> = {}) {
    const fns = this.list.get(name);
    fns && fns.forEach((fn) => fn(data));
  },
  off: function (name: EventNameType, fn?: Function) {
    if (fn) {
      const fns = this.list.get(name);
      if (fns) {
        const index = fns.indexOf(fn);
        if (index !== -1) {
          fns.splice(index, 1);
        }
      }
    } else {
      this.list.delete(name);
    }
  }
};
