import { ChatItemType } from '@/fastgpt/global/core/chat/type';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';

interface MindMapState {
  chatItems: any;
  selectedChatId: string | null;
  setSelectedChat: (items: ChatItemType) => void;
  clearMindMap: () => void;
}

export const useMindMapStore = create<MindMapState>()(
  devtools(
    persist(
      immer((set) => ({
        chatItems: [],
        selectedChatId: null,
        setSelectedChat: (items: ChatItemType) =>
          set((state) => {
            state.chatItems = items;
          }),
        clearMindMap: () =>
          set((state) => {
            state.selectedChatId = null;
            state.chatItems = [];
          })
      })),
      {
        name: 'mind-map-storage'
      }
    )
  )
);
