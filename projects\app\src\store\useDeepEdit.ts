// setDeepEditChatItem
// setDeepEditModalOpen

import { ChatItemType } from '@/fastgpt/global/core/chat/type';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware'; // Import the persist middleware
import { DynamicFormDataType } from '@/types/api/chat';
import { ChooserFileType } from '@/pages/cloud/list/components/Chooser';
import { OverviewTreeNode } from '@/pages/deepeditor/components/Overview/OverviewTree';
export type SaveStatus = 'idle' | 'saving' | 'saved';

export type FileType = {
  fileFrom: 'upload' | 'cloud';
} & ChooserFileType;

export const useDeepEditStore = create<{
  chatItem: ChatItemType;
  modalOpen: boolean;
  editType: 'edit' | 'new';
  generateFormData: DynamicFormDataType | undefined;
  overviewTreeData: OverviewTreeNode[] | null;
  saveStatus: SaveStatus;
  saveTime: string;
  saveObj: {
    saveStatus: SaveStatus;
    updateTime: string;
  };
  file: FileType | null;
  activeTab: 0 | 1;
  generateType?: 'overview' | 'content';
  generateOverviewLoading: boolean;
  returnFormData: any;
  returnDocumentTitle: string;
  generateContentLoading: boolean;
  setGenerateOverviewLoading: (loading: boolean) => void;
  setGenerateContentLoading: (loading: boolean) => void;
  setReturnDocumentTitle: (title: string) => void;
  setGenerateType: (type?: 'overview' | 'content') => void;
  setActiveTab: (tab: 0 | 1) => void;
  setDeepEditChatItem: (chatItem: ChatItemType) => void;
  setDeepEditModalOpen: (modalOpen: boolean) => void;
  setEditType: (editType: 'edit' | 'new') => void;
  setGenerateFormData: (data: DynamicFormDataType | undefined) => void;
  setOverviewTreeData: (data: any) => void;
  setReturnFormData: (data: any) => void;
  setSaveStatus: (status: SaveStatus) => void;
  setSaveTimes: (time: string) => void;
  setSaveObj: (obj: { saveStatus: SaveStatus; updateTime: string }) => void;
  setFile: (file: FileType | null) => void;
}>()(
  devtools(
    persist(
      immer((set) => ({
        chatItem: {} as ChatItemType,
        modalOpen: false,
        editType: 'new',
        generateFormData: null, // 初始化为 null 或其他默认值
        overviewTreeData: null, // 初始化为 null 或其他默认值
        saveStatus: 'idle' as SaveStatus, // 初始状态
        saveTime: '',
        saveObj: {
          saveStatus: 'idle',
          updateTime: ''
        },
        file: null,
        activeTab: 1,
        generateType: undefined,
        returnFormData: null,
        returnDocumentTitle: '',
        generateOverviewLoading: false,
        generateContentLoading: false,
        setGenerateContentLoading: (loading: boolean) =>
          set((state) => {
            state.generateContentLoading = loading;
          }),
        setGenerateOverviewLoading: (loading: boolean) =>
          set((state) => {
            state.generateOverviewLoading = loading;
          }),

        setReturnFormData: (data: any) =>
          set((state) => {
            state.returnFormData = data;
          }),
        setReturnDocumentTitle: (title: string) =>
          set((state) => {
            state.returnDocumentTitle = title;
          }),
        setGenerateType: (type?: 'overview' | 'content') =>
          set((state) => {
            state.generateType = type;
          }),
        setDeepEditChatItem: (chatItem: ChatItemType) =>
          set((state) => {
            if (state.editType === 'new') {
              state.chatItem = {} as ChatItemType;
            } else {
              state.chatItem = chatItem;
            }
          }),
        setDeepEditModalOpen: (modalOpen: boolean) =>
          set((state) => {
            // 关闭清空缓存
            state.modalOpen = modalOpen;
            if (!modalOpen) {
              state.chatItem = {} as ChatItemType;
            }
          }),
        setEditType: (editType: 'edit' | 'new') =>
          set((state) => {
            state.editType = editType;
          }),
        setGenerateFormData: (data: any) =>
          set((state) => {
            state.generateFormData = data;
          }),
        setOverviewTreeData: (data: any) =>
          set((state) => {
            state.overviewTreeData = data;
          }),
        setSaveStatus: (status: SaveStatus) =>
          set((state) => {
            state.saveStatus = status;
          }),
        setSaveTimes: (time: string) =>
          set((state) => {
            state.saveTime = time;
          }),
        setSaveObj: (obj: { saveStatus: SaveStatus; updateTime: string }) =>
          set((state) => {
            state.saveObj = obj;
          }, false),
        setFile: (file: FileType | null) =>
          set((state) => {
            state.file = file;
          }),
        setActiveTab: (tab: 0 | 1) =>
          set((state) => {
            state.activeTab = tab;
          })
      })),
      {
        name: 'deep-edit-store'
      }
    )
  )
);

export const useDeepEditModalOpen = () => {
  return useDeepEditStore((state) => state.modalOpen);
};
export const useDeepEditChatItem = () => {
  return useDeepEditStore((state) => state.chatItem);
};

export const useSaveStatus = () => useDeepEditStore((state) => state.saveStatus);
export const useSaveTimes = () => useDeepEditStore((state) => state.saveTime);
export const useSaveObj = () => useDeepEditStore((state) => state.saveObj);
