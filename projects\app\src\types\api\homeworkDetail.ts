import { RequestPageParams } from '..';

// 定义作业详情数据类型
export interface HomeworkDetailResponse {
  /**
   * 能力分组班级ID
   */
  abilityGroupedClazzId: number;
  /**
   * 能力分组班级名称
   */
  abilityGroupedClazzName: string;
  /**
   * 能力分组策略,当不是分层作业的时候这个值为null
   */
  abilityGroupedStrategy: AbilityGroupedStrategy | null;
  /**
   * AI助手触发条件，逗号分隔
   */
  aiAssistantTriggers: string;
  /**
   * 班级列表
   */
  clazzList: ClazzList[];
  /**
   * 内容列表
   */
  contentList: ContentList[];
  /**
   * 批改方式：1-AI批改，2-手动批改
   */
  correctMethod: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建用户ID
   */
  createUser: number;
  /**
   * 创建用户名称
   */
  createUserName: string;
  /**
   * 截止时间
   */
  deadlineTime: string;
  /**
   * 作业描述
   */
  description: string;
  /**
   * 是否启用AI助手，0-否，1-是
   */
  enableAiAssistant: number;
  /**
   * 是否允许重新提交，0-否，1-是
   */
  enableAllowResubmit: number;
  /**
   * 是否允许查看答案，0-否，1-是
   */
  enableAnswerVisible: number;
  /**
   * 是否启用接收通知，0-否，1-是
   */
  enableReceiveNotice: number;
  /**
   * 是否启用定时发布，0-否，1-是
   */
  enableScheduledPublish: number;
  /**
   * 预计完成时间(分钟)
   */
  expectFinishDuration: number;
  /**
   * 预计发布时间
   */
  expectPublishTime: string;
  /**
   * 已批改数量
   */
  gradedCount: number;
  /**
   * 年级ID
   */
  gradeId: number;
  /**
   * 年级名称
   */
  gradeName: string;
  /**
   * 批改率
   */
  gradingRate: null;
  /**
   * 作业ID
   */
  id: number;
  /**
   * 是否自定义，0-否，1-是
   */
  isCustom: number;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 是否记住推题规则，0-否，1-是
   */
  isRememberPushRule: number;
  /**
   * 是否记住规则，0-否，1-是
   */
  isRememberRule: number;
  /**
   * 模块状态
   */
  moduleStatus: ModuleStatus;
  /**
   * 选择题评分方式：1-全对得分，2-按选项得分
   */
  multipleChoiceScoringMethod: number;
  /**
   * 作业名称
   */
  name: string;
  /**
   * 发布时间
   */
  publishTime: string;
  /**
   * 推题规则
   */
  pushRule: PushRule;
  /**
   * 推题规则ID
   */
  pushRuleId: number;
  /**
   * 接收通知类型，逗号分隔：1-提交，2-批改完成
   */
  receiveNoticeTypes: string;
  /**
   * 学期ID
   */
  semesterId: number;
  /**
   * 学段ID
   */
  stageId: number;
  /**
   * 学段名称
   */
  stageName: string;
  /**
   * 状态：1-草稿，2-已发布，3-已结束，4-已归档
   */
  status: number;
  /**
   * 策略ID
   */
  strategyId: number;
  /**
   * 学生总数
   */
  studentCount: number;
  /**
   * 学科ID
   */
  subjectId: number;
  /**
   * 主观题评分方式：1-按总分评，2-按步骤评
   */
  subjectiveScoringMethod: number;
  /**
   * 学科名称
   */
  subjectName: string;
  /**
   * 提交率
   */
  submissionRate: null;
  /**
   * 提交方式：1-学生在线答题，2-学生平板拍照,3-教师帮录
   */
  submitMethod: number;
  /**
   * 已提交人数
   */
  submittedCount: number;
  /**
   * 租户ID
   */
  tenantId: number;
  /**
   * 租户名称
   */
  tenantName: string;
  /**
   * 学期
   */
  term: null;
  /**
   * 租户成员ID
   */
  tmbId: number;
  /**
   * 作业类型图标URL
   */
  typeIconUrl: string;
  /**
   * 作业类型ID
   */
  typeId: number;
  /**
   * 作业类型名称
   */
  typeName: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 更新用户ID
   */
  updateUser: number;
  /**
   * 年份
   */
  year: string;
  /**
   * 是否同步至校本资源库
   * 0-否；1-是
   */
  isSync: number;
  [property: string]: any;
}

// 班级列表类型
export interface ClazzList {
  /**
   * 班级ID
   */
  clazzId: number;
  /**
   * 班级名称
   */
  clazzName: string;
  /**
   * 年级名称
   */
  gradeName: string;
  /**
   * 班级图标文件
   */
  iconFile: null;
  /**
   * 学生ID列表
   */
  studentIdList: number[];
  /**
   * 学生详细信息列表
   */
  studentList?: StudentInfo[];
  [property: string]: any;
}

/**
 * 学生信息接口
 */
export interface StudentInfo {
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 学生层级：0-默认层级，1-熟练掌握层，2-基本掌握层，3-初步学习层
   */
  level: number;
  /**
   * 学生ID
   */
  studentId: number;
  /**
   * 学生姓名
   */
  studentName: string;
  /**
   * 学生学号/编码
   */
  studentCode: string;
  /**
   * 学生评分/比率
   */
  rate: number | null;
}

// 内容列表类型
export interface ContentList {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户ID
   */
  createUser?: number;
  /**
   * 创建用户名称
   */
  createUserName?: string;
  /**
   * 作业ID
   */
  homeworkId?: number;
  /**
   * 内容ID
   */
  id?: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted?: number;
  /**
   * 布局列表
   */
  layoutList?: LayoutList[];
  /**
   * 层级
   */
  level?: number;
  /**
   * 作文辅助材料
   */
  materialFileList?: MaterialFileList[];
  /**
   * 题目数量
   */
  questionCount?: number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 总分
   */
  totalScore?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新用户ID
   */
  updateUser?: number;
  [property: string]: any;
}

// 布局列表类型
export interface LayoutList {
  /**
   * 内容ID
   */
  contentId?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 说明
   */
  description?: string;
  /**
   * 布局ID
   */
  id?: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted?: number;
  /**
   * 题目列表
   */
  questionList?: QuestionList[];
  /**
   * 标题
   */
  title?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  [property: string]: any;
}

// 题目列表类型
export interface QuestionList {
  /**
   * 内容布局ID
   */
  contentLayoutId?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 评分标准
   */
  gradingCriteria?: string;
  /**
   * 作业ID
   */
  homeworkId?: number;
  /**
   * 题目ID
   */
  id?: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted?: number;
  /**
   * 层级
   */
  level?: number;
  /**
   * 题目详情
   */
  question?: Question;
  /**
   * 问题ID
   */
  questionId?: number;
  /**
   * 题目顺序
   */
  questionOrder?: number;
  /**
   * 分数
   */
  score?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  [property: string]: any;
}

// 题目详情类型
export interface Question {
  /**
   * 解析
   */
  analysis: string;
  /**
   * 答案，JSON格式
   */
  answer: string;
  /**
   * 答题形式：1-选择，2-填空，3-解答
   */
  answerType: number;
  /**
   * 地区编码
   */
  areaCode: string;
  /**
   * 地区ID
   */
  areaId: number;
  /**
   * 地区名称
   */
  areaName: string;
  /**
   * 归属
   */
  attribution: number;
  /**
   * 归属关联ID
   */
  attributionToId: number;
  /**
   * 归属关联名称
   */
  attributionToName: string;
  /**
   * 创建时间戳
   */
  createTime: number;
  /**
   * 难度：1-简单，2-中等，3-困难
   */
  difficulty: number;
  /**
   * 难度名称
   */
  difficultyName: string;
  /**
   * 反馈数量
   */
  feedbackCount: null;
  /**
   * 年级ID
   */
  gradeId: number;
  /**
   * 年级名称
   */
  gradeName: string;
  /**
   * 题目ID
   */
  id: number;
  /**
   * 知识点ID列表
   */
  knowledgePointIds: number[];
  /**
   * 知识点名称列表
   */
  knowledgePointNames: string[];
  /**
   * 选项，JSON格式
   */
  options: string;
  /**
   * 来源信息
   */
  originInfo: string;
  /**
   * 原始资源ID
   */
  originResourceId: number;
  /**
   * 题型ID
   */
  questionTypeId: number;
  /**
   * 题型名称
   */
  questionTypeName: string;
  /**
   * 资源ID
   */
  resourceId: number;
  /**
   * 是否资源题：0-否，1-是
   */
  resourceQuestion: number;
  /**
   * 共享状态：0-私有，1-共享
   */
  shareStatus: number;
  /**
   * 学段ID
   */
  stageId: number;
  /**
   * 学段名称
   */
  stageName: string;
  /**
   * 状态：0-禁用，1-启用
   */
  status: number;
  /**
   * 题干
   */
  stem: string;
  /**
   * 学科ID
   */
  subjectId: number;
  /**
   * 学科名称
   */
  subjectName: string;
  /**
   * 同步状态：0-未同步，1-已同步，2-同步失败
   */
  syncStatus: number;
  /**
   * 标签
   */
  tags: string[];
  /**
   * 教材章节ID列表
   */
  textbookChapterIds: string[];
  /**
   * 教材章节名称列表
   */
  textbookChapterNames: string[];
  /**
   * 教材版本ID
   */
  textbookVersionId: number;
  /**
   * 教材版本名称
   */
  textbookVersionName: string;
  /**
   * 教材册次ID
   */
  textbookVolumeId: number;
  /**
   * 教材册次名称
   */
  textbookVolumeName: string;
  /**
   * 租户成员名称
   */
  tmbName: string;
  /**
   * 更新时间戳
   */
  updateTime: number;
  /**
   * 年份
   */
  vintages: number;
  [property: string]: any;
}

// 资源视图对象类型
export interface ResourcesVO {
  /**
   * 资源ID
   */
  id: number;
  /**
   * 资源标题
   */
  title: string;
  /**
   * 资源描述
   */
  description: string;
  /**
   * 资源类型ID
   */
  resourceTypeId: number;
  /**
   * 文件格式ID
   */
  fileFormatId: number;
  /**
   * 文件格式名称
   */
  fileFormatName: string;
  /**
   * 学科ID
   */
  subjectId: number;
  /**
   * 年级ID
   */
  gradeId: number;
  /**
   * 学段ID
   */
  stageId: number;
  /**
   * 教材版本ID
   */
  textbookVersionId: number;
  /**
   * 教材册次ID
   */
  textbookVolumeId: number;
  /**
   * 教材章节ID列表
   */
  textbookChapterIds: number[];
  /**
   * 教材章节名称列表
   */
  textbookChapterNames: string[];
  /**
   * 地区ID
   */
  areaId: number;
  /**
   * 地区编码
   */
  areaCode: string;
  /**
   * 创建用户名
   */
  createUserName: string;
  /**
   * 归属：1-我的 2-校本 3-官方
   */
  attribution: number;
  /**
   * 归属对象ID
   */
  attributionToId: number;
  /**
   * 归属对象名称
   */
  attributionToName: string;
  /**
   * 年份
   */
  vintages: number;
  /**
   * 分享状态：0-未分享
   */
  shareStatus: number;
  /**
   * 下载次数
   */
  downloadCount: number;
  /**
   * 查看次数
   */
  viewCount: number;
  /**
   * 策展状态：0-未策展
   */
  curation: number;
  /**
   * 文件Key
   */
  fileKey: string;
  /**
   * 文件名称
   */
  fileName: string;
  /**
   * 文件URL
   */
  fileUrl: string;
  /**
   * 文件大小
   */
  fileSize: number;
  /**
   * 文件JSON信息
   */
  fileJson: string;
  /**
   * 是否可切题：0-否，1-是
   */
  cutQuestion: number;
  /**
   * 状态：1-正常
   */
  status: number;
  /**
   * 资源类型名称
   */
  resourceTypeName: string;
  /**
   * 学科名称
   */
  subjectName: string;
  /**
   * 学段名称
   */
  stageName: string;
  /**
   * 年级名称
   */
  gradeName: string;
  /**
   * 教材版本名称
   */
  textbookVersionName: string;
  /**
   * 教材册次名称
   */
  textbookVolumeName: string;
  /**
   * 地区名称
   */
  areaName: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 是否收藏
   */
  isCollected: boolean | null;
  /**
   * 是否拥有：1-是
   */
  isOwned: number;
  /**
   * 是否可操作：1-是
   */
  isOperable: number;
  /**
   * 知识点ID列表
   */
  knowledgePointIds: number[];
  /**
   * 知识点名称列表
   */
  knowledgePointNames: string[];
  /**
   * 自定义标签列表
   */
  customTags: string[];
  [property: string]: any;
}

// 资料文件列表类型
export interface MaterialFileList {
  /**
   * 文件ID
   */
  id: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 资源ID
   */
  resourceId: number;
  /**
   * 层级
   */
  level: number;
  /**
   * 内容ID
   */
  contentId: number;
  /**
   * 文件信息
   */
  file: File;
  /**
   * 文件Key
   */
  fileKey: string;
  /**
   * 资源视图对象-作业辅助材料需要使用
   */
  resourcesVO?: ResourcesVO;
  [property: string]: any;
}

// 文件信息类型
export interface File {
  /**
   * 文件ID
   */
  id: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 文件名称
   */
  fileName: string;
  /**
   * 文件URL
   */
  fileUrl: string;
  /**
   * 文件Key
   */
  fileKey: string;
  /**
   * 文件大小(字节)
   */
  fileSize: number;
  /**
   * 文件JSON配置信息
   */
  fileJson: string;
  /**
   * 文件类型
   */
  fileType: string;
  [property: string]: any;
}

// 模块状态类型
export interface ModuleStatus {
  /**
   * 1-作业要求，true-已解锁
   */
  '1': boolean;
  /**
   * 2-作业批改，false-未解锁
   */
  '2': boolean;
  /**
   * 3-学生错题，false-未解锁
   */
  '3': boolean;
  /**
   * 4-学情分析，false-未解锁
   */
  '4': boolean;
}

// 推题规则类型
export interface PushRule {
  /**
   * 归属 1个人，2学校，3华云自有，4第三方
   */
  attribution: number;
  /**
   * 章节ID列表
   */
  chapterIdList: number[];
  /**
   * 章节名称列表
   */
  chapterNameList: string[];
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建用户ID
   */
  createUser: number;
  /**
   * 创建用户名称
   */
  createUserName: string;
  /**
   * 难度ID
   */
  difficultyId: number;
  /**
   * 难度模式
   */
  difficultyMode: number;
  /**
   * 难度名称
   */
  difficultyName: string;
  /**
   * 规则ID
   */
  id: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 来源
   */
  origin: number;
  /**
   * 知识点ID列表
   */
  pointIdList: number[];
  /**
   * 知识点名称列表
   */
  pointNameList: string[];
  /**
   * 推题模式：1-按知识点推题；2-按章节推题；3-自定义作文
   */
  pushMode: 1 | 2 | 3;
  /**
   * 题型列表
   */
  questionTypeList: QuestionTypeList[];
  /**
   * 学段ID
   */
  stageId: number;
  /**
   * 学段名称
   */
  stageName: string;
  /**
   * 学科ID
   */
  subjectId: number;
  /**
   * 学科名称
   */
  subjectName: string;
  /**
   * 租户ID
   */
  tenantId: number;
  /**
   * 教材版本ID
   */
  textbookVersionId: number;
  /**
   * 教材版本名称
   */
  textbookVersionName: string;
  /**
   * 教材册次ID
   */
  textbookVolumeId: number;
  /**
   * 教材册次名称
   */
  textbookVolumeName: string;
  /**
   * 租户成员ID
   */
  tmbId: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 更新用户ID
   */
  updateUser: number;
  /**
   * 供应商ID列表
   */
  vendorIdList: number[];
  /**
   * 供应商名称列表
   */
  vendorNameList: string[];
  /**
   * 年份列表
   */
  yearList: number[];
  /**
   * 题目来源列表
   * 1-官方题库；2-校本题库；3-我的题库；4-我的收藏。多选字段，值以逗号分隔
   * 如果选择了1-官方题库
   * 还要显示题目来源供应商名称列表
   * vendorNameList
   */
  origins: string;
  [property: string]: any;
}

// 题型列表类型
export interface QuestionTypeList {
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 题目数量
   */
  questionCount: number;
  /**
   * 题型ID
   */
  questionTypeId: number;
  /**
   * 题型名称
   */
  questionTypeName: string;
  /**  作业任务类型 1-智慧作业；2-分层作业；3-写作任务(又称作文类)；4-自定义作业； */
  taskType: number;
  [property: string]: any;
}

// 定义作业类型详情数据类型
export type HomeworkTypeDetailResponse = {
  [key: string]: any;
};

// 获取作业提交概况-提交情况 响应类型
export interface HomeworkSubmissionStatsResponse {
  /**
   * 待提交人数
   */
  pendingCount: number;
  /**
   * 已提交人数
   */
  submittedCount: number;
  /**
   * 应提交人数
   */
  totalCount: number;
  /**
   * 未提交人数
   */
  unSubmittedCount: number;
}

// 获取作业提交概况-完成情况 响应类型
export interface HomeworkSubmissionCompletionStatsResponse {
  /**
   * 平均完成时长(秒)
   */
  avgCompletionTime: number;
  /**
   * 最长完成时长(秒)
   */
  maxCompletionTime: number;
  /**
   * 最短完成时长(秒)
   */
  minCompletionTime: number;
}

// 获取作业提交概况-班级对比 响应类型
export interface HomeworkSubmissionClassComparisonResponse {
  /**
   * 平均完成时长(秒)
   */
  avgCompletionTime: number;
  /**
   * 班级ID
   */
  clazzId: number;
  /**
   * 班级名称
   */
  clazzName: string;
  /**
   * 提交率
   */
  submissionRate: number;
}

// 获取作业批改概况-批改进度 响应类型
export interface HomeworkGradingStatsResponse {
  /**
   * 已批改数量
   */
  gradedCount: number;
  /**
   * 待批改数量
   */
  pendingCount: number;
}

// 获取作业批改概况-答题情况 响应类型
export interface HomeworkGradingAnswerStatsResponse {
  /**
   * 平均分
   */
  averageScore: number;
  /**
   * 最高分
   */
  maxScore: number;
  /**
   * 最低分
   */
  minScore: number;
}

// 获取作业批改概况-班级对比 相关类型
export interface ClassItemList {
  /**
   * 平均分
   */
  averageScore: number;
  /**
   * 班级ID
   */
  clazzId: number;
  /**
   * 班级名称
   */
  clazzName: string;
  /**
   * 年级名称
   */
  gradeName: string;
  [property: string]: any;
}

export interface HomeworkGradingClassComparisonResponse {
  /**
   * 平均分
   */
  averageScore: number;
  /**
   * 班级平均分列表
   */
  classItemList: ClassItemList[];
  /**
   * 总分（满分值）
   */
  totalScore: number;
  [property: string]: any;
}

// 分页查询学生提交与批改列表 请求参数类型
export interface StudentSubmissionPageRequest {
  /**
   * 班级id
   */
  clazzId?: number;
  current: number;
  /**
   * 结束日期
   */
  endDate: string;
  /**
   * 作业标识：1-草稿箱；2-我的发布
   */
  flagType: number;
  /**
   * 年级ID
   */
  gradeId: number;
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 作业名称
   */
  name: string;
  /**
   * 排序关键字（正序）,发布时间排序传publish_time;截止时间排序传：deadline_time;创建时间排序传：create_time;
   */
  orderKeyAsc: string;
  /**
   * 排序关键字（倒序）,发布时间排序传publish_time;截止时间排序传：deadline_time;创建时间排序传：create_time;
   */
  orderKeyDesc: string;
  /**
   * 学期ID
   */
  semesterId: number;
  size: number;
  /**
   * 开始日期
   */
  startDate: string;
  /**
   * 作业状态：0-待提交，1-未提交，2-待批改，3-批改中，4-待确认，5-已完成，6-待订正，7-批改失败
   */
  status: number | null;
  /**
   * 学科ID
   */
  subjectId: number;
  /**
   * 作业类型ID
   */
  typeId: number;
  /**
   * 提交开始日期
   */
  submitStartTime: string;
  /**
   * 提交结束日期
   */
  submitEndTime: string;
  /**
   * 批改完成开始日期
   */
  confirmStartTime: string;
  /**
   * 批改完成结束日期
   */
  confirmEndTime: string;
  /**
   * 学生作业层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level: number;
  /**
   * 搜索关键字（学生学号、学生姓名）
   */
  searchKey: string;
  /**
   * 学生学号
   */
  code: string;
}

// 分页查询学生提交与批改列表 响应类型
export interface StudentSubmissionPageResponse {
  current: number;
  orders: string[];
  pages: number;
  records: StudentSubmissionRecord[];
  searchCount: boolean;
  size: number;
  total: number;
  [property: string]: any;
}

export interface StudentSubmissionRecord {
  /**
   * 学生照片
   */
  avatarUrl?: string;
  clazzId?: number;
  /**
   * 班级名称
   */
  clazzName?: string;
  /**
   * 学号
   */
  code?: string;
  /**
   * 确认批改时间
   */
  confirmTime?: string;
  /**
   * 订正原因
   */
  correctionReason?: string;
  createTime?: string;
  /**
   * 失败原因
   */
  failureReason?: string;
  /**
   * 年级名称
   */
  gradeName?: string;
  homeworkId?: number;
  homeworkStudentId?: null;
  /**
   * 主键ID
   */
  id?: string;
  isDeleted?: null;
  /**
   * 是否为订正 0-否；1-是
   */
  isRevise?: number;
  /**
   * 学生作业层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level?: number;
  /**
   * 得分
   */
  score?: number;
  /**
   * 状态：0-待提交，1-未提交，2-待批改，3-AI批改中，4-待确认，5-已完成，6-待订正，7-批改失败
   */
  status?: number;
  studentId?: number;
  /**
   * 学生名称
   */
  studentName?: string;
  /**
   * 提交ID
   */
  submitId?: number;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 满分
   */
  totalScore?: number;
  updateTime?: string;
  [property: string]: any;
}

// ==================== 学情分析相关类型定义 ====================

/**
 * 学情分析通用请求参数
 */
export interface LearningAnalysisRequest {
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 班级ID，不传代表全部班级
   */
  clazzId?: number | null;
  /**
   * 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层，不传表示全部分层
   */
  level?: number | null;
}

/**
 * 学情分析通用分页响应数据
 */
export interface LearningAnalysisPageResponse {
  current: number;
  orders: string[];
  pages: number;
  records: StudentSubmissionRecord[];
  searchCount: boolean;
  size: number;
  total: number;
  [property: string]: any;
}

/**
 * 学情分析-作业完成情况-提交情况/提交时间/完成时长 响应类型
 */
export interface LearningAnalysisResponse {
  /**
   * 提交统计
   */
  submitStats: {
    /**
     * 应提交总数
     */
    requiredSubmitTotal: number;
    /**
     * 已提交数量
     */
    submitted: number;
    /**
     * 未提交数量
     */
    unSubmitted: number;
    /**
     * 未提交学生名单
     */
    unSubmittedStudents: string[];
  };
  /**
   * 提交时间统计
   */
  submissionTimeStats: {
    /**
     * 按时提交数量
     */
    onTimeSubmitted: number;
    /**
     * 延迟提交数量
     */
    delayedSubmitted: number;
    /**
     * 延迟提交学生名单
     */
    delayedSubmittedStudents: string[];
  };
  /**
   * 完成时长统计
   */
  completionStats: {
    /**
     * 平均完成时间(分钟)
     */
    avgCompletionTime: number;
    /**
     * 最长完成时间(分钟)
     */
    maxCompletionTime: number;
    /**
     * 最短完成时间(分钟)
     */
    minCompletionTime: number;
  };
}

/**
 * 学情分析-作业完成情况-题目平均完成率/错题占比 响应类型
 */
export interface QuestionCompletionStatsResponse {
  /**
   * 平均完成情况统计
   */
  avgCompletionStats: {
    /**
     * 总题目数
     */
    totalQuestions: number;
    /**
     * 平均已完成题目数
     */
    avgCompletedQuestions: number;
    /**
     * 平均未完成题目数
     */
    avgUncompletedQuestions: number;
    /**
     * 平均完成率
     */
    avgCompletionRate: number;
  };
  /**
   * 错题统计
   */
  wrongStats: {
    /**
     * 错题学生数量
     */
    errorStudentCount: number;
    /**
     * 平均错题数
     */
    avgErrorCount: number;
    /**
     * 平均错误率
     */
    avgErrorRate: number;
  };
}

/**
 * 优秀学生记录
 */
export interface ExcellentStudentRecord {
  /**
   * 排名
   */
  rank: number | null;
  /**
   * 学号
   */
  studentNo: string;
  /**
   * 学生姓名
   */
  studentName: string;
  /**
   * 得分
   */
  score: number;
  /**
   * 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level: number | null;
  /**
   * 得分率
   */
  scoreRate: number;
}

/**
 * 学情分析-作业质量分析-前10优秀作业 响应类型
 */
export type ExcellentStudentResponse = ExcellentStudentRecord[];

/**
 * 知识点错题记录
 */
export interface KnowledgePointError {
  /**
   * 知识点ID
   */
  knowledgePointId: number;
  /**
   * 知识点名称
   */
  knowledgePointName: string;
  /**
   * 错题学生数量
   */
  errorStudentCount: number;
}

/**
 * 题型错题记录
 */
export interface QuestionTypeError {
  /**
   * 题型ID
   */
  questionTypeId: number;
  /**
   * 题型名称
   */
  questionTypeName: string;
  /**
   * 错题学生数量
   */
  errorStudentCount: number;
}

/**
 * 学情分析-作业质量分析-知识点/题型错题分布 响应类型
 */
export interface ErrorDistributionResponse {
  /**
   * 知识点错题列表
   */
  knowledgePointErrorList: KnowledgePointError[];
  /**
   * 题型错题列表
   */
  questionTypeErrorList: QuestionTypeError[];
}

/**
 * 分层平均分记录
 */
export interface LevelAvgScoreRecord {
  /**
   * 层级：0-全部层级；1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level: number;
  /**
   * 平均分
   */
  averageScore: number;
  /**
   * 总分
   */
  totalScore: number;
}

/**
 * 学情分析-作业质量分析-班级平均分（分层作业） 响应类型
 */
export type LevelAvgScoreResponse = LevelAvgScoreRecord[];

// ==================== 学生提交记录相关类型定义 ====================

/**
 * 获取学生提交记录列表请求参数
 */
export interface StudentSubmitRecordRequest {
  /**
   * 班级ID，不传代表全部班级
   */
  clazzId?: number | null;
  /**
   * 层级，不传代表全部
   */
  level?: number | null;
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 学生ID
   */
  studentId: number;
}

/**
 * 学生提交记录
 */
export interface StudentSubmitRecord {
  /**
   * 提交ID
   */
  submitId: number;
  /**
   * 是否订正：0-否，1-是
   */
  isRevise: number;
  /**
   * 订正次数
   */
  reviseCount: number;
}

/**
 * 学生提交记录列表响应
 */
export type StudentSubmitRecordResponse = StudentSubmitRecord[];

// ==================== 学情分析-个体分析相关类型定义 ====================

/**
 * 学情分析-个体分析请求参数
 */
export interface IndividualAnalysisRequest {
  /**
   * 提交ID（从获取学生提交记录列表的submitId获取）
   */
  id: number;
}

/**
 * 知识点项目
 */
export interface PointItem {
  /**
   * 知识点ID
   */
  pointId: number;
  /**
   * 知识点名称
   */
  pointName: string;
  /**
   * 正确题目数量
   */
  correctCount: number;
  /**
   * 总题目数量
   */
  totalCount: number;
  /**
   * 正确率
   */
  correctRate: number;
}

/**
 * 学情分析-个体分析响应
 */
export interface IndividualAnalysisResponse {
  /**
   * 得分
   */
  score: number;
  /**
   * 总分
   */
  totalScore: number;
  /**
   * 正确率
   */
  correctRate: number;
  /**
   * 答对题目数量
   */
  correctCount: number;
  /**
   * 答错题目数量
   */
  wrongCount: number;
  /**
   * 已答题目数量
   */
  answeredCount: number;
  /**
   * 未答题目数量
   */
  unansweredCount: number;
  /**
   * 用时（秒）
   */
  timeSecond: number;
  /**
   * 知识点项目列表
   */
  pointItems: PointItem[];
}

// ==================== 学生错题相关类型定义 ====================

/**
 * 学生错题概况-订正情况请求参数
 */
export interface StudentErrorReviseStatsRequest {
  /** 作业ID */
  homeworkId: number;
  /** 班级ID，不传表示全部班级 */
  clazzId?: number | null;
  /** 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层，不传表示全部分层 */
  level?: number | null;
}

/**
 * 学生错题概况-订正情况响应数据
 */
export interface StudentErrorReviseStatsResponse {
  /** 待订正数量 */
  reviseCount: number;
  /** 已完成数量 */
  completedCount: number;
}

/**
 * 学生错题概况-错题占比请求参数
 */
export interface StudentErrorWrongStatsRequest {
  /** 作业ID */
  homeworkId: number;
  /** 班级ID，不传表示全部班级 */
  clazzId?: number | null;
  /** 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层，不传表示全部分层 */
  level?: number | null;
}

/**
 * 学生错题概况-错题占比响应数据
 */
export interface StudentErrorWrongStatsResponse {
  /** 错题人数 */
  errorStudentCount: number;
  /** 平均错题数 */
  avgErrorCount: number;
  /** 平均答错率（百分比） */
  avgErrorRate: number;
}

/**
 * 学生错题列表查询参数
 */
export type StudentErrorPageRequest = {
  /** 作业ID */
  homeworkId: number;
  /** 班级ID */
  clazzId: number;
  /** 学生作业层级：1-熟练掌握层；2-基本掌握层；3-初步学习层，不传表示全部层级 */
  level?: number | null;
  /** 作业状态：0-待提交，1-未提交，2-待批改，3-批改中，4-待确认，5-已完成，6-待订正，7-批改失败 */
  status?: number | null;
  /** 关键字 */
  searchKey?: string;
  /** 订正完成开始日期 */
  reviseStartTime?: string;
  /** 订正完成结束日期 */
  reviseEndTime?: string;
  /** 排序关键字（正序）,答错题数排序传wrongCount;订正完成时间排序传：reviseTime */
  orderKeyAsc?: string;
  /** 排序关键字（倒序）,答错题数排序传wrongCount;订正完成时间排序传：reviseTime */
  orderKeyDesc?: string;
} & RequestPageParams;

/**
 * 学生错题记录
 */
export interface StudentErrorRecord {
  id?: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number | null;
  homeworkId?: number;
  clazzId?: number;
  studentId?: number;
  status?: number;
  correctionReason?: string;
  failureReason?: string;
  /** 学生作业层级：1-熟练掌握层；2-基本掌握层；3-初步学习层 */
  level?: number;
  homeworkStudentId?: number | null;
  /** 学生名称 */
  studentName?: string;
  /** 学号 */
  code?: string;
  /** 学生照片 */
  avatarUrl?: string;
  /** 班级名称 */
  clazzName?: string;
  /** 年级名称 */
  gradeName?: string;
  score?: number | null;
  totalScore?: number | null;
  /** 学生作业提交ID */
  submitId?: number;
  submitTime?: string;
  confirmTime?: string;
  isRevise?: number;
  /** 订正状态：0-无状态，1-待订正，2-已完成 */
  reviseStatus?: number;
  /** 已归纳错题数 */
  errorCount?: number;
  /** 答错题数 */
  wrongCount?: number;
  /** 订正完成时间 */
  reviseTime?: string;
}

/**
 * 学生错题列表分页响应数据
 */
export interface StudentErrorPageResponse {
  records: StudentErrorRecord[];
  total: number;
  size: number;
  current: number;
  orders: string[];
  searchCount: boolean;
  pages: number;
}

/**
 * 批量操作请求参数
 */
export interface BatchOperationRequest {
  /** 学生作业提交ID列表 */
  submitIds: number[];
}

/**
 * 打回订正请求参数
 */
export interface RejectRevisionRequest {
  /** 学生作业提交ID列表 */
  submitIds: number[];
  /** 归纳错题规则：1-订正后需要批改，2-订正后向学生公布答案解析 */
  addErrorRule: number;
  /** 打回原因 */
  rejectReason: string;
}

/**
 * 学生错题概况查询参数
 */
export interface StudentErrorQuestionStatsRequest {
  /** 作业ID */
  homeworkId: number;
  /** 学生ID */
  studentId: number;
}

/**
 * 错题题型分布
 */
export interface WrongQuestionType {
  id: number;
  /** 题型 */
  name: string;
  /** 错题数量 */
  wrongCount: number;
  /** 总题数 */
  totalCount: number;
  /** 错误占比 */
  wrongRate: number;
}

/**
 * 错题知识点分布
 */
export interface WrongKnowledgePoint {
  id: number;
  /** 知识点 */
  name: string;
  /** 错题数量 */
  wrongCount: number;
  /** 总题数 */
  totalCount: number;
  /** 错误占比 */
  wrongRate: number;
}

/**
 * 学生错题概况响应数据
 */
export interface StudentErrorQuestionStatsResponse {
  /** 错题题型分布 */
  wrongQuestionTypeList: WrongQuestionType[];
  /** 错题知识点分布 */
  wrongKnowledgePointList: WrongKnowledgePoint[];
}

/**
 * 学生订正提交列表查询参数
 */
export interface StudentReviseSubmitListRequest {
  /** 学生作业ID */
  id: number;
}

/**
 * 学生作业题目文件
 */
export interface StudentQuestionFile {
  // 根据实际需要定义文件属性
  [key: string]: any;
}

/**
 * 学生提交的答案
 */
export interface SubmitQuestion {
  /** 主键ID */
  id: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 是否删除 */
  isDeleted: number;
  /** 学生作业提交ID */
  submitId: number;
  /** 题目ID */
  questionId: number;
  /** 答案 */
  studentAnswer: string;
  /** 评分 */
  earnedScore: number;
  /** 评分理由 */
  reason: string;
  /** 批改结果 0-错误；1-正确；2-部分正确 */
  correctResult: number;
  /** 是否归纳为错题 0-否；1-是 */
  isAddError: number;
  /** 累计答错次数 */
  wrongCount: number;
  /** 学生作业提交题目文件列表 */
  files: StudentQuestionFile[];
}

/**
 * 题目详情
 */
export interface QuestionDetail {
  /** 源题目ID */
  originQuestionId: number | null;
  /** 主键ID */
  id: number;
  /** 父级ID */
  parentId: number;
  /** 题干 */
  stem: string;
  /** 题目选项 */
  options: string;
  /** 题目类型ID */
  questionTypeId: number;
  /** 难度 */
  difficulty: number;
  /** 答案 */
  answer: string;
  /** 解析 */
  analysis: string;
  /** 归属类型 */
  attribution: number;
  /** 归属关联ID */
  attributionToId: number;
  /** 题目归属关联名称 */
  attributionToName: string;
  /** 共享状态：0私有，1共享到学校 */
  shareStatus: number;
  /** 学科ID */
  subjectId: number;
  /** 学段ID */
  stageId: number;
  /** 年级ID */
  gradeId: number;
  /** 教材版本ID */
  textbookVersionId: number;
  /** 教材ID */
  textbookVolumeId: number;
  /** 地区ID */
  areaId: number;
  /** 地区Code */
  areaCode: string;
  /** 题目来源 */
  originInfo: string;
  /** 来源资源ID */
  originResourceId: number;
  /** 年份 */
  vintages: number;
  /** 是否资源题 */
  resourceQuestion: number;
  /** 资源ID */
  resourceId: number;
  /** 同步状态(0:未同步,1:已同步,2:同步失败) */
  syncStatus: number;
  /** 状态 */
  status: number;
  /** 租户用户名 */
  tmbName: string;
  /** 创建时间(时间戳) */
  createTime: number;
  /** 更新时间(时间戳) */
  updateTime: number;
  /** 题目类型名称 */
  questionTypeName: string;
  /** 学科名称 */
  subjectName: string;
  /** 学段名称 */
  stageName: string;
  /** 年级名称 */
  gradeName: string;
  /** 教材版本名称 */
  textbookVersionName: string;
  /** 教材名称 */
  textbookVolumeName: string;
  /** 教材章节ID列表 */
  textbookChapterIds: string[];
  /** 教材章节名称列表 */
  textbookChapterNames: string[];
  /** 地区名称 */
  areaName: string;
  /** 难度名称 */
  difficultyName: string;
  /** 答题形式：1选择；2填空；3解答 */
  answerType: number;
  /** 待处理反馈数量 */
  feedbackCount: number | null;
  /** 题目标签列表 */
  tags: string[];
  /** 题目知识点ID列表 */
  knowledgePointIds: number[];
  /** 题目知识点名称列表 */
  knowledgePointNames: string[];
  /** 学生提交的答案 */
  submitQuestion: SubmitQuestion | null;
  /** 子题目列表 */
  children: QuestionDetail[];
  /** 题号 */
  questionOrder: number | null;
  /** 题目类型编码 */
  questionTypeCode: string;
  /** 学生笔记 */
  studentsNotes: string | null;
}

/**
 * 学生作业题目
 */
export interface StudentHomeworkQuestion {
  /** 主键ID */
  id: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 是否删除 */
  isDeleted: number;
  /** 作业ID */
  homeworkId: number;
  /** 题目ID */
  questionId: number;
  /** 父作业题目ID */
  parentId: number;
  /** 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层 */
  level: number;
  /** 评分标准 */
  gradingCriteria: string;
  /** 题目分值 */
  score: number;
  /** 题号：题目在作业中的顺序 */
  questionOrder: number;
  /** 内容布局ID */
  contentLayoutId: number;
  /** 子题目列表 */
  children: StudentHomeworkQuestion[];
  /** 题目详情 */
  question: QuestionDetail;
}

/**
 * 学生订正提交记录
 */
export interface StudentReviseSubmitRecord {
  /** 主键ID */
  id?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 是否删除 (逻辑删除标记) */
  isDeleted?: number;
  /** 作业学生ID */
  homeworkStudentId?: number;
  /** 订正次数 */
  reviseCount?: number;
  /** 用时(秒) */
  timeSecond?: number;
  /** 提交时间 */
  submitTime?: string;
  /** 状态：0-待提交，1-已提交 */
  status?: number;
  /** 确认批改时间 */
  confirmTime?: string;
  /** 得分 */
  score?: number;
  /** 作业总分值 */
  totalScore?: number;
  /** 是否为订正 0-否；1-是 */
  isRevise?: number;
  /** 归纳错题规则 1-订正后，需要批改；2-订正后，向学生公布答案解析 */
  addErrorRule?: number;
  /** 打回原因 */
  rejectReason?: string;
  /** 共计题目数 */
  totalCount?: number;
  /** 已完成题目数 */
  completedCount?: number;
  /** 未完成题目数 */
  uncompletedCount?: number;
  /** 学生作业提交题目列表 */
  questionList?: StudentHomeworkQuestion[];
  /** 学生纸质作业提交文件列表 */
  paperFiles?: string[];
}

export interface WordCloudRequest {
  /** 业务类型 */
  businessType: string;
  /** 消息列表 */
  messages: [
    {
      /** 数据ID */
      dataId: string;
      /** 角色 */
      role: string;
      /** 是否隐藏 */
      hideInUI: boolean;
      /** 内容 */
      content: string;
    }
  ];
  /** 是否流式 */
  stream: boolean;
}

export interface WordCloudResponse {
  /** 词汇 */
  vocabulary: string;
  /** 词频 */
  frequency: number;
}

// ==================== 能力分组策略相关类型定义 ====================

/**
 * 能力分组依据
 */
export interface AbilityGroupedBasis {
  /**
   * 主键ID
   */
  id: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 策略ID
   */
  strategyId: number;
  /**
   * 历史作业ID
   */
  pastHomeworkId: number;
  /**
   * 历史作业名称
   */
  pastHomeworkName: string;
}

/**
 * 能力层级
 */
export interface AbilityLayer {
  /**
   * 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level: number;
  /**
   * 学生列表
   */
  studentList: StudentInfo[];
  /**
   * 学生数量
   */
  studentCount: number;
}

/**
 * 能力分组规则
 */
export interface AbilityGroupedRule {
  /**
   * 主键ID
   */
  id: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 作业ID
   */
  homeworkId: number;
  /**
   * 策略ID
   */
  strategyId: number;
  /**
   * 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
   */
  level: number;
  /**
   * 最小分数
   */
  min: number;
  /**
   * 最大分数
   */
  max: number;
}

/**
 * 能力分组策略
 */
export interface AbilityGroupedStrategy {
  /**
   * 主键ID
   */
  id: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 是否删除，0-未删除，1-已删除
   */
  isDeleted: number;
  /**
   * 策略名称
   */
  name: string;
  /**
   * 能力分组方法：1-基于历史作业成绩分组
   */
  abilityGroupedMethod: number;
  /**
   * 归属：1-个人，2-学校，3-华云自有，4-第三方
   */
  attribution: number;
  /**
   * 租户ID
   */
  tenantId: number;
  /**
   * 租户成员ID
   */
  tmbId: number;
  /**
   * 创建用户ID
   */
  createUser: number;
  /**
   * 更新用户ID
   */
  updateUser: number;
  /**
   * 创建用户名称
   */
  createUserName: string;
  /**
   * 能力分组依据列表
   */
  abilityGroupedBasisList: AbilityGroupedBasis[];
  /**
   * 能力层级列表
   */
  abilityLayerList: AbilityLayer[];
  /**
   * 能力分组规则列表
   */
  abilityGroupedRuleList: AbilityGroupedRule[];
}
