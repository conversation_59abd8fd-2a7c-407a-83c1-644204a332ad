export interface DictSublistRequest {
  code: 'feedback_type';
}

export interface DictSublistResponse {
  /** 反馈类型ID */
  id: string;
  /** 反馈类型名称 */
  dictValue: string;
}

export interface FeedbackSubmitRequest {
  /** 反馈类型ID */
  dictId: string;
  /** 反馈内容 */
  feedbackContent: string;
  /** 图片列表 */
  fileKeys?: string[];
}

export interface FeedbackUpdateRequest {
  /** 反馈ID */
  id: string;
  /** 反馈类型ID */
  dictId?: string;
  /** 反馈内容 */
  feedbackContent?: string;
  /** 图片列表 */
  fileKeys?: string[];
}

export interface FeedbackDetailRequest {
  /** 反馈类型ID */
  id: string;
}

export interface FeedbackDetailResponse {
  /** 反馈ID */
  id: string;
  /** 用户ID */
  userId: number;
  /** 租户ID */
  tenantId: number;
  /** 租户人员ID */
  tmbId: number;
  /** 反馈类型字典ID */
  dictId: string;
  /** 反馈内容 */
  feedbackContent: string;
  /** 反馈时间 */
  feedbackTime: string;
  /** 回复内容 */
  replyContent?: string;
  /** 回复人用户ID */
  replyUserId?: number;
  /** 回复时间 */
  replyTime?: string;
  /** 状态 */
  status: number;
  /** 用户名称 */
  userName?: string;
  /** 用户账号 */
  account?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 反馈类型 */
  feedbackType?: string;
  /** 反馈文件列表 */
  feedbackFiles?: FeedbackFile[];
  /** 回复文件列表 */
  replyFiles?: FeedbackFile[];
}

export interface FeedbackFile {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
}
export interface FeedbackPageRequest {
  /** 账号 */
  account?: string;
  /** 当前页 */
  current: number;
  /** 反馈内容 */
  feedbackContent?: string;
  /** 每页条数 */
  pageSize: number;
  /** 用户名称 */
  userName?: string;
  /** 反馈类型ID */
  dictId?: string;
}
export interface FeedbackPageResponse {
  records: FeedbackRecord[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

interface FileInfo {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
}

interface FeedbackRecord {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  userId: string;
  tenantId: string;
  tmbId: string;
  feedbackContent: string;
  replyContent: string;
  replyUserId: string;
  replyTime: string;
  /** 0-待处理 1-已处理 */
  status: number;
  userName: string;
  account: string;
  feedbackFiles: FileInfo[];
  replyFiles: FileInfo[];
  feedbackTime: string;
  dictId: string;
  tenantName: string;
  feedbackType: string;
}

export interface DeleteFeedbackRequest {
  /** 反馈ID */
  id: string;
}
