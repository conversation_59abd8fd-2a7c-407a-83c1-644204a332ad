import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, Flex, Button, ModalBody } from '@chakra-ui/react';
import { Select, message } from 'antd';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useTranslation } from 'next-i18next';
import MyModal from '@/components/MyModal';
import { setClientSchoolDeptManageCopy } from '@/api/tenant/teamManagement/teach';
import { getClientSemesterGetDelayList } from '@/api/tenant/teamManagement/semester';
import { ClientSemesterGetDelayList, Semester } from '@/types/api/tenant/teamManagement/semester';

const { Option } = Select;

interface FormData {
  targetSemesterId: string;
}

interface SynchronizationModalProps {
  activeTab: string;
  semesterData: { year: string; type: number };
  semesterId: string;
  onClose: (submited: boolean, settingId?: string) => void;
  onSuccess: () => void | Promise<void>;
}

const SynchronizationModal: React.FC<SynchronizationModalProps> = ({
  activeTab,
  semesterData,
  semesterId,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [semesters, setSemesters] = useState<ClientSemesterGetDelayList>([]);
  const [selectedSemester, setSelectedSemester] = useState<string | null>(null);

  const {
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<FormData>({
    defaultValues: {
      targetSemesterId: ''
    }
  });

  const { mutate, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: FormData) => {
      return setClientSchoolDeptManageCopy({ sourceSemesterId: semesterId, ...data });
    },
    onSuccess(res) {
      onSuccess();
      onClose(true, res.id);
    },
    successToast: '操作成功'
  });

  useEffect(() => {
    fetchSemesters();
  }, []);

  const fetchSemesters = async () => {
    try {
      const response = await getClientSemesterGetDelayList(semesterId);
      setSemesters(response);
      const current = response.find((sem) => sem.isCurrent === 1);
      if (current) {
        setSelectedSemester(current.id);
        setValue('targetSemesterId', current.id);
      }
    } catch (error) {
      message.error('获取学期列表失败');
    }
  };

  const handleSemesterChange = (value: string) => {
    setSelectedSemester(value);
    setValue('targetSemesterId', value);
  };

  const onSubmit: SubmitHandler<FormData> = (data) => {
    mutate(data);
  };

  const getSemesterLabel = (sem: Semester) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  return (
    <MyModal isOpen={true} title="同步其他学期" onClose={() => onClose(false)} isCentered>
      <ModalBody>
        <Box p="20px">
          <FormControl>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                {`您将${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期 教学管理设置对应${activeTab === '1' ? '年级班级' : '学科'}及教师同步至:`}
              </FormLabel>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.targetSemesterId}>
            <Select
              value={selectedSemester}
              style={{ width: '100%' }}
              dropdownStyle={{ zIndex: 2000 }}
              placeholder="请选择学期"
              onChange={handleSemesterChange}
            >
              {semesters.map((sem) => (
                <Option key={sem.id} value={sem.id}>
                  {getSemesterLabel(sem)}
                </Option>
              ))}
            </Select>
            {errors.targetSemesterId && (
              <Box color="#F53F3F" fontSize="13px" mt="8px">
                {errors.targetSemesterId.message}
              </Box>
            )}
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  variant={'grayBase'}
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SynchronizationModal;
