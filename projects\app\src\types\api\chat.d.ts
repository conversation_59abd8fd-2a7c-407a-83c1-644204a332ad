import { ChatBoxInputType } from '@/components/ChatBox/type';
import { ModeTypeEnum } from '@/constants/api/app';
import { DataSource } from '@/constants/common';
import { AppChatConfigType, VariableItemType } from '@/fastgpt/global/core/app/type';
import {
  ChatItemValueTypeEnum,
  ChatStatusEnum,
  FeedbackTypeEnum
} from '@/fastgpt/global/core/chat/constants';
import {
  AdminFbkType,
  ChatHistoryItemResType,
  ChatItemType,
  ChatHistoryItemType as FastGPTChatHistoryItemType,
  SystemChatItemType,
  ToolModuleResponseItemType,
  UserChatItemType
} from '@/fastgpt/global/core/chat/type';
import { FileMetaType } from './file';

export type UploadChatFileProps = {
  chatId: string;
  name: string;
  rawTextLength: number;
  hashRawText: string;
  fileMetadata?: Record<string, any>;
};

export type ChatFileMetaType = {
  id: string; // AIFileObjectSchema._id 不返回第三方的id
  name: string; // AIFileObjectSchema.originalName 返回文件原名
  contentType: string; // AIFileObjectSchema
  status: string;
  size: number;
  chatId: string;
  metadata?: {
    imgSrc?: string; // 图片文件
  };
};

export type GetChatSpeechProps = {
  ttsConfig: AppTTSConfigType;
  input: string;
  shareId?: string;
};

/* ---------- chat ----------- */
export type InitChatProps = {
  tenantAppId?: string;
  chatId?: string;
  loadCustomFeedbacks?: boolean;
};

export type InitChatSharingProps = {
  tenantAppId?: string;
  chatId?: string;
  loadCustomFeedbacks?: boolean;
  deviceId?: string;
};

export type InitOutLinkChatProps = {
  chatId?: string;
  shareId?: string;
  outLinkUid?: string;
};

type SceneListType = {
  id?: string;
  isDisplayed?: number;
  tenantAppId?: string;
  tenantSceneId: string;
  tenantSceneName?: string;
};

export type InitChatResponse = {
  id?: string;
  chatId?: string;
  appId?: string;
  userAvatar?: string;
  title: string;
  customTitle?: string;
  appName: string;
  appAvatarUrl: string;
  variables: Record<string, any>;
  history: ChatItemType[];
  sceneId?: string;
  source?: DataSource;
  editStatus?: number;
  finalAppId?: string;
  app: {
    chatModels?: string[];
    chatConfig?: AppChatConfigType;
    name: string;
    avatar: string;
    intro: string;
    canUse?: boolean;
    useInternet?: boolean; // 是否使用搜索
    useFileUpload?: boolean; // 是否使用文件上传
    type: string;
  };
  sceneList: SceneListType[];
};

/* ---------- history ----------- */
export type getHistoriesProps = {
  appId?: string;
  // share chat
  shareId?: string;
  outLinkUid?: string; // authToken/uid
  keyword?: string;
  current?: number;
  size?: number;
};

export type FileContentParams = {
  tenantAppId: string;
  fileUrl: string;
  filename: string;
  chatId: string;
};

export type FileContentType = {
  filename: string;
  chatId: string;
  content: string;
  fileType: string;
};

export type UpdateHistoryProps = {
  chatId: string;
  title: string;
  updateTime?: Date;
  tenantAppId?: string;
  id?: string;
  appName?: string;
  appAvatarUrl?: string;
  files?: FileType[];
};

export type UpdateHistoryBySharingProps = {
  chatId: string;
  title: string;
  updateTime?: Date;
  tenantAppId?: string;
  id?: string;
  appName?: string;
  appAvatarUrl?: string;
  files?: FileType[];
  deviceId?: string;
};

export type DelHistoryProps = {
  id: string;
  chatId: string;
};

export type ClearHistoriesProps = {
  appId?: string;
  shareId?: string;
  outLinkUid?: string;
};

/* -------- chat item ---------- */
export type DeleteChatItemProps = {
  tenantAppId: string;
  chatId: string;
  contentId?: string;
  shareId?: string;
  outLinkUid?: string;
};

export type UpdateChatItemProps = {
  dataId: string;
  value?: string;
  content?: string;
  responseData?: string;
  feedbackType?: FeedbackTypeEnum;
  customFeedback?: string;
};

export type UpdateChatItemBySharingProps = {
  dataId: string;
  value?: string;
  content?: string;
  responseData?: string;
  feedbackType?: FeedbackTypeEnum;
  customFeedback?: string;
  deviceId?: string;
};

export type CleanChatProps = {
  chatId: string;
};

export type AdminUpdateFeedbackParams = AdminFbkType & {
  appId: string;
  chatId: string;
  chatItemId: string;
};

export type UpdateChatFeedbackProps = {
  tenantAppId: string;
  chatId: string;
  chatItemId: string;
  userGoodFeedback?: boolean;
  userBadFeedback?: string;
};

export type ChatPptUrlParams = {
  topic: string;
};

export type ChatFileType = {
  id: string;
  name: string;
  svgIcon?: SvgIconNameType;
  imgIcon?: string;
  type: string;
  sizeText: string;
};

export type DynamicFormDataType = {
  [key: string]: string | FileMetaType[] | string[] | [];
} | null;

export type DynamicFormValue = string | FileMetaType[] | string[] | [];

export type BackgroundFileType = {
  fileName: string;
  fileType?: string;
  fileUrl: string;
  fileContent: string;
  fileSize?: number;
  fileKey?: string;
};
