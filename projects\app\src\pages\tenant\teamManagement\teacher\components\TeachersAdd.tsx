import {
  <PERSON>ton,
  Flex,
  FormControl,
  FormLabel,
  Select,
  Text,
  IconButton,
  Divider,
  useToast
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { DeleteIcon } from '@chakra-ui/icons';
import {
  getAccountList,
  getTenantSubjectList,
  getCurrentSemester,
  getSchoolDeptTree,
  createTeacher,
  getInfoByAccount
} from '@/api/teacher';
import type {
  TeacherAccountItem,
  TenantSubjectItem,
  SchoolDeptTreeItem,
  TeacherCreateParams
} from '@/types/api/teacher';
import {
  SCHOOL_MANAGE_TEACHER_TYPE_MAP,
  SCHOOL_SUBJECT_TEACHER_TYPE_MAP,
  SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT,
  SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT
} from '@/constants/teacher';
import MyModal from '@/components/MyModal';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';
import MyBox from '@/components/common/MyBox';
import { extractSubjectListFromResponse } from '@/utils/subjectUtils';

const GENDER_OPTIONS = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
];

// 定义类型，包含 filteredClassTree
interface AdminJob {
  teachType: string;
  deptId: string;
  deptName: string;
  disabled: boolean;
  filteredClassTree: SchoolDeptTreeItem[];
}
interface TeachJob {
  teachType: string;
  deptId: string;
  deptName: string;
  subjectId: string;
  disabled: boolean;
  filteredClassTree: SchoolDeptTreeItem[];
}

interface TeachersAddProps {
  onSuccess?: () => void;
  onClose?: () => void;
  isOpen: boolean;
}

const TeachersAdd = ({ onSuccess, onClose, isOpen }: TeachersAddProps) => {
  const toast = useToast();
  const [accountList, setAccountList] = useState<TeacherAccountItem[]>([]);
  const [subjectList, setSubjectList] = useState<TenantSubjectItem[]>([]);
  const [classTree, setClassTree] = useState<SchoolDeptTreeItem[]>([]);
  const [semesterId, setSemesterId] = useState<string | number>('');
  const [form, setForm] = useState({
    name: '',
    tmbId: '',
    phone: '',
    gender: '' // 初始值设为''
  });
  const [adminJobs, setAdminJobs] = useState<AdminJob[]>([
    { teachType: '', deptId: '', deptName: '', disabled: false, filteredClassTree: [] }
  ]);
  const [teachJobs, setTeachJobs] = useState<TeachJob[]>([
    {
      teachType: '',
      deptId: '',
      deptName: '',
      subjectId: '',
      disabled: false,
      filteredClassTree: []
    }
  ]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getAccountList().then((res) => {
      console.log('TeachersAdd getAccountList 返回:', res);
      console.log('TeachersAdd accountList:', JSON.stringify(res));
      console.log('TeachersAdd accountList 类型:', typeof res);
      console.log('TeachersAdd accountList 是否为数组:', Array.isArray(res));
      if (Array.isArray(res) && res.length > 0) {
        console.log('TeachersAdd 第一个账户数据结构:', res[0]);
        console.log('TeachersAdd 第一个账户的type字段:', res[0].type);
      }
      setAccountList(res || []);
    });
    getTenantSubjectList().then((res) => {
      const list = extractSubjectListFromResponse(res);
      setSubjectList(list);
    });
    getCurrentSemester().then((res) => {
      if (res && res.id) {
        setSemesterId(res.id);
        getSchoolDeptTree({ semesterId: res.id }).then((treeRes) => {
          setClassTree(Array.isArray(treeRes) ? treeRes : treeRes?.data || []);
        });
      } else {
        console.warn('当前学期信息为空，无法获取班级树');
        setClassTree([]);
      }
    });
  }, []);

  // 递归获取所有叶子节点（班级）
  function getAllLeafNodes(tree: SchoolDeptTreeItem[]): SchoolDeptTreeItem[] {
    let result: SchoolDeptTreeItem[] = [];
    for (const node of tree) {
      if (!node.children || node.children.length === 0) {
        result.push(node);
      } else {
        result = result.concat(getAllLeafNodes(node.children));
      }
    }
    return result;
  }
  // 过滤班级树到指定层级
  const getFilteredClassTree = (
    tree: SchoolDeptTreeItem[],
    teachType: number,
    isAdmin: boolean
  ) => {
    let classLimit = isAdmin
      ? SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT[teachType]
      : SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT[teachType];
    if (!classLimit || classLimit.includes('不可选')) return [];
    if (classLimit.includes('学段')) return tree;
    if (classLimit.includes('年级')) return tree.flatMap((node) => node.children || []);
    if (classLimit.includes('班级')) return getAllLeafNodes(tree);
    return tree;
  };

  // 行政职务操作
  const handleAddAdminJob = () => {
    setAdminJobs([
      ...adminJobs,
      { teachType: '', deptId: '', deptName: '', disabled: false, filteredClassTree: [] }
    ]);
  };
  const handleRemoveAdminJob = (idx: number) => {
    setAdminJobs(adminJobs.filter((_, i) => i !== idx));
  };
  // 修改 handleAdminJobChange，切换 teachType 时过滤班级树
  const handleAdminJobChange = (idx: number, key: string, value: any) => {
    setAdminJobs(
      adminJobs.map((item, i) => {
        if (i === idx) {
          if (key === 'teachType') {
            return {
              ...item,
              teachType: value,
              deptId: '',
              deptName: '',
              filteredClassTree: getFilteredClassTree(classTree, Number(value), true)
            };
          }
          return { ...item, [key]: value };
        }
        return item;
      })
    );
  };

  // 教学职务操作
  const handleAddTeachJob = () => {
    setTeachJobs([
      ...teachJobs,
      {
        teachType: '',
        deptId: '',
        deptName: '',
        subjectId: '',
        disabled: false,
        filteredClassTree: []
      }
    ]);
  };
  const handleRemoveTeachJob = (idx: number) => {
    setTeachJobs(teachJobs.filter((_, i) => i !== idx));
  };
  // 修改 handleTeachJobChange，切换 teachType 时过滤班级树
  const handleTeachJobChange = (idx: number, key: string, value: any) => {
    setTeachJobs(
      teachJobs.map((item, i) => {
        if (i === idx) {
          if (key === 'teachType') {
            return {
              ...item,
              teachType: value,
              deptId: '',
              deptName: '',
              subjectId: '',
              filteredClassTree: getFilteredClassTree(classTree, Number(value), false)
            };
          }
          return { ...item, [key]: value };
        }
        return item;
      })
    );
  };

  // 递归渲染班级树，优先显示 deptName，将七年级改为初一
  const renderClassOptions = (tree: SchoolDeptTreeItem[]): JSX.Element[] =>
    tree.flatMap((node) => [
      <option key={node.id} value={node.id}>
        {formatClassName(node.deptName || node.name || node.id, node.id)}
      </option>,
      ...(node.children ? renderClassOptions(node.children) : [])
    ]);

  // 格式化班级名称，将七年级改为初一
  const formatClassName = (name: string, nodeId: string | number): string => {
    if (!name) return name;
    let formattedName = name.replace(/七年级/g, '初一');
    if (formattedName.includes('班')) {
      // 查找父级年级
      const findGradeForClass = (tree: SchoolDeptTreeItem[], classId: string | number): string => {
        for (const node of tree) {
          if (node.children) {
            for (const child of node.children) {
              if (child.id === classId) {
                return node.deptName || node.name || '';
              }
              if (child.children) {
                for (const grandChild of child.children) {
                  if (grandChild.id === classId) {
                    return child.deptName || child.name || '';
                  }
                }
              }
            }
          }
        }
        return '';
      };
      const gradeName = findGradeForClass(classTree, nodeId);
      if (gradeName) {
        const formattedGrade = gradeName.replace(/七年级/g, '初一');
        return `${formattedGrade}${formattedName}`;
      }
    }
    return formattedName;
  };

  // classTree 加载后自动刷新所有 filteredClassTree
  useEffect(() => {
    if (classTree.length > 0) {
      setAdminJobs((adminJobs) =>
        adminJobs.map((item) => ({
          ...item,
          filteredClassTree: item.teachType
            ? getFilteredClassTree(classTree, Number(item.teachType), true)
            : []
        }))
      );
      setTeachJobs((teachJobs) =>
        teachJobs.map((item) => ({
          ...item,
          filteredClassTree: item.teachType
            ? getFilteredClassTree(classTree, Number(item.teachType), false)
            : []
        }))
      );
    }
  }, [classTree]);

  // 提交表单
  const handleSubmit = async () => {
    if (!form.name || !form.tmbId || !form.phone || !form.gender) {
      toast({ title: '请填写完整信息', status: 'warning' });
      return;
    }
    setLoading(true);
    try {
      const params: TeacherCreateParams = {
        tmbId: Number(form.tmbId),
        name: form.name,
        phone: form.phone,
        gender: Number(form.gender),
        schoolManageTeachersDetail: adminJobs
          .filter((j) => j.teachType)
          .map((j) => ({
            teachType: Number(j.teachType),
            deptId: j.deptId || '',
            deptName: j.deptName || ''
          })),
        schoolSubjectTeachersDetail: teachJobs
          .filter((j) => j.teachType)
          .map((j) => ({
            teachType: Number(j.teachType),
            deptId: j.deptId || '',
            deptName: j.deptName || '',
            subjectId: j.subjectId || ''
          }))
      };
      console.log('保存参数', params);
      await createTeacher(params);
      toast({ title: '添加成功', status: 'success' });
      if (onSuccess) onSuccess();
    } catch (e) {
      toast({ title: '添加失败', status: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <MyModal
      isOpen={isOpen}
      onClose={onClose || (() => {})}
      title="添加教师"
      w="684px"
      maxH="748px"
      isLoading={loading}
    >
      <MyBox p={6} overflow="auto" w="100%" flex={1} maxH="calc(748px - 80px)" isLoading={loading}>
        <Flex gap={4} mb={3}>
          <FormControl isRequired flex={1}>
            <FormLabel fontSize="14px" mb={1}>
              * 姓名：
            </FormLabel>
            <MyInput
              placeholder="请输入姓名"
              value={form.name}
              onChange={(e) => setForm((f) => ({ ...f, name: e.target.value }))}
              size="sm"
              borderRadius="md"
              bg="#F2F3F5"
              border="none"
              _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
              _hover={{ bg: '#F2F3F5' }}
            />
          </FormControl>
          <FormControl isRequired flex={1}>
            <FormLabel fontSize="14px" mb={1}>
              * 账号：
            </FormLabel>
            <MySelect
              placeholder="请选择账号"
              value={form.tmbId}
              onchange={(value) => setForm((f) => ({ ...f, tmbId: value }))}
              list={accountList.map((s) => ({
                label: s.account,
                value: s.id
              }))}
              size="sm"
              borderRadius="md"
              bg="#F2F3F5"
              border="none"
              _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
              _hover={{ bg: '#F2F3F5' }}
            />
            <Text color="gray.400" fontSize="12px" mt={1}>
              需从现有成员管理账号中选取
            </Text>
          </FormControl>
        </Flex>
        <Flex gap={4} mb={4}>
          <FormControl isRequired flex={1}>
            <FormLabel fontSize="14px" mb={1}>
              * 手机号：
            </FormLabel>
            <MyInput
              placeholder="请输入手机号"
              value={form.phone}
              onChange={(e) => setForm((f) => ({ ...f, phone: e.target.value }))}
              size="sm"
              borderRadius="md"
              bg="#F2F3F5"
              border="none"
              _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
              _hover={{ bg: '#F2F3F5' }}
            />
          </FormControl>
          <FormControl isRequired flex={1}>
            <FormLabel fontSize="14px" mb={1}>
              * 性别：
            </FormLabel>
            <MySelect
              placeholder="请选择性别"
              value={form.gender}
              onchange={(value) => setForm((f) => ({ ...f, gender: value }))}
              list={GENDER_OPTIONS.map((opt) => ({
                label: opt.label,
                value: opt.value
              }))}
              size="sm"
              borderRadius="md"
              bg="#F2F3F5"
              border="none"
              _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
              _hover={{ bg: '#F2F3F5' }}
            />
          </FormControl>
        </Flex>
        {/* 行政职务 */}
        <Text fontWeight="bold" mt={6} mb={3} fontSize="16px">
          行政职务
        </Text>
        {adminJobs.map((item, idx) => {
          const teachTypeNum = Number(item.teachType);
          const classLimit = SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT[teachTypeNum];
          const classDisabled = classLimit && classLimit.includes('不可选');
          return (
            <Flex key={idx} align="center" mb={2} gap={2}>
              <MySelect
                w="180px"
                value={item.teachType}
                onchange={(value) => handleAdminJobChange(idx, 'teachType', value)}
                placeholder="职务类型"
                list={Object.entries(SCHOOL_MANAGE_TEACHER_TYPE_MAP).map(([value, label]) => ({
                  label,
                  value
                }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              />
              <Select
                w="180px"
                value={item.deptId}
                onChange={(e) => handleAdminJobChange(idx, 'deptId', e.target.value)}
                isDisabled={!item.teachType || !!item.disabled || !!classDisabled}
                placeholder="班级"
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              >
                {renderClassOptions(item.filteredClassTree || [])}
              </Select>
              {item.disabled ? null : (
                <IconButton
                  aria-label="删除"
                  icon={<DeleteIcon />}
                  colorScheme="red"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveAdminJob(idx)}
                />
              )}
            </Flex>
          );
        })}
        <Button
          size="sm"
          variant="outline"
          onClick={handleAddAdminJob}
          mb={4}
          colorScheme="purple"
          leftIcon={<Text fontSize="16px">+</Text>}
          borderColor="purple.500"
          color="purple.500"
          _hover={{ bg: 'purple.50' }}
        >
          添加职务
        </Button>
        {/* 教学职务 */}
        <Text fontWeight="bold" mt={6} mb={3} fontSize="16px">
          教学职务
        </Text>
        {teachJobs.map((item, idx) => {
          const teachTypeNum = Number(item.teachType);
          const classLimit = SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT[teachTypeNum];
          const classDisabled = classLimit && classLimit.includes('不可选');
          return (
            <Flex key={idx} align="center" mb={2} gap={2}>
              <MySelect
                w="180px"
                value={item.teachType}
                onchange={(value) => handleTeachJobChange(idx, 'teachType', value)}
                placeholder="职务类型"
                list={Object.entries(SCHOOL_SUBJECT_TEACHER_TYPE_MAP).map(([value, label]) => ({
                  label,
                  value
                }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              />
              <Select
                w="180px"
                value={item.deptId}
                onChange={(e) => handleTeachJobChange(idx, 'deptId', e.target.value)}
                isDisabled={!item.teachType || !!item.disabled || !!classDisabled}
                placeholder="班级"
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              >
                {renderClassOptions(item.filteredClassTree || [])}
              </Select>
              <MySelect
                w="180px"
                value={item.subjectId}
                onchange={(value) => handleTeachJobChange(idx, 'subjectId', value)}
                isDisabled={item.disabled}
                placeholder="学科"
                list={subjectList.map((sub) => ({
                  label: sub.subjectName,
                  value: sub.subjectId
                }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              />
              {item.disabled ? null : (
                <IconButton
                  aria-label="删除"
                  icon={<DeleteIcon />}
                  colorScheme="red"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveTeachJob(idx)}
                />
              )}
            </Flex>
          );
        })}
        <Button
          size="sm"
          variant="outline"
          onClick={handleAddTeachJob}
          mb={4}
          colorScheme="purple"
          leftIcon={<Text fontSize="16px">+</Text>}
          borderColor="purple.500"
          color="purple.500"
          _hover={{ bg: 'purple.50' }}
        >
          添加职务
        </Button>
        {/* 底部按钮 */}
        <Divider my={6} />
        <Flex justify="flex-end" gap={4}>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button colorScheme="purple" isLoading={loading} onClick={handleSubmit}>
            添加
          </Button>
        </Flex>
      </MyBox>
    </MyModal>
  );
};

export default TeachersAdd;
