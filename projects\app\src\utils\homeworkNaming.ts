/**
 * 作业命名工具函数
 */

/**
 * 获取作业任务类型的中文名称
 */
export const getHomeworkTypeName = (typeId: number): string => {
  const typeMapping: Record<number, string> = {
    0: '智慧作业',
    1: '分层作业',
    2: '写作作业',
    3: '自定义作业'
  };
  return typeMapping[typeId] || '作业';
};

/**
 * 生成默认的作业名称
 * 格式：${当前日期，YYYY年MM月DD日} ${学科} ${作业任务类型}
 * 当学科名称为空时：${当前日期，YYYY年MM月DD日} ${作业任务类型}
 */
export const generateDefaultHomeworkName = (
  subjectName?: string,
  homeworkType?: number
): string => {
  // 获取当前日期，格式化为YYYY年MM月DD日（补零）
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0'); // getMonth() 返回 0-11，补零
  const day = now.getDate().toString().padStart(2, '0'); // 补零
  const dateStr = `${year}年${month}月${day}日`;

  // 获取作业类型名称
  const typeName = homeworkType ? getHomeworkTypeName(homeworkType) : '作业';

  // 🔧 修复：当学科名称存在时才加入，避免多余空格
  const subject = subjectName?.trim();

  // 组合生成默认名称
  const defaultName = subject ? `${dateStr} ${subject} ${typeName}` : `${dateStr} ${typeName}`;

  return defaultName;
};
