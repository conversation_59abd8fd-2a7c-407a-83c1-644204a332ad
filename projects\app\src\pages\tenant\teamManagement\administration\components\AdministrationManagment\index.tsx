import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Box, Flex, Button, useDisclosure } from '@chakra-ui/react';
import { Card, Empty } from 'antd';
import { vwDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { TmbUser as TeachTmbUser } from '@/types/api/tenant/teamManagement/teach';
import styles from '../../../index.module.scss';

import { Toast } from '@/utils/ui/toast';
import {
  getClientSchoolManageTeacherTree,
  updateClientSchoolManageTeacher
} from '@/api/tenant/teamManagement/administration';
import { DepartmentNode, TmbUser } from '@/types/api/tenant/teamManagement/administration';
import { TeachType } from '@/constants/tenant/teamManagement/administration';
import SettingModal from '../SettingModal';

interface AdministrationManagementProps {
  onEditTeachers?: (
    level: string,
    id: string,
    gradeName?: string,
    className?: string,
    tmbIds?: string[]
  ) => void;
  semesterData: {
    year: string;
    type: 1 | 2;
  };
  semesterId: string;
  highlightedTmbIds: number[];
  refreshList: () => void;
}

const renderEmptyState = (
  semesterData: { year: string; type: 1 | 2 },
  tabDescription: string = '年班级'
) => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    height="100%"
    pt="200px"
  >
    <SvgIcon name="empty" w="100px" h="100px" />
    {semesterData.year && semesterData.type ? (
      <Box fontSize="16px" color="#303133" textAlign="center" mt={4} w="390px">
        {`当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂无${tabDescription}数据。`}
      </Box>
    ) : (
      <Empty description="请选择学期后进行操作。" />
    )}
  </Box>
);

const AdministrationManagement: React.FC<AdministrationManagementProps> = ({
  onEditTeachers,
  semesterData,
  semesterId,
  highlightedTmbIds,
  refreshList
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [treeData, setTreeData] = useState<DepartmentNode[]>([]);

  // 弹窗状态控制
  const [modalSettingId, setModalSettingId] = useState<string>('');
  const [modalTitle, setModalTitle] = useState<string>('');
  const [modalDeptName, setModalDeptName] = useState<string>('');
  const [modalTmbIds, setModalTmbIds] = useState<string[]>([]);
  const [modalLevel, setModalLevel] = useState<string>('');
  const [modalDepartmentType, setModalDepartmentType] = useState<'primary' | 'junior' | undefined>(
    undefined
  );
  const [modalDeptId, setModalDeptId] = useState<string>('');

  const {
    isOpen: isOpenSettingModal,
    onOpen: onOpenSettingModal,
    onClose: onCloseSettingModal
  } = useDisclosure();

  // 获取行政组织数据
  const fetchData = useCallback(async () => {
    if (!semesterId) return;

    try {
      const response = await getClientSchoolManageTeacherTree(semesterId);
      if (response && response.treeList) {
        setTreeData(response.treeList);
      }
    } catch (error) {
      Toast.error('获取行政组织数据失败');
    }
  }, [semesterId]);

  // 当学期ID变化时获取数据
  useEffect(() => {
    if (semesterId) {
      fetchData();
    }
  }, [semesterId, fetchData]);

  // 计算最大班级数
  const maxClassCounts = useMemo(() => {
    return (
      treeData[0]?.children?.reduce(
        (acc, department) => {
          const departmentMax =
            department.children?.reduce((gradeMax, grade) => {
              return Math.max(gradeMax, grade.children?.length || 0);
            }, 0) || 0;

          if (department.deptName.includes('小学')) {
            acc.primary = Math.max(acc.primary, departmentMax);
          } else if (department.deptName.includes('初中')) {
            acc.middle = Math.max(acc.middle, departmentMax);
          }

          return acc;
        },
        { primary: 0, middle: 0 }
      ) || { primary: 0, middle: 0 }
    );
  }, [treeData]);

  // 处理关闭弹窗事件
  const handleModalClose = (submitted: boolean, settingId: string, selectedTmbIds: number[]) => {
    if (submitted && selectedTmbIds && selectedTmbIds.length > 0) {
      // 确定teachType
      let teachType: TeachType;
      switch (modalLevel) {
        case 'school':
          teachType = TeachType.TEACHING_ADMIN;
          break;
        case 'department':
          teachType =
            modalDepartmentType === 'primary'
              ? TeachType.PRIMARY_SECTION_ADMIN
              : TeachType.JUNIOR_SECTION_ADMIN;
          break;
        case 'grade':
          teachType = TeachType.GRADE_DIRECTOR;
          break;
        case 'class':
          teachType = TeachType.CLASS_MASTER;
          break;
        default:
          teachType = TeachType.TEACHING_ADMIN;
      }

      // 构建并提交请求
      const requestData = {
        deptId: modalDeptId,
        teachType: teachType,
        semesterId: Number(semesterId),
        tmbIds: selectedTmbIds
      };

      updateClientSchoolManageTeacher(requestData)
        .then(() => {
          Toast.success('操作成功');
          fetchData();
          refreshList();
        })
        .catch((error) => {
          console.error('API调用失败:', error);
          Toast.error('操作失败');
        });
    }

    onCloseSettingModal();
  };

  // 渲染用户列表
  const renderTmbUsers = (users: TmbUser[]) => {
    const renderUserSpans = (users: TmbUser[]): JSX.Element[] => {
      return users.map((user) => (
        <span
          key={`user-${user.tmbId || Math.random().toString(36).substring(2, 11)}`}
          style={{
            color: highlightedTmbIds.includes(Number(user.tmbId)) ? 'red' : '#000'
          }}
        >
          {user.userName}
        </span>
      ));
    };

    const combineUserSpans = (userSpans: JSX.Element[]): JSX.Element => {
      return userSpans.reduce(
        (prev, curr, index) => (
          <>
            {prev}
            {index > 0 && <span key={`separator-${index}`}>, </span>}
            {curr}
          </>
        ),
        <></>
      );
    };

    return <span>{users.length > 0 ? combineUserSpans(renderUserSpans(users)) : '未设置'}</span>;
  };

  // 处理编辑按钮点击
  const handleEditTeachersWithLog = (
    level: string,
    id: string,
    users: TmbUser[] = [],
    gradeName?: string,
    className?: string,
    departmentType?: 'primary' | 'junior',
    nodeDeptId?: string
  ) => {
    // 设置弹窗数据
    let title = '';
    let deptName = '';

    switch (level) {
      case 'school':
        title = '教学管理员设置';
        break;
      case 'department':
        title = '管理教师设置';
        deptName = gradeName || '';
        setModalDepartmentType(departmentType);
        break;
      case 'grade':
        title = '班主任设置';
        deptName = gradeName || '';
        break;
      case 'class':
        title = '班主任设置';
        deptName = gradeName && className ? `${gradeName}${className}` : '';
        break;
    }

    // 设置状态并打开弹窗
    setModalSettingId(id);
    setModalTitle(title);
    setModalDeptName(deptName);
    setModalTmbIds(users.map((user) => user.tmbId));
    setModalLevel(level);
    setModalDeptId(nodeDeptId || id);
    onOpenSettingModal();

    // 如果父组件需要处理这些数据
    if (onEditTeachers) {
      onEditTeachers(
        level,
        id,
        gradeName,
        className,
        users.map((user) => user.tmbId)
      );
    }
  };

  // 渲染编辑图标
  const renderEditIcon = (
    onClick: () => void,
    itemId: string,
    users: TmbUser[] = [],
    right: string | number = 8
  ) => (
    <SvgIcon
      name="editIcon"
      w="16px"
      h="16px"
      cursor="pointer"
      onClick={onClick}
      style={{
        display: hoveredItem === itemId ? 'inline-block' : 'none',
        position: 'absolute',
        right: typeof right === 'number' ? `${right}px` : right,
        top: '50%',
        transform: 'translateY(-50%)'
      }}
    />
  );

  if (treeData.length === 0) {
    return renderEmptyState(semesterData, '年班级');
  }

  // 确保只有一个根节点
  const rootNode = treeData[0];
  if (!rootNode) {
    return renderEmptyState(semesterData, '年班级');
  }

  return (
    <Box>
      <Box
        style={{
          backgroundColor: '#f8f9fa',
          borderRadius: '0px',
          boxShadow: 'none',
          padding: '0px',
          height: '100%'
        }}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          h="60px"
          padding="0 16px"
          border="1px solid #e8e8e8"
          borderBottom="0"
        >
          <span
            style={{ fontSize: '14px', color: '#303133', position: 'relative' }}
            onMouseEnter={() => setHoveredItem('school')}
            onMouseLeave={() => setHoveredItem(null)}
          >
            教学管理员: {renderTmbUsers(rootNode.tmbUserList || [])}
            {renderEditIcon(
              () =>
                handleEditTeachersWithLog(
                  'school',
                  rootNode.id || '',
                  rootNode.tmbUserList || [],
                  rootNode.deptName,
                  undefined,
                  undefined,
                  rootNode.deptId
                ),
              'school',
              rootNode.tmbUserList || [],
              -16
            )}
          </span>
        </Box>

        {/* 部门列表 */}
        {rootNode.children?.map((department, deptIndex) => (
          <Card
            key={`department-${department.deptId || deptIndex}`}
            bodyStyle={{ padding: 0 }}
            style={{
              backgroundColor: '#fff',
              borderRadius: '0px',
              boxShadow: 'none',
              padding: '0px',
              fontSize: '14px',
              color: '#303133'
            }}
            title={
              <Box
                style={{ position: 'relative' }}
                display="inline-block"
                fontSize="14px"
                color="#303133"
                fontWeight="400"
                position="relative"
                onMouseEnter={() => setHoveredItem(`department-${department.deptId}`)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {department.deptName} | 管理教师: {renderTmbUsers(department.tmbUserList || [])}
                {renderEditIcon(
                  () =>
                    handleEditTeachersWithLog(
                      'department',
                      department.deptId,
                      department.tmbUserList || [],
                      department.deptName,
                      undefined,
                      department.deptName.includes('小学') ? 'primary' : 'junior',
                      department.deptId
                    ),
                  `department-${department.deptId}`,
                  department.tmbUserList || [],
                  -16
                )}
              </Box>
            }
          >
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {department.children?.map((grade, gradeIndex) => {
                const maxClassCount = department.deptName.includes('小学')
                  ? maxClassCounts.primary
                  : maxClassCounts.middle;

                return (
                  <div
                    key={`grade-container-${grade.id || gradeIndex}`}
                    style={{ width: '16.66%', padding: '0px' }}
                  >
                    <table
                      style={{ width: '100%', borderCollapse: 'collapse' }}
                      className={styles['table']}
                    >
                      <tbody>
                        <tr key={`grade-header-${grade.id || gradeIndex}`}>
                          <td
                            style={{
                              border: '1px solid #e8e8e8',
                              fontSize: '12px',
                              color: '#606266',
                              backgroundColor: '#F9FAFB',
                              height: '46px',
                              width: '92px',
                              padding: '0 8px',
                              textAlign: 'center'
                            }}
                          >
                            {grade.deptName}
                          </td>
                          <td
                            style={{
                              border: '1px solid #e8e8e8',
                              height: '46px',
                              padding: '0 8px',
                              fontSize: '12px',
                              color: '#606266',
                              backgroundColor:
                                hoveredItem === `grade-${grade.deptId}` ? 'primary.50' : '#fff',
                              transition: 'background-color 0.3s',
                              position: 'relative',
                              textAlign: 'center',
                              cursor: 'pointer'
                            }}
                            onMouseEnter={() => setHoveredItem(`grade-${grade.deptId}`)}
                            onMouseLeave={() => setHoveredItem(null)}
                          >
                            年级主任: {renderTmbUsers(grade.tmbUserList || [])}
                            {renderEditIcon(
                              () =>
                                handleEditTeachersWithLog(
                                  'grade',
                                  grade.id,
                                  grade.tmbUserList || [],
                                  grade.deptName,
                                  undefined,
                                  undefined,
                                  grade.deptId
                                ),
                              `grade-${grade.deptId}`,
                              grade.tmbUserList || [],
                              8
                            )}
                          </td>
                        </tr>
                        {/* 班级列表 */}
                        {[...Array(maxClassCount)].map((_, index) => {
                          const classItem =
                            grade.children && index < grade.children.length
                              ? grade.children[index]
                              : null;

                          const rowKey = `${grade.id || 'grade'}-row-${index}-${Math.random()
                            .toString(36)
                            .substring(2, 7)}`;

                          return (
                            <tr key={rowKey}>
                              <td
                                style={{
                                  border: '1px solid #e8e8e8',
                                  borderBottom: '0px',
                                  height: '46px',
                                  padding: '0 8px',
                                  fontSize: '12px',
                                  color: '#606266',
                                  backgroundColor: '#F9FAFB',
                                  width: '92px',
                                  textAlign: 'center'
                                }}
                              >
                                {classItem ? classItem.deptName : ''}
                              </td>
                              <td
                                style={{
                                  border: '1px solid #e8e8e8',
                                  borderBottom: '0px',
                                  height: '46px',
                                  padding: '0 8px',
                                  fontSize: '12px',
                                  color: '#606266',
                                  backgroundColor: classItem
                                    ? hoveredItem === `class-${classItem.deptId}`
                                      ? 'primary.50'
                                      : '#fff'
                                    : '#fff',
                                  transition: 'background-color 0.3s',
                                  position: 'relative',
                                  userSelect: classItem ? 'auto' : 'none',
                                  pointerEvents: classItem ? 'auto' : 'none',
                                  textAlign: 'center',
                                  cursor: classItem ? 'pointer' : 'default'
                                }}
                                onMouseEnter={() =>
                                  classItem && setHoveredItem(`class-${classItem.deptId}`)
                                }
                                onMouseLeave={() => classItem && setHoveredItem(null)}
                              >
                                {classItem ? (
                                  <>
                                    班主任: {renderTmbUsers(classItem.tmbUserList || [])}
                                    {renderEditIcon(
                                      () =>
                                        handleEditTeachersWithLog(
                                          'class',
                                          classItem.id,
                                          classItem.tmbUserList || [],
                                          grade.deptName,
                                          classItem.deptName,
                                          undefined,
                                          classItem.deptId
                                        ),
                                      `class-${classItem.deptId}`,
                                      classItem.tmbUserList || [],
                                      8
                                    )}
                                  </>
                                ) : (
                                  ''
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                );
              })}
            </div>
          </Card>
        ))}
      </Box>

      {/* 设置弹窗 */}
      {isOpenSettingModal && (
        <SettingModal
          settingId={modalSettingId}
          title={modalTitle}
          deptName={modalDeptName}
          onClose={handleModalClose}
          selectedTmbIds={modalTmbIds}
          onSuccess={() => {}}
        />
      )}
    </Box>
  );
};

export default AdministrationManagement;
