华云 AI 用户端是一个基于 Next.js 的现代化 Web 应用，采用 TypeScript 开发，使用 Chakra UI 加 antd 作为 UI 组件库。

### 核心技术

- **框架**：Next.js 13+ (React 18+)
- **语言**：TypeScript
- **UI 库**：Chakra UI antd
- **状态管理**：Zustand
- **样式**：SCSS + Chakra UI +Antd
- **包管理**：pnpm
- **代码规范**：ESLint + Prettier + Husky

### 开发工具

- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.6.0
- **IDE**：VSCode (推荐)
- **Git**：版本控制

### 目录结构

```
huayunai-user/
├── packages/                 # 共享包
│   ├── global/              # 全局类型定义
│   └── web/                 # Web共享组件
├── projects/                # 项目应用
│   └── app/                 # 主应用
│       ├── src/
│       │   ├── api/         # API接口
│       │   ├── components/  # 组件库
│       │   ├── constants/   # 常量定义
│       │   ├── hooks/       # 自定义Hooks
│       │   ├── pages/       # 页面组件
│       │   ├── store/       # 状态管理
│       │   ├── styles/      # 样式文件
│       │   ├── types/       # 类型定义
│       │   └── utils/       # 工具函数
│       ├── public/          # 静态资源
│       └── package.json
├── scripts/                 # 构建脚本
├── worker/                  # Worker服务
└── docs/                    # 文档目录
```

### 核心模块说明

#### 1。API 层 (`src/api/`)

- 所有后端接口调用统一管理
- 使用 axios 进行 HTTP 请求
- 支持请求拦截和错误处理
- 接口类型定义在 `src/types/api/`

#### 2。状态管理 (`src/store/`)

- 使用 Zustand 进行状态管理
- 按功能模块划分 store
- 支持持久化存储

#### 3。路由系统 (`src/pages/`)

- 基于 Next.js 文件系统路由
- 支持动态路由和 API 路由

### 代码风格

- 使用 TypeScript 严格模式
- 遵循 ESLint 规则配置
- 使用 Prettier 进行代码格式化
- 组件命名使用 PascalCase
- 文件命名使用 camelCase 或 kebab-case

### 组件开发规范

**样式规范**：

- 响应式设计使用 `vwDims` 工具函数

#### 4。个人中心组件 (`src/pages/personalCenter/components/`)

- **QuestionOptions**：选择题组件，支持多选答案、动态选项管理
- **FillInBlankOptions**：填空题组件，支持智能填空插入、答案列表管理
- **MyEdit**：增强版富文本编辑器，支持音频视频插入、光标位置文本插入
- **AddQuestionModal**：添加题目模态框，集成选择题、填空题、解答题
- **ShortAnswerOptions**：解答题组件，支持小题管理、富文本编辑
- **UserInfo**：用户信息组件，显示真实头像、姓名、手机号

#### 5。个人中心 API (`src/api/personalCenter.ts`)

- **getKnowledgePointTree**：获取知识点树结构接口
- **getMyQuestionList**：获取我的题目列表
- **addQuestion**：添加题目
- **editQuestion**：编辑题目
- **deleteQuestion**：删除题目

## 部署流程

### 本地开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
cd projects/app
pnpm dev
```

### 登录帐号密码

帐号：13901230001
密码：Xx@123456

## 重要注意事项

1. **代码提交**：使用 Husky 进行 pre-commit 检查，确保代码质量
2. **类型安全**：严格使用 TypeScript，避免 any 类型
3. **组件复用**：优先使用项目内的全局组件
4. **API 调用**：统一使用项目的 API 层，不要直接调用 fetch

## 项目部署地址

[项目部署地址](https://kubesphere.hwzxs.com/learnking-pre/clusters/learnking-pre/devops/anxun-aijnprv/pipelines/anxun-ai-fastgpt-user-web/activity)
