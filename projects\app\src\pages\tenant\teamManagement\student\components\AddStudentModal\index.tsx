import React, { useState, useEffect, useRef } from 'react';
import { 
  FormControl, 
  FormLabel, 
  Flex, 
  Button, 
  ModalBody, 
  ModalFooter, 
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Modal,
  ModalOverlay,
  ModalContent,
  useDisclosure
} from '@chakra-ui/react';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 通用组件导入
import MyModal from '@/components/MyModal';
import MyBox from '@/components/common/MyBox';
import UploadImage from '@/components/UploadImage';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';
import MyTextarea from '@/components/common/Textarea/MyTextarea';
import RegionSelector from '../CascaderAddress/index';
import SchoolStructureSelect from '../SchoolStructureSelect/SchoolStructureSelect';

// API和类型导入
import { Toast } from '@/utils/ui/toast';
import { getSchoolDeptTree, createStudent, updateStudent, getStudentDetail, resetStudentPassword } from '@/api/student';
import { CityItem } from '@/types/api/tenant/teamManagement/student';
import type { StudentDetailResponse } from '@/types/student';
import { Divider } from '@chakra-ui/react';

// 样式导入
import styles from '../../student.module.scss';

dayjs.locale('zh-cn');

interface FormData {
  name: string;
  avatarUrl: string;
  avatar: string;
  code: string;
  sex: number;
  birthday: Date | null;
  enrollmentDate: Date | null;
  experienceIntroduction: string;
  address: string;
  schoolStructure: null;
  stageId: string;
  gradeId: string;
  clazzId: string;
}

interface AddStudentModalProps {
  studentId: string;
  mode: string;
  onClose: (submited: boolean, studentId?: string) => void;
  onSuccess: () => void;
}

const AddStudentModal: React.FC<AddStudentModalProps> = ({
  studentId,
  mode,
  onClose,
  onSuccess
}) => {
  const [schoolStructure, setSchoolStructure] = useState<{ title: string; value: string }[]>([]);
  const [city, setCity] = useState({});
  const [address, setAddress] = useState('');
  const [resetPasswordSuccess, setResetPasswordSuccess] = useState<{ studentName: string; newPassword: string } | null>(null);
  const dataFetchedRef = useRef(false);
  
  // 重置密码确认对话框
  const {
    isOpen: isResetPasswordOpen,
    onOpen: onResetPasswordOpen,
    onClose: onResetPasswordClose
  } = useDisclosure();
  const resetPasswordCancelRef = useRef<HTMLButtonElement>(null);

  const {
    register,
    control,
    getValues,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    trigger
  } = useForm<FormData>({
    mode: 'onBlur',
    shouldFocusError: false,
    defaultValues: {
      name: '',
      code: '',
      sex: 1,
      avatarUrl: '',
      avatar: '',
      birthday: null,
      enrollmentDate: null,
      experienceIntroduction: '',
      address: '',
      schoolStructure: null,
      stageId: '',
      gradeId: '',
      clazzId: ''
    }
  });

  // 初始化数据
  useEffect(() => {
    if (dataFetchedRef.current) return;
    dataFetchedRef.current = true;

    const convertToTreeSelectData = (data: any[]): any[] => {
      if (!Array.isArray(data)) return [];
      return data.map((item) => {
        // 格式化班级名称，将七年级改为初一
        const formattedTitle = (item.deptName || item.name || '').replace(/七年级/g, '初一');
        return {
          value: item.id,
          title: formattedTitle,
          children:
            Array.isArray(item.children) && item.children.length > 0
              ? convertToTreeSelectData(item.children)
              : undefined,
          isLeaf: !item.children || item.children.length === 0
        };
      });
    };

    // 获取学校部门树
    getSchoolDeptTree().then((res) => {
      const convertedData = convertToTreeSelectData(res || []);
      setSchoolStructure(convertedData);
    });
  }, []);

  // 获取学生详情
  useEffect(() => {
    if (!studentId) return;

    console.log('开始获取学生详情，studentId:', studentId);

    getStudentDetail({ id: studentId })
      .then((res: StudentDetailResponse) => {
        console.log('获取到的学生详情:', res);

        const formData = {
          name: res.name || '',
          avatarUrl: res.avatarUrl || '',
          avatar: res.avatarUrl || '',
          code: res.code || '',
          sex: res.sex || 1,
          birthday: res.birthday ? dayjs(res.birthday).toDate() : null,
          enrollmentDate: res.enrollmentDate ? dayjs(res.enrollmentDate).toDate() : null,
          experienceIntroduction: res.experienceIntroduction || '',
          address: res.address || '',
          schoolStructure:
            res.stageId && res.gradeId && res.clazzId
              ? ({
                  stageId: res.stageId,
                  gradeId: res.gradeId,
                  clazzId: res.clazzId
                } as any)
              : null,
          stageId: res.stageId || '',
          gradeId: res.gradeId || '',
          clazzId: res.clazzId || ''
        };

        console.log('准备重置表单数据:', formData);
        reset(formData);

        setAddress(res.address || '');
        setCity({
          provinceCode: res.provinceCode,
          provinceName: res.provinceName,
          cityCode: res.cityCode,
          cityName: res.cityName,
          districtCode: res.districtCode,
          districtName: res.districtName
        });
        handleRegionChange({
          provinceCode: res.provinceCode,
          provinceName: res.provinceName,
          cityCode: res.cityCode,
          cityName: res.cityName,
          districtCode: res.districtCode,
          districtName: res.districtName
        });

        console.log('数据加载完成');
      })
      .catch((error) => {
        console.error('获取学生详情失败:', error);
      });
  }, [studentId, reset]);

  const handleRegionChange = (value: CityItem) => {
    setCity(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return null;
    return dayjs(dateString).format('YYYY-MM-DD');
  };

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
  };

  const handleAddressChange = (val: string) => {
    setAddress(val);
    setValue('address', val);
  };

  const { mutate, isLoading: isSubmitting } = useRequest({
    mutationFn: (data: FormData) => {
      let schoolStructure = { stageId: '', gradeId: '', clazzId: '' };
      if (data.schoolStructure) {
        if (Array.isArray(data.schoolStructure)) {
          schoolStructure = data.schoolStructure[0] || schoolStructure;
        } else if (typeof data.schoolStructure === 'object') {
          schoolStructure = data.schoolStructure;
        }
      }

      const baseParams = {
        name: data.name,
        avatarUrl: data.avatarUrl,
        code: data.code,
        sex: data.sex,
        birthday: data.birthday ? dayjs(data.birthday).format('YYYY-MM-DD') : undefined,
        experienceIntroduction: data.experienceIntroduction,
        ...city,
        ...schoolStructure,
        address
      };

      if (studentId) {
        // 更新学生 - 不包含enrollmentDate
        return updateStudent({
          id: studentId,
          ...baseParams,
          birthday: baseParams.birthday
        });
      } else {
        // 创建学生 - 包含enrollmentDate
        return createStudent({
          ...baseParams,
          birthday: baseParams.birthday,
          enrollmentDate: data.enrollmentDate
            ? dayjs(data.enrollmentDate).format('YYYY-MM-DD')
            : undefined
        });
      }
    },
    onSuccess(res) {
      Toast.success({ title: studentId ? '更新成功' : '新增成功' });
      onSuccess();
      onClose(true);
    },
    onError: (error) => {
      console.error('提交出错:', error);
    }
  });

  const onSubmit: SubmitHandler<FormData> = (data) => {
    console.log('提交数据:', data);
    console.log('当前表单值:', getValues());
    console.log('表单错误:', errors);

    // 确保表单数据完整
    if (!data.name?.trim() || !data.code?.trim()) {
      Toast.error('请填写必填字段');
      return;
    }

    mutate(data);
  };

  // 重置密码处理函数
  const handleResetPassword = async () => {
    if (!studentId) {
      Toast.error('学生ID不存在');
      return;
    }

    try {
      await resetStudentPassword({ id: studentId });
      // 显示成功弹窗，包含学生姓名和新密码
      const studentName = getValues('name') || '学生';
      const newPassword = 'Xx@123456'; // 默认密码
      
      // 关闭确认对话框
      onResetPasswordClose();
      
      // 显示成功弹窗
      setResetPasswordSuccess({ studentName, newPassword });
    } catch (error: any) {
      console.error('重置密码失败:', error);
      const errorMessage = error?.response?.data?.msg || error?.message || '重置密码失败';
      Toast.error(errorMessage);
    }
  };

  return (
    <MyModal
      isOpen={true}
      title={studentId ? '编辑学生' : '添加学生'}
      w="684px"
      maxH="748px"
      returnFocusOnClose={false}
      closeOnOverlayClick={false}
      onClose={() => onClose(false)}
    >
      <MyBox p={6} overflow="auto" w="100%" flex={1} maxH="calc(748px - 80px)" isLoading={isSubmitting}>
          {/* 头像上传 */}
          <Flex justifyContent="center">
            <UploadImage
              imageUrl={getValues('avatarUrl')}
              isCircular={true}
              onImageSelect={(fileKey, fileUrl) => handleImageSelect('avatar', fileKey, fileUrl)}
              maxWidthOrHeight={300}
              showPlaceholderAsBox={true}
              className={styles['custom-cascader']}
              {...register('avatarUrl')}
            />
          </Flex>
          <MyBox textAlign="center" color="#4E5969" fontWeight="12px" p="10px 0 20px 0">
            点击上传学生照片
          </MyBox>

          {/* 基本信息 */}
          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.name} w="328px" mr="32px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Flex alignItems="center" justifyContent="space-between">
                  <MyBox
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    学生姓名
                  </MyBox>
                  {errors.name && (
                    <MyBox color="#F53F3F" fontSize="13px">
                      {errors.name.message}
                    </MyBox>
                  )}
                </Flex>
              </FormLabel>
              <MyInput
                {...register('name', {
                  required: '请输入学生姓名',
                  maxLength: {
                    value: 20,
                    message: '学生姓名不能超过20个字符'
                  },
                  validate: (value) => {
                    // 在编辑模式下，如果数据正在加载，不显示错误
                    if (studentId && !value) {
                      return true;
                    }
                    if (!value || !value.trim()) {
                      return '请输入学生姓名';
                    }
                    return true;
                  }
                })}
                placeholder="请输入学生姓名"
                isDisabled={mode === 'view'}
                maxLength={20}
                showCharCount={true}
                currentLength={watch('name')?.length || 0}
                style={{
                  height: '38px',
                  borderRadius: '8px',
                  backgroundColor: '#F6F6F6',
                  border: 'none'
                }}
                sx={{
                  '::placeholder': {
                    color: '#959699',
                    fontSize: '14px'
                  }
                }}
              />
            </FormControl>
            <FormControl isInvalid={!!errors.code} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Flex alignItems="center" justifyContent="space-between">
                  <MyBox
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    学号
                  </MyBox>
                  {errors.code && (
                    <MyBox color="#F53F3F" fontSize="13px">
                      {errors.code.message}
                    </MyBox>
                  )}
                </Flex>
              </FormLabel>
              <MyInput
                {...register('code', {
                  required: '请输入学号',
                  maxLength: {
                    value: 20,
                    message: '学号不能超过20个字符'
                  },
                  validate: (value) => {
                    // 在编辑模式下，如果数据正在加载，不显示错误
                    if (studentId && !value) {
                      return true;
                    }
                    if (!value || !value.trim()) {
                      return '请输入学号';
                    }
                    return true;
                  }
                })}
                placeholder="请输入学号"
                isDisabled={mode === 'view'}
                maxLength={20}
                showCharCount={true}
                currentLength={watch('code')?.length || 0}
                style={{
                  width: '100%',
                  height: '38px',
                  borderRadius: '8px',
                  backgroundColor: '#F6F6F6',
                  border: 'none'
                }}
                sx={{
                  '::placeholder': {
                    color: '#959699',
                    fontSize: '14px'
                  }
                }}
              />
            </FormControl>
          </Flex>

          {/* 性别和出生日期 */}
          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.sex} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Flex alignItems="center" justifyContent="space-between">
                  <MyBox
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    性别
                  </MyBox>
                  {errors.sex && (
                    <MyBox color="#F53F3F" fontSize="13px">
                      {errors.sex.message}
                    </MyBox>
                  )}
                </Flex>
              </FormLabel>
              <Controller
                name="sex"
                control={control}
                rules={{ required: '请选择性别' }}
                render={({ field }) => (
                  <MySelect
                    value={field.value}
                    onchange={(value) => field.onChange(value)}
                    placeholder="请选择性别"
                    list={[
                      { label: '男', value: 1 },
                      { label: '女', value: 2 }
                    ]}
                    className={styles['custom-cascader']}
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                  />
                )}
              />
            </FormControl>
            <FormControl isInvalid={!!errors.birthday} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Flex alignItems="center" justifyContent="space-between">
                  <MyBox
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    出生日期
                  </MyBox>
                  {errors.birthday && (
                    <MyBox color="#F53F3F" fontSize="13px">
                      {errors.birthday.message}
                    </MyBox>
                  )}
                </Flex>
              </FormLabel>
              <Controller
                name="birthday"
                control={control}
                rules={{ required: '请选择出生日期' }}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    popupClassName={`datePicker ${styles.datePicker} hidden-ascader-level1-check`}
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date ? formatDate(date.format('YYYY-MM-DD')) : '')
                    }
                    format="YYYY-MM-DD"
                    placeholder="请选择出生日期"
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      zIndex: 999,
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                    className={`customPlaceholder ${styles.customPlaceholder}`}
                    disabled={mode === 'view'}
                  />
                )}
              />
            </FormControl>
          </Flex>

          {/* 班级和入学时间 */}
          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.schoolStructure} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Flex alignItems="center" justifyContent="space-between">
                  <MyBox
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    班级
                  </MyBox>
                  {errors.schoolStructure && (
                    <MyBox color="#F53F3F" fontSize="13px">
                      {errors.schoolStructure.message}
                    </MyBox>
                  )}
                </Flex>
              </FormLabel>
              <Controller
                name="schoolStructure"
                control={control}
                rules={{ required: '请选择班级' }}
                render={({ field }) => (
                  <SchoolStructureSelect
                    control={control}
                    name="schoolStructure"
                    treeData={schoolStructure}
                    placeholder="请选择学段/年级/班级"
                    mode={mode}
                  />
                )}
              />
            </FormControl>
            <FormControl w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                入学时间
              </FormLabel>
              <Controller
                name="enrollmentDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    className={`customPlaceholder ${styles.customPlaceholder}`}
                    popupClassName={`datePicker ${styles.datePicker} hidden-ascader-level1-check`}
                    {...field}
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date ? formatDate(date.format('YYYY-MM-DD')) : null)
                    }
                    format="YYYY-MM-DD"
                    placeholder="请选择入学时间"
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                    disabled={mode === 'view'}
                  />
                )}
              />
            </FormControl>
          </Flex>

        {/* 地址信息 */}
        <FormControl mb="14px" position="relative" zIndex={9998}>
          <FormLabel color="#4E5969" fontSize="14px">
            家庭地址
          </FormLabel>
          <MyBox>
            <RegionSelector onChange={handleRegionChange} initialValue={city} mode={mode} />
            <MyInput
              mt="14px"
              placeholder="详细地址"
              {...register('address', { required: false })}
              value={address}
              onChange={(e) => handleAddressChange(e.target.value)}
              isDisabled={mode === 'view'}
              style={{
                height: '38px',
                borderRadius: '8px',
                borderColor: '#d9d9d9',
                backgroundColor: '#F6F6F6',
                border: 'none'
              }}
              sx={{
                '::placeholder': {
                  color: '#959699',
                  fontSize: '14px'
                }
              }}
            />
          </MyBox>
        </FormControl>

        {/* 实习经历 */}
        <FormControl mb="14px">
          <FormLabel color="#4E5969" fontSize="14px">
            实习经历
          </FormLabel>
          <MyTextarea
            {...register('experienceIntroduction')}
            placeholder="请输入实习经历"
            isDisabled={mode === 'view'}
            style={{
              height: '84px',
              borderRadius: '8px',
              backgroundColor: '#F6F6F6',
              border: 'none'
            }}
            sx={{
              '::placeholder': {
                color: '#959699',
                fontSize: '14px'
              }
            }}
          />
        </FormControl>

        {/* 底部按钮 */}
        <Divider my={6} />
        <Flex justify="space-between" gap={4}>
          <Flex>
            {/* 重置密码按钮 - 只在编辑模式且有学生ID时显示 */}
            {mode === 'edit' && studentId && (
              <Button
                variant="outline"
                colorScheme="red"
                onClick={onResetPasswordOpen}
                size="sm"
              >
                重置密码
              </Button>
            )}
          </Flex>
          
          <Flex gap={4}>
            <Button variant="outline" onClick={() => onClose(false)}>
              取消
            </Button>
            <Button colorScheme="purple" isLoading={isSubmitting} onClick={handleSubmit(onSubmit)}>
              {studentId ? '保存' : '添加'}
            </Button>
          </Flex>
        </Flex>
      </MyBox>

      {/* 重置密码确认对话框 */}
      <AlertDialog
        isOpen={isResetPasswordOpen}
        leastDestructiveRef={resetPasswordCancelRef}
        onClose={onResetPasswordClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              重置密码
            </AlertDialogHeader>
            <AlertDialogBody>
              确定要重置该学生的密码吗？重置后学生需要使用新密码登录。
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={resetPasswordCancelRef} onClick={onResetPasswordClose}>
                取消
              </Button>
              <Button colorScheme="red" onClick={handleResetPassword} ml={3}>
                确定重置
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* 重置密码成功弹窗 */}
      <Modal isOpen={!!resetPasswordSuccess} onClose={() => setResetPasswordSuccess(null)}>
        <ModalOverlay />
        <ModalContent maxW="400px">
          <ModalBody p={6} position="relative">
            <Button
              position="absolute"
              top={4}
              right={4}
              variant="ghost"
              size="sm"
              onClick={() => setResetPasswordSuccess(null)}
            >
              ×
            </Button>
            <MyBox textAlign="center">
              <MyBox fontSize="lg" fontWeight="bold" mb={4} color="#333">
                重置密码成功
              </MyBox>
              <MyBox mb={6} color="#333" lineHeight="1.5">
                学生[{resetPasswordSuccess?.studentName}]密码已被重置为 {resetPasswordSuccess?.newPassword}
              </MyBox>
              <Flex justify="flex-end">
                <Button colorScheme="blue" onClick={() => setResetPasswordSuccess(null)}>
                  好的
                </Button>
              </Flex>
            </MyBox>
          </ModalBody>
        </ModalContent>
      </Modal>
    </MyModal>
  );
};

export default AddStudentModal;
