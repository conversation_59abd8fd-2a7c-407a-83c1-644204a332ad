import { useEffect, useState } from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { ChakraProvider, ColorModeScript, Flex, Text } from '@chakra-ui/react';
import Layout from '@/components/Layout';
import { theme } from '@/styles/theme';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import NProgress from 'nprogress'; //nprogress module
import Router from 'next/router';
import { initSystemData } from '@/utils/system';
import { appWithTranslation, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { change2DefaultLng, setLngStore } from '@/utils/i18n';

import 'nprogress/nprogress.css';
import '@/styles/reset.scss';
import { useUserStore } from '@/store/useUserStore';
import { getToken, setToken } from '@/utils/auth';

import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { useTenantStore } from '@/store/useTenantStore';
import PortalProvider from '@/components/PortalProvider';
import CloudProvider from '@/components/CloudProvider';
import RoutesProvider from '@/components/RoutesProvider';
import { LayoutProvider } from '@/components/LayoutProvider';
import { getErrorMessageList } from '@/api/errorMessage';
import Script from 'next/script';
import '../../public/static/ueditor/themes/default/css/ueditor.css';
import { ClickToComponent } from 'click-to-react-component';
import { getCasLogin, getOauth2Token } from '@/api/auth';
//Binding events.
Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', () => NProgress.done());
Router.events.on('routeChangeError', () => NProgress.done());

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      keepPreviousData: true,
      refetchOnWindowFocus: false,
      retry: false,
      cacheTime: 10
    }
  }
});

function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const { token, pathname } = router.query as { hiId?: string; token?: string; pathname?: string };
  const { i18n } = useTranslation();
  const [title, setTitle] = useState('AI平台');
  const { userInfo, setUserInfo } = useUserStore();
  const { tenant, loadTenant } = useTenantStore();

  useEffect(() => {
    // 如果url包含szedu-class.hwzxs.com/humans/mocc，则显示调试按钮
    if (location.href.includes('szedu-class.hwzxs.com/humans/mocc')) {
      const loadEruda = async () => {
        const eruda = (await import('eruda')).default;
        eruda.init();
      };
      loadEruda();
    }
  }, []);

  useEffect(() => {
    // add window error track
    window.onerror = function (msg, url) {
      window.umami?.track('windowError', {
        device: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          appName: navigator.appName
        },
        msg,
        url
      });
    };

    return () => {
      window.onerror = null;
    };
  }, []);

  useEffect(() => {
    // get default language
    const targetLng = change2DefaultLng(i18n.language);
    if (targetLng) {
      setLngStore(targetLng);
      router.replace(router.asPath, undefined, { locale: targetLng });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTitle(tenant?.name ? `${tenant.name}AI平台` : 'AI平台');
  }, [tenant?.name]);

  useEffect(() => {
    if (token) {
      setUserInfo(null);
      // 如果当前路径是 temp_chat 页面，不获取错误信息列表
      const isGetErrorMessageList = !router.pathname.includes('temp_chat');
      setToken(token, isGetErrorMessageList);
      const query = { ...router.query };
      delete query.token;
      delete query.pathname;

      if (pathname) {
        router.replace({
          pathname,
          query
        });
      } else {
        router.replace({
          pathname: router.pathname,
          query
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, pathname, setUserInfo]);

  useEffect(() => {
    if (router.query.grant_code || router.query.code) {
      getOauth2Token({ code: (router.query.grant_code as string) || (router.query.code as string) })
        .then((res) => {
          if (res.accessToken) {
            router.replace({
              pathname: '/',
              query: {
                token: res.accessToken
              }
            });
          }
        })
        .catch((err) => {
          router.replace({
            pathname: '/login'
          });
        });
    } else if (router.query.ticket) {
      getCasLogin(router.query.ticket as string)
        .then((res) => {
          if (res.accessToken) {
            router.replace({
              pathname: '/',
              query: { token: res.accessToken }
            });
          }
        })
        .catch((err) => {
          router.replace({
            pathname: '/login'
          });
        });
    }
  }, [router.query.grant_code, router.query.code, router.query.ticket]);

  useEffect(() => {
    if (router.pathname !== '/temp_chat') {
      loadTenant();
    }
  }, [loadTenant]);

  useEffect(() => {
    userInfo?.userId && initSystemData();
  }, [userInfo?.userId]);

  if (router.query.code || router.query.ticket || !router.isReady || router.query.grant_code) {
    return (
      <Flex direction="column" justify="center" align="center" h="100vh" flex={1}>
        <Text color="gray.600" fontSize="md">
          登录中...
        </Text>
      </Flex>
    );
  }

  if (router.pathname === '/temp_chat') {
    return (
      <>
        <ClickToComponent editor="kiro" />
        <Head>
          <title>{title}</title>
          <meta
            name="description"
            content={`${tenant?.name || ''}专属AI平台，让每所学校成为AI时代的领航者，每位教师成为AI时代的先行者，每个学生成为AI时代的受益者。`}
          />
          <meta
            name="viewport"
            content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no, viewport-fit=cover"
          />
          {tenant?.avatarUrl && <link rel="icon" href={tenant?.avatarUrl} />}
        </Head>
        <LayoutProvider>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#7D4DFF'
              }
            }}
          >
            <ColorModeScript initialColorMode={theme.config.initialColorMode} />
            <ChakraProvider theme={theme}>
              <PortalProvider>
                <QueryClientProvider client={queryClient}>
                  <Component {...pageProps} />
                </QueryClientProvider>
              </PortalProvider>
            </ChakraProvider>
          </ConfigProvider>
        </LayoutProvider>
      </>
    );
  }

  return (
    <>
      <ClickToComponent editor="vscode" />
      <Head>
        <title>{title}</title>
        <meta
          name="description"
          content={`${tenant?.name || ''}专属AI平台，让每所学校成为AI时代的领航者，每位教师成为AI时代的先行者，每个学生成为AI时代的受益者。`}
        />
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no, viewport-fit=cover"
        />
        {tenant?.avatarUrl && <link rel="icon" href={tenant?.avatarUrl} />}

        <script
          dangerouslySetInnerHTML={{
            __html: `
            console.log('Clarity Analytics Script Loaded');
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "ofcg6mom4x");
          `
          }}
        />
      </Head>

      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <ColorModeScript initialColorMode={theme.config.initialColorMode} />
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#7D4DFF'
              }
            }}
          >
            <LayoutProvider>
              <RoutesProvider>
                <CloudProvider>
                  <PortalProvider>
                    <Layout>
                      <Component {...pageProps} />
                    </Layout>
                  </PortalProvider>
                </CloudProvider>
              </RoutesProvider>
            </LayoutProvider>
          </ConfigProvider>
        </ChakraProvider>
      </QueryClientProvider>
    </>
  );
}

// @ts-ignore
export default appWithTranslation(App);
