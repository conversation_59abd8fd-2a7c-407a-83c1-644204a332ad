// 学科配置相关类型定义

// 学科列表请求参数
export interface SubjectListParams {
  semesterId: number;
}

// 学科详情请求参数
export interface SubjectDetailParams {
  semesterId: number;
  gradeId: number;
}

// 学科拖动排序请求参数
export interface SubjectSortParams {
  id: number; // 被选中的id
  upperId: number; // 目标位置上方元素id（若在顶部则id = 0）
}

// 编辑学科请求参数
export interface SubjectUpdateParams {
  semesterId: number;
  gradeId: number;
  subjectIds: number[];
}

// 重新排序请求参数
export interface SubjectReSortParams {
  semesterId: number;
  gradeId: number;
}

// 学科列表响应数据项结构（根据实际API返回结构）
export interface SubjectListItem {
  id: number | null;
  createTime: string;
  updateTime: string;
  isDeleted: number | null;
  tenantId: string;
  semesterId: string;
  stageType: string; // 学段类型
  gradeId: string;
  deptValue: string;
  deptName: string; // 年级名称
  subjectId: number | null;
  sort: number | null;
  parentId: number | null;
  subjectName: string;
  subjectIds: number[]; // 学科ID数组
  subjectNames: string[]; // 学科名称数组
}

// 学科列表响应数据（数组格式）
export type SubjectListResponse = SubjectListItem[];

// 学科详情响应数据项结构
export interface SubjectDetailItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  semesterId: string;
  stageType: string;
  gradeId: string;
  deptValue: string;
  deptName: string;
  subjectId: string;
  sort: number;
  parentId: string | null;
  subjectName: string;
  subjectIds: any[]; // 在detail接口中为空数组
  subjectNames: any[]; // 在detail接口中为空数组
}

// 学科详情响应数据（数组格式）
export type SubjectDetailResponse = SubjectDetailItem[];

// 新增：/client/subject/list 接口相关类型定义

// 学科基础列表请求参数
export interface SubjectBasicListParams {
  gradeId: number;
}

// 学科基础列表响应数据项结构
export interface SubjectBasicListItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  name: string;
  stageTypes: string; // 学段类型，如 "2,3,4"
  type: number;
}

// 学科基础列表响应数据（数组格式）
export type SubjectBasicListResponse = SubjectBasicListItem[];

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}
