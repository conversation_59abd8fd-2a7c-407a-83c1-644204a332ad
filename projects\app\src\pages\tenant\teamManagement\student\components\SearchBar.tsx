import {
  Button,
  Input,
  Flex,
  FormControl,
  FormLabel,
  Select as ChakraSelect,
  Tag,
  TagLabel,
  TagCloseButton,
  List,
  ListItem,
  useOutsideClick
} from '@chakra-ui/react';
import MyBox from '@/components/common/MyBox';
import { Select } from 'antd';
import { useMemo, useState, useEffect, useRef } from 'react';
import TagTextarea from '@/components/common/Textarea/TagTextarea';
import TagSelect from '@/components/common/TagSelect';
import { getSchoolDeptTree } from '@/api/student';
import { SearchBarProps } from '@/components/MyTable/types';
import React from 'react';
import type { StudentPageParams } from '@/types/student';
import { vwDims } from '@/utils/chakra';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';

// 假设 CLASS_OPTIONS 为后端返回的班级 id 和名称，实际应从接口获取
const CLASS_OPTIONS = [
  { label: '三年级一班', value: 1 },
  { label: '三年级三班', value: 2 }
];
const STATUS_OPTIONS = [
  { value: 1, label: '在读' },
  { value: 2, label: '失效' },
  { value: 3, label: '毕业' }
];
const GENDER_OPTIONS = [
  { value: undefined, label: '全部' },
  { value: 1, label: '男' },
  { value: 2, label: '女' }
];

interface StudentSearchBarProps extends SearchBarProps<StudentPageParams> {
  ButtonsComponent?: React.ComponentType | React.ReactNode;
}

// 自定义标签式多选下拉组件
interface TagSelectOption {
  label: string;
  value: string;
}



const SearchBar = ({ onSearch, ButtonsComponent }: StudentSearchBarProps) => {
  const [form, setForm] = useState<Partial<StudentPageParams>>({
    name: '',
    code: '',
    sex: undefined,
    clazzIds: [],
    status: undefined // 默认查全部
  });
  const [classOptions, setClassOptions] = useState<TagSelectOption[]>([]);

  useEffect(() => {
    getSchoolDeptTree().then((res) => {
      // 年级和班级都可选，年级只显示年级名，班级显示"年级-班级"，七年级显示为初一
      const mapGradeName = (name: string) => (name === '七年级' ? '初一' : name);
      const flatten = (nodes: any[], parentGrade = ''): TagSelectOption[] => {
        let result: TagSelectOption[] = [];
        nodes.forEach((node) => {
          const labelRaw = node.deptName || node.name || '';
          const label = mapGradeName(labelRaw);
          if (node.children && node.children.length > 0) {
            // 递归处理班级
            const nextGrade = label;
            result = result.concat(flatten(node.children, nextGrade));
          } else if (label) {
            // 只有叶子节点（班级）才可选
            result.push({
              label: parentGrade ? `${parentGrade} - ${label}` : label,
              value: String(node.id)
            });
          }
        });
        return result;
      };
      const options = flatten(res || []);
      console.log('班级选项数据:', options); // 调试信息
      setClassOptions(options);
    }).catch((error) => {
      console.error('获取班级数据失败:', error);
      setClassOptions([]);
    });
  }, []);

  const handleChange = (key: keyof StudentPageParams, value: any) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };

  const handleSearch = () => {
    const params: StudentPageParams = {
      current: 1,
      size: 10,
      ...form,
      clazzIds: form.clazzIds && form.clazzIds.length > 0 ? form.clazzIds : undefined,
      sex: form.sex === undefined ? undefined : form.sex,
      ...(form.status !== undefined ? { status: form.status } : {})
    };
    if (typeof onSearch === 'function') {
      onSearch(params);
    }
  };

  const handleReset = () => {
    setForm({
      name: '',
      code: '',
      sex: undefined,
      clazzIds: [],
      status: undefined // 重置查全部
    });
    if (typeof onSearch === 'function') {
      onSearch({ current: 1, size: 10 });
    }
  };

  return (
    <MyBox p={vwDims(24)} minH={vwDims(160)} position="relative" zIndex={100} top={vwDims(-48)} left={vwDims(0)}>
      {/* 第一行 */}
      <Flex align="center" gap={vwDims(32)} mb={vwDims(24)} flexWrap="wrap">
        <Flex align="center" w={vwDims(284)}>
          <FormLabel fontSize={vwDims(14)} mb={0} minW={vwDims(70)} mr={vwDims(8)} color="#1D2129" fontFamily="PingFang SC" fontWeight={500}>
            学生名称：
          </FormLabel>
          <MyInput
            size="sm"
            value={form.name}
            onChange={(e) => handleChange('name', e.target.value)}
            w={vwDims(206)}
            h={vwDims(40)}
            borderRadius={vwDims(8)}
            px={vwDims(12)}
            py={vwDims(4)}
            bg="#F2F3F5"
            border="none"
            placeholder="请输入"
          />
        </Flex>
        <Flex align="center" w={vwDims(260)}>
          <FormLabel fontSize={vwDims(14)} mb={0} minW={vwDims(70)} mr={vwDims(8)} color="#1D2129" fontFamily="PingFang SC" fontWeight={500}>
            学号：
          </FormLabel>
          <MyInput
            size="sm"
            value={form.code}
            onChange={(e) => handleChange('code', e.target.value)}
            w={vwDims(206)}
            h={vwDims(40)}
            borderRadius={vwDims(8)}
            px={vwDims(12)}
            py={vwDims(4)}
            bg="#F2F3F5"
            border="none"
            placeholder="请输入"
          />
        </Flex>

        <Flex align="center" w={vwDims(200)}>
          <FormLabel fontSize={vwDims(14)} mb={0} minW={vwDims(70)} mr={vwDims(8)} color="#1D2129" fontFamily="PingFang SC" fontWeight={500}>
            性别：
          </FormLabel>
          <MySelect
            size="sm"
            value={form.sex === undefined ? '' : String(form.sex)}
            onchange={(value) => handleChange('sex', value === '' ? undefined : Number(value))}
            placeholder="请选择性别"
            list={GENDER_OPTIONS.map((opt) => ({
              label: opt.label,
              value: opt.value === undefined ? '' : String(opt.value),
              alias: opt.label
            }))}
            w={vwDims(120)}
            h={vwDims(40)}
            bg="#F2F3F5"
            border="none"
            borderRadius={vwDims(8)}
            color="#4E5969"
          />
        </Flex>
        <Flex align="center" w={vwDims(280)} h={vwDims(40)}>
          <FormLabel fontSize={vwDims(14)} mb={0} minW={vwDims(70)} mr={vwDims(8)} color="#1D2129" fontFamily="PingFang SC" fontWeight={500}>
            班级：
          </FormLabel>
          <TagSelect
            options={classOptions}
            value={(form.clazzIds || []).map(String)}
            onChange={(vals) => handleChange('clazzIds', vals.map(Number))}
            placeholder="请选择班级"
            width="100%"
          />
        </Flex>
      </Flex>
      {/* 第二行 */}
      <Flex align="center" gap={vwDims(32)} mb={vwDims(24)}>
        <Flex align="center" w={vwDims(475)} h={vwDims(40)}>
          <FormLabel fontSize={vwDims(14)} minW={vwDims(70)} mr={vwDims(8)} textAlign="right" mb={1} color="#1D2129" fontFamily="PingFang SC" fontWeight={500}>
            状态：
          </FormLabel>
          <MySelect
            size="sm"
            value={form.status === undefined ? '' : String(form.status)}
            onchange={(value) => handleChange('status', value === '' ? undefined : Number(value))}
            placeholder="请选择状态"
            list={[
              { label: '全部', value: '', alias: '全部' },
              ...STATUS_OPTIONS.map((opt) => ({
                label: opt.label,
                value: String(opt.value),
                alias: opt.label
              }))
            ]}
            w={vwDims(475)}
            h={vwDims(32)}
            bg="#F2F3F5"
            border="none"
            borderRadius={vwDims(8)}
            color="#4E5969"
          />
        </Flex>
      </Flex>
      
      {/* 按钮行 */}
      <Flex justify="flex-end" gap={vwDims(8)} mb={vwDims(16)}>
        <Button 
          size="sm" 
          h={vwDims(30)} 
          px={vwDims(16)} 
          bg="#7D4DFF"
          color="white"
          _hover={{ bg: "#6B3FD9" }}
          onClick={handleSearch}
          borderRadius={vwDims(4)}
        >
          搜索
        </Button>
        <Button 
          size="sm" 
          h={vwDims(30)} 
          px={vwDims(16)} 
          variant="outline"
          borderColor="#D9D9D9"
          color="#595959"
          _hover={{ borderColor: "#40A9FF", color: "#40A9FF" }}
          onClick={handleReset} 
          borderRadius={vwDims(4)}
        >
          重置
        </Button>
      </Flex>
    </MyBox>
  );
};

export default SearchBar;
