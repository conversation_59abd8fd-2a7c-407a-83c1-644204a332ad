import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Button, Flex, useDisclosure, Tooltip } from '@chakra-ui/react';
import { Table, Switch } from 'antd';
import {
  getClientSemesterPage,
  deleteSemester,
  setSemester,
  startSemester
} from '@/api/tenant/teamManagement/semester';
import { ClientSemesterPageType } from '@/types/api/tenant/teamManagement/semester';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SemesterModal from './components/SemesterModal';
import SemesterStartModal, { SemesterStartConfig } from './components/SemesterStartModal';
import { TableProps } from 'antd';
import PageContainer from '@/components/PageContainer';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { vwDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';

const Semester = () => {
  const { toast } = useToast();
  const [modalId, setModalId] = useState('');
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');
  const actionRef = useRef<MyTableRef<ClientSemesterPageType>>(null);
  const [tableData, setTableData] = useState<ClientSemesterPageType[]>([]);
  const [hasInProgressSemester, setHasInProgressSemester] = useState(false);
  const [isSameYear, setIsSameYear] = useState(true);
  const [startSemesterId, setStartSemesterId] = useState('');
  const [startLoading, setStartLoading] = useState(false);

  const tableRef = useRef<MyTableRef>(null);

  const SEMESTER_STATUS = {
    NOT_STARTED: 0 as 0, // 未开始
    IN_PROGRESS: 1 as 1, // 进行中
    ENDED: 2 as 2 // 已结束
  };

  const Status_type = {
    [SEMESTER_STATUS.NOT_STARTED]: '未开始',
    [SEMESTER_STATUS.IN_PROGRESS]: '进行中',
    [SEMESTER_STATUS.ENDED]: '已结束'
  };

  const {
    isOpen: isOpenSemesterModal,
    onOpen: onOpenSemesterModal,
    onClose: onCloseSemesterModal
  } = useDisclosure();

  const {
    isOpen: isOpenStartModal,
    onOpen: onOpenStartModal,
    onClose: onCloseStartModal
  } = useDisclosure();

  // 获取数据后检查是否有进行中的学期以及最近结束的学期
  useEffect(() => {
    const inProgressSemester = tableData.find(
      (item) => item.isCurrent === SEMESTER_STATUS.IN_PROGRESS
    );
    setHasInProgressSemester(!!inProgressSemester);

    // 如果有开始学期ID，检查是否与最近结束的学期同一学年
    if (startSemesterId) {
      // 获取最新的已结束学期
      const endedSemesters = tableData.filter((item) => item.isCurrent === SEMESTER_STATUS.ENDED);
      let latestEndedSemester = null;

      if (endedSemesters.length > 0) {
        // 按更新时间排序找最新的已结束学期
        latestEndedSemester = [...endedSemesters].sort(
          (a, b) => new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime()
        )[0];
      }

      const startSemester = tableData.find((item) => item.id === startSemesterId);

      if (startSemester && latestEndedSemester) {
        // 比较即将开始的学期是否与最近结束的学期为同一学年
        const isSame = startSemester.year === latestEndedSemester.year;
        setIsSameYear(isSame);
      } else {
        // 如果没有已结束的学期，默认设为true
        setIsSameYear(true);
      }
    }
  }, [tableData, startSemesterId]);

  const onAdd = () => {
    setModalId('');
    setModalMode('add');
    onOpenSemesterModal();
  };

  const onEdit = (id: string) => {
    setModalId(id);
    setModalMode('edit');
    onOpenSemesterModal();
  };

  const onView = (id: string) => {
    setModalId(id);
    setModalMode('view');
    onOpenSemesterModal();
  };

  const onRecover = (id: string) => {
    MessageBox.confirm({
      title: '恢复',
      content: '确认要将该学期恢复至进行中状态吗？',
      onOk: async () => {
        await setSemester({ id, isCurrent: SEMESTER_STATUS.IN_PROGRESS });
        Toast.success('恢复成功');
        tableRef.current?.reload();
      }
    });
  };

  const onEnd = (id: string) => {
    MessageBox.confirm({
      title: '结束',
      content: '确认要结束当前学期吗？结束后，学期状态将不可更改。',
      onOk: async () => {
        await setSemester({ id, isCurrent: SEMESTER_STATUS.ENDED });
        Toast.success('结束成功');
        tableRef.current?.reload();
      }
    });
  };

  const onDelete = (id: string) => {
    MessageBox.confirm({
      title: '删除',
      content: '请确认，删除后将不可恢复，该学期相关的历史数据（如成绩等）也将一并被清除。',
      onOk: async () => {
        await deleteSemester(id);
        Toast.success('删除成功');
        tableRef.current?.reload();
      }
    });
  };

  // 开始学期操作
  const onStart = (id: string) => {
    if (hasInProgressSemester) {
      return;
    }

    // 设置要开始的学期ID
    setStartSemesterId(id);

    const startSemester = tableData.find((item) => item.id === id);
    const endedSemesters = tableData.filter((item) => item.isCurrent === SEMESTER_STATUS.ENDED);

    if (endedSemesters.length > 0 && startSemester) {
      // 按更新时间排序找最新的已结束学期
      const latestEndedSemester = [...endedSemesters].sort(
        (a, b) => new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime()
      )[0];

      // 比较即将开始的学期是否与最近结束的学期为同一学年
      const isSame = startSemester.year === latestEndedSemester.year;

      setIsSameYear(isSame);
    } else {
      setIsSameYear(true);
    }

    onOpenStartModal();
  };

  // 确认开始学期
  const handleStartConfirm = async (config: SemesterStartConfig) => {
    try {
      setStartLoading(true);

      // 检查学期ID是否存在
      if (!startSemesterId) {
        Toast.error('未选择学期，无法开始');
        console.error('未选择学期ID');
        return;
      }

      // 构建API所需参数
      const apiParams = {
        id: startSemesterId,
        isCrossYear: isSameYear ? 0 : (1 as 0 | 1), // 0-跨学期，1-跨学年
        copyTenantSubject: config.copySubject ? 1 : (0 as 0 | 1), // 学科配置
        copySchoolManageTeacher: config.administrativeWorkType === 'copy' ? 1 : (0 as 0 | 1), // 行政工作分配
        copySchoolSubjectManager: config.subjectResponsibleType === 'copy' ? 1 : (0 as 0 | 1), // 学科负责工作分配
        copySchoolSubjectTeacher: config.teachingAssignmentType === 'copy' ? 1 : (0 as 0 | 1) // 任课工作分配
      };

      // 调用API
      await startSemester(apiParams);
      Toast.success('学期已成功开始');
      onCloseStartModal();
      tableRef.current?.reload();
    } catch (error) {
      Toast.error('开始学期失败');
    } finally {
      setStartLoading(false);
    }
  };

  const statusStyles = {
    notEnabled: {
      color: '#C9CDD4',
      display: 'flex',
      alignItems: 'center',
      opacity: 0.6
    },
    enabled: {
      color: '#00B42A',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#FC8F0A',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const getLatestEndedSemesterId = () => {
    const endedSemesters = tableData.filter((item) => item.isCurrent === SEMESTER_STATUS.ENDED);
    if (endedSemesters.length === 0) return null;

    // 按更新时间排序找最新的
    const sortedSemesters = [...endedSemesters].sort(
      (a, b) => new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime()
    );
    return sortedSemesters[0].id;
  };

  const columns: TableProps<ClientSemesterPageType>['columns'] = useMemo(() => {
    return [
      {
        title: '学年',
        key: 'year',
        dataIndex: 'year'
      },
      {
        title: '学期',
        key: 'type',
        render: (_: any, row: ClientSemesterPageType) => (
          <Flex>{row.type === 1 ? '第一学期' : '第二学期'}</Flex>
        )
      },
      {
        title: '起止时间',
        key: 'startDate',
        dataIndex: 'startDate',
        render: (_: any, row: ClientSemesterPageType) => (
          <Flex>
            {row.startDate} 至 {row.endDate}
          </Flex>
        )
      },
      {
        title: '状态',
        key: 'isCurrent',
        dataIndex: 'isCurrent',
        render: (status: number) => {
          switch (status) {
            case 0:
              return (
                <>
                  <Box style={statusStyles.notEnabled}>
                    <Box
                      style={{
                        ...statusStyles.dot,
                        backgroundColor: statusStyles.notEnabled.color
                      }}
                    />
                    未开始
                  </Box>
                </>
              );
            case 1:
              return (
                <>
                  <Box style={statusStyles.enabled}>
                    <Box
                      style={{
                        ...statusStyles.dot,
                        backgroundColor: statusStyles.enabled.color
                      }}
                    />
                    进行中
                  </Box>
                </>
              );
            case 2:
              return (
                <>
                  <Box style={statusStyles.disabled}>
                    <Box
                      style={{
                        ...statusStyles.dot,
                        backgroundColor: statusStyles.disabled.color
                      }}
                    />
                    已结束
                  </Box>
                </>
              );
            default:
              return <>传入数值错误</>;
          }
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 220,
        render: (_: any, row: ClientSemesterPageType) => {
          // 检查系统中是否有进行中的学期
          const hasInProgressSemester = tableData.some(
            (item) => item.isCurrent === SEMESTER_STATUS.IN_PROGRESS
          );
          // 获取最新已结束学期的ID
          const latestEndedSemesterId = getLatestEndedSemesterId();

          return (
            <Flex gap="8px">
              {(row.isCurrent === SEMESTER_STATUS.NOT_STARTED ||
                row.isCurrent === SEMESTER_STATUS.IN_PROGRESS) && (
                <Button
                  w={vwDims(70)}
                  h={vwDims(30)}
                  fontSize={vwDims(14)}
                  fontWeight="500"
                  color="#7D4DFF"
                  variant="link"
                  onClick={() => onEdit(row.id)}
                  border="1px solid #7D4DFF"
                >
                  编辑
                </Button>
              )}
              {row.isCurrent === SEMESTER_STATUS.ENDED && (
                <Button
                  w={vwDims(70)}
                  h={vwDims(30)}
                  fontSize={vwDims(14)}
                  fontWeight="500"
                  color="#7D4DFF"
                  variant="link"
                  border="1px solid #7D4DFF"
                  onClick={() => onView(row.id)}
                >
                  查看
                </Button>
              )}
              {row.isCurrent === SEMESTER_STATUS.NOT_STARTED && (
                <Tooltip
                  label={hasInProgressSemester ? '请先结束当前学期' : ''}
                  isDisabled={!hasInProgressSemester}
                >
                  <Button
                    w={vwDims(70)}
                    h={vwDims(30)}
                    fontSize={vwDims(14)}
                    fontWeight="500"
                    color="#7D4DFF"
                    variant="link"
                    border="1px solid #7D4DFF"
                    onClick={() => onStart(row.id)}
                    isDisabled={hasInProgressSemester}
                  >
                    开始
                  </Button>
                </Tooltip>
              )}

              {row.isCurrent === SEMESTER_STATUS.ENDED &&
                row.id === latestEndedSemesterId &&
                !hasInProgressSemester && (
                  <Button
                    w={vwDims(70)}
                    h={vwDims(30)}
                    fontSize={vwDims(14)}
                    fontWeight="500"
                    color="#7D4DFF"
                    variant="link"
                    border="1px solid #7D4DFF"
                    onClick={() => onRecover(row.id)}
                  >
                    恢复
                  </Button>
                )}

              {row.isCurrent === SEMESTER_STATUS.IN_PROGRESS && (
                <Button
                  w={vwDims(70)}
                  h={vwDims(30)}
                  fontSize={vwDims(14)}
                  fontWeight="500"
                  color="#4E5969"
                  variant="link"
                  border="1px solid #D1D5DB"
                  onClick={() => onEnd(row.id)}
                >
                  结束
                </Button>
              )}
              {(row.isCurrent === SEMESTER_STATUS.NOT_STARTED ||
                row.isCurrent === SEMESTER_STATUS.ENDED) && (
                <Button
                  w={vwDims(70)}
                  h={vwDims(30)}
                  fontSize={vwDims(14)}
                  fontWeight="500"
                  color="#4E5969"
                  variant="link"
                  border="1px solid #D1D5DB"
                  onClick={() => onDelete(row.id)}
                >
                  删除
                </Button>
              )}
            </Flex>
          );
        }
      }
    ];
  }, [tableData, hasInProgressSemester]);

  const ButtonsComponent = () => (
    <Button h={vwDims(36)} borderRadius="8px" onClick={onAdd} w={vwDims(163)}>
      <AddIcon mr={vwDims(8)} />
      添加学期
    </Button>
  );

  return (
    <PageContainer>
      <MyTable
        tableTitle="学期管理"
        cacheKey="semesterList"
        columns={columns}
        defaultQuery={{
          searchKey: ''
        }}
        api={getClientSemesterPage}
        rowKey="id"
        ref={tableRef}
        headerConfig={{
          showHeader: true,
          ButtonsComponent: ButtonsComponent
        }}
        tableStyle={{
          marginTop: vwDims(20)
        }}
      />

      {isOpenSemesterModal && (
        <SemesterModal
          modalId={modalId}
          mode={modalMode}
          onClose={onCloseSemesterModal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}

      <SemesterStartModal
        isOpen={isOpenStartModal}
        onClose={onCloseStartModal}
        onConfirm={handleStartConfirm}
        isLoading={startLoading}
        isSameYear={isSameYear}
        semesterId={startSemesterId}
      />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Semester;
