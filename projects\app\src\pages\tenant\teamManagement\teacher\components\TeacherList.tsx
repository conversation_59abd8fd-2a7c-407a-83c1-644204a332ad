import {
  Box,
  Text,
  Button,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Checkbox,
  Input
} from '@chakra-ui/react';
import MyBox from '@/components/common/MyBox';
import { vwDims } from '@/utils/chakra';
import TeachersEditInformation from './TeachersEditInformation';
import TeachersViewInformation from './TeachersViewInformation';
import { useState, useEffect, useRef } from 'react';
import { getTeacherPage } from '@/api/teacher';
import type { TeacherPageItem } from '@/types/api/teacher';
import { updateTeacherStatus } from '@/api/teacher';
import { useToast } from '@chakra-ui/react';
import { deleteTeacher } from '@/api/teacher';
import {
  SCHOOL_MANAGE_TEACHER_TYPE_MAP,
  SCHOOL_SUBJECT_TEACHER_TYPE_MAP
} from '@/constants/teacher';
import MyModal from '@/components/MyModal';
import Loading from '@/components/Loading';
import EmptyTip from '@/components/EmptyTip';
import MyTable from '@/components/MyTable';
import type { ColumnsType } from 'antd/es/table';

type TeacherListProps = {
  onRefresh?: () => void;
  searchParams?: any;
  refreshKey?: number;
};

type ModalType = 'edit' | 'view' | null;

const PAGE_SIZE = 10;

// 格式化班级名称，将七年级改为初一
const formatClassName = (name: string, nodeId?: string | number, classTree?: any): string => {
  if (!name) return name;
  let formattedName = name.replace(/七年级/g, '初一');
  if (formattedName.includes('班') && classTree && nodeId) {
    // 查找父级年级
    const findGradeForClass = (tree: any[], classId: string | number): string => {
      for (const node of tree) {
        if (node.children) {
          for (const child of node.children) {
            if (child.id === classId) {
              return node.deptName || node.name || '';
            }
            if (child.children) {
              for (const grandChild of child.children) {
                if (grandChild.id === classId) {
                  return child.deptName || child.name || '';
                }
              }
            }
          }
        }
      }
      return '';
    };
    const gradeName = findGradeForClass(classTree, nodeId);
    if (gradeName) {
      const formattedGrade = gradeName.replace(/七年级/g, '初一');
      return `${formattedGrade}${formattedName}`;
    }
  }
  return formattedName;
};

const TeacherList = ({ onRefresh, searchParams, refreshKey = 0 }: TeacherListProps) => {
  const [modalType, setModalType] = useState<ModalType>(null);
  const [modalIndex, setModalIndex] = useState<number | null>(null);
  const [currentTeacher, setCurrentTeacher] = useState<any>(null); // 添加当前教师数据状态
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [deleteAccount, setDeleteAccount] = useState(false);
  const [disableIndex, setDisableIndex] = useState<number | null>(null);
  const [disableAccount, setDisableAccount] = useState(false);
  const [recoverIndex, setRecoverIndex] = useState<number | null>(null);

  console.log('=== TEACHER LIST RENDERED ===', { searchParams, refreshKey });
  
  // 监听搜索参数变化
  useEffect(() => {
    console.log('TeacherList 搜索参数发生变化:', searchParams);
    // 强制刷新表格
    if (tableRef.current) {
      tableRef.current.reload();
    }
  }, [searchParams]);

  const tableRef = useRef<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isDisableOpen, onOpen: onDisableOpen, onClose: onDisableClose } = useDisclosure();
  const { isOpen: isRecoverOpen, onOpen: onRecoverOpen, onClose: onRecoverClose } = useDisclosure();
  const toast = useToast();

  // 性别、状态、操作等格式化
  const getGenderText = (gender: number) => {
    if (gender === 1) return '男';
    if (gender === 2) return '女';
    return '-';
  };
  const getStatusText = (status: number) => {
    console.log('教师状态值:', status, '类型:', typeof status);
    if (status === 0) {
      console.log('状态为0，返回在职');
      return '在职';
    }
    if (status === 1) {
      console.log('状态为1，返回离职');
      return '离职'; // 修正：1-离职
    }
    console.log('状态未知，返回-');
    return '-';
  };
  const getStatusColor = (status: number) => {
    if (status === 0) return 'green.400';
    if (status === 1) return 'red.400'; // 修正：1-离职显示红色
    return 'gray.400';
  };
  const getActions = (status: number) => {
    console.log('获取操作按钮，状态值:', status, '类型:', typeof status, '状态文本:', getStatusText(status));
    if (status === 0) {
      console.log('状态为0，返回在职操作按钮');
      return ['编辑', '离职', '删除']; // 0-在职：显示离职按钮
    }
    if (status === 1) {
      console.log('状态为1，返回离职操作按钮');
      return ['查看', '恢复', '删除']; // 1-离职：显示恢复按钮
    }
    console.log('状态未知，返回空数组');
    return [];
  };

  const handleEdit = (idx: number, record: any) => {
    setModalType('edit');
    setModalIndex(idx);
    setCurrentTeacher(record); // 设置当前教师数据
    onOpen();
  };
  const handleView = (idx: number, record: any) => {
    setModalType('view');
    setModalIndex(idx);
    setCurrentTeacher(record); // 设置当前教师数据
    onOpen();
  };

  // 删除、离职、恢复操作
  const handleDeleteClick = (id: number) => {
    setDeleteIndex(id);
    onDeleteOpen();
  };

  const handleDisableClick = (id: number) => {
    setDisableIndex(id);
    onDisableOpen();
  };

  const handleRecoverClick = (id: number) => {
    setRecoverIndex(id);
    onRecoverOpen();
  };

  const handleDeleteConfirm = async () => {
    if (deleteIndex === null) return;
    try {
      await deleteTeacher({
        id: deleteIndex,
        deleteAccount: deleteAccount ? 1 : 0
      });
      toast({ title: '删除成功', status: 'success' });
      onDeleteClose();
      setDeleteIndex(null);
      setDeleteAccount(false);
      
      // 强制刷新表格数据
      if (tableRef.current) {
        tableRef.current.reload();
      }
      onRefresh?.();
      
    } catch (e) {
      toast({ title: '删除失败', status: 'error' });
    }
  };

  const handleDisableConfirm = async () => {
    if (disableIndex === null) return;
    try {
      console.log('设置教师离职，ID:', disableIndex, '状态:', 1, '禁用账号:', disableAccount);
      await updateTeacherStatus({
        id: disableIndex,
        status: 1, // 1-离职
        disableAccount: disableAccount ? 1 : 0
      });
      console.log('教师状态更新成功');
      toast({ title: '已设为离职', status: 'success' });
      onDisableClose();
      setDisableIndex(null);
      setDisableAccount(false);
      
      // 强制刷新表格数据
      if (tableRef.current) {
        tableRef.current.reload();
      }
      onRefresh?.();
      
    } catch (e: any) {
      console.error('教师状态更新失败:', e);
      console.error('错误详情:', e.response?.data || e.message);
      toast({ 
        title: '操作失败', 
        description: e.response?.data?.message || e.message || '未知错误',
        status: 'error' 
      });
    }
  };

  const handleRecoverConfirm = async () => {
    if (recoverIndex === null) return;
    try {
      await updateTeacherStatus({ id: recoverIndex, status: 0 }); // 0-在职
      toast({ title: '已恢复为在职', status: 'success' });
      onRecoverClose();
      setRecoverIndex(null);
      
      // 强制刷新表格数据
      if (tableRef.current) {
        tableRef.current.reload();
      }
      onRefresh?.();
      
    } catch (e: any) {
      console.error('教师状态恢复失败:', e);
      console.error('错误详情:', e.response?.data || e.message);
      toast({ 
        title: '操作失败', 
        description: e.response?.data?.message || e.message || '未知错误',
        status: 'error' 
      });
    }
  };

  // 创建表格列配置
  const columns: ColumnsType<TeacherPageItem> = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (text: string) => <Text fontSize={vwDims(14)}>{text}</Text>
    },
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
      width: 120,
      render: (text: string) => <Text fontSize={vwDims(14)}>{text}</Text>
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (text: string) => <Text fontSize={vwDims(14)}>{text}</Text>
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 60,
      render: (gender: number) => <Text fontSize={vwDims(14)}>{getGenderText(gender)}</Text>
    },
    {
      title: '行政职务',
      dataIndex: 'schoolManageTeachers',
      key: 'schoolManageTeachers',
      width: 140,
      render: (teachers: any[]) => (
        <Text fontSize={vwDims(14)} noOfLines={2} title={teachers && teachers.length > 0
          ? teachers
              .map((role) => SCHOOL_MANAGE_TEACHER_TYPE_MAP[String(role)] || role)
              .join(', ')
          : '-'}>
          {teachers && teachers.length > 0
            ? teachers
                .map((role) => SCHOOL_MANAGE_TEACHER_TYPE_MAP[String(role)] || role)
                .join(', ')
            : '-'}
        </Text>
      )
    },
    {
      title: '教学职务',
      dataIndex: 'schoolSubjectTeachers',
      key: 'schoolSubjectTeachers',
      width: 140,
      render: (teachers: any[]) => (
        <Text fontSize={vwDims(14)} noOfLines={2} title={teachers && teachers.length > 0
          ? teachers
              .map((role) => SCHOOL_SUBJECT_TEACHER_TYPE_MAP[Number(role)] || role)
              .join(', ')
          : '-'}>
          {teachers && teachers.length > 0
            ? teachers
                .map((role) => SCHOOL_SUBJECT_TEACHER_TYPE_MAP[Number(role)] || role)
                .join(', ')
            : '-'}
        </Text>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Flex align="center" gap={vwDims(8)}>
          <Box w={vwDims(8)} h={vwDims(8)} borderRadius="full" bg={getStatusColor(status)} />
          <Text fontSize={vwDims(14)}>{getStatusText(status)}</Text>
        </Flex>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record, index) => {
        console.log('渲染操作列，教师:', record.name, '状态:', record.status, '状态类型:', typeof record.status);
        const actions = getActions(record.status);
        console.log('获取到的操作按钮:', actions);
        return (
          <Flex gap={vwDims(8)}>
            {actions.map((act) => (
              <Button
                key={act}
                size="sm"
                variant="outline"
                colorScheme={
                  act === '查看'
                    ? 'blue'
                    : act === '删除'
                      ? 'gray'
                      : act === '离职'
                        ? 'red'
                        : act === '恢复'
                          ? 'green'
                          : 'gray'
                }
                border={act === '编辑' ? '1px solid #7D4DFF' : undefined}
                color={act === '编辑' ? '#7D4DFF' : undefined}
                _hover={act === '编辑' ? { bg: 'rgba(125, 77, 255, 0.1)' } : undefined}
                onClick={
                  act === '编辑'
                    ? () => handleEdit(index, record)
                    : act === '查看'
                      ? () => handleView(index, record)
                      : act === '删除'
                        ? () => handleDeleteClick(Number(record.id))
                        : act === '离职'
                          ? () => handleDisableClick(Number(record.id))
                          : act === '恢复'
                            ? () => handleRecoverClick(Number(record.id))
                            : undefined
                }
              >
                {act}
              </Button>
            ))}
          </Flex>
        );
      }
    }
  ];

  return (
    <MyBox h="100%" display="flex" flexDirection="column">
      <MyTable
        api={async (params) => {
          // 添加时间戳和随机数避免缓存
          const paramsWithTimestamp = {
            ...params,
            _t: Date.now(),
            _r: Math.random()
          };
          console.log('请求教师列表参数:', paramsWithTimestamp);
          console.log('搜索参数 searchParams:', searchParams);
          const response = await getTeacherPage(paramsWithTimestamp);
          console.log('教师列表原始响应:', response);
          console.log('教师列表数据:', response.records.map(item => ({ 
            id: item.id, 
            name: item.name, 
            status: item.status,
            statusType: typeof item.status,
            statusText: item.status === 0 ? '在职' : item.status === 1 ? '离职' : '未知'
          })));
          return {
            records: response.records,
            total: response.total,
            size: response.size,
            current: response.current,
            pages: Math.ceil(response.total / response.size)
          };
        }}
        columns={columns}
        defaultQuery={searchParams}
        cacheKey={`teacher-list-${refreshKey}-${JSON.stringify(searchParams)}`}
        pageConfig={{
          showPaginate: true,
          defaultCurrent: 1,
          defaultSize: PAGE_SIZE,
          showSizeChanger: true,
          showQuickJumper: true
        }}
        emptyConfig={{
          EmptyComponent: () => <EmptyTip text="暂无教师数据" />
        }}
        boxStyle={{
          bg: 'transparent',
          p: 0
        }}
        tableStyle={{
          bg: 'white'
        }}
        scroll={{ y: 'calc(100vh - 300px)' }}
        rowKey="id"
        scrollMode="sticky"
        ref={tableRef}
        key={`${refreshKey}-${JSON.stringify(searchParams)}`} // 添加key属性，包含搜索参数
      />

      {/* 弹窗 */}
      {modalType === 'edit' && modalIndex !== null && currentTeacher && (
        <TeachersEditInformation
          teacher={currentTeacher}
          isOpen={isOpen}
          onClose={onClose}
          onSuccess={() => {
            onClose();
            // 强制刷新表格数据
            if (tableRef.current) {
              tableRef.current.reload();
            }
            onRefresh?.();
          }}
        />
      )}
      {modalType === 'view' && currentTeacher && (
        <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalBody p={0}>
              <TeachersViewInformation teacher={currentTeacher} onClose={onClose} />
            </ModalBody>
          </ModalContent>
        </Modal>
      )}

      {/* 删除确认弹窗 */}
      <MyModal isOpen={isDeleteOpen} onClose={onDeleteClose} title="删除" iconSrc="trash" w="400px">
        <Box p={6}>
          <Text fontSize="14px" color="gray.700" mb={4} lineHeight="1.6">
            请确认,删除后将不可恢复,历史相关数据亦将无法找回。此外,您可同步删除其账号。
          </Text>
          <Flex align="center" gap={3} mb={6}>
            <Checkbox
              colorScheme="purple"
              isChecked={deleteAccount}
              onChange={(e) => setDeleteAccount(e.target.checked)}
              size="md"
            >
              <Text fontSize="14px" color="gray.700">
                同步删除账号
              </Text>
            </Checkbox>
          </Flex>
          <Flex justify="flex-end" gap={3}>
            <Button
              variant="outline"
              onClick={onDeleteClose}
              size="md"
              borderRadius="md"
              borderColor="gray.300"
              color="gray.700"
              _hover={{ bg: 'gray.50' }}
            >
              取消
            </Button>
            <Button
              colorScheme="purple"
              onClick={handleDeleteConfirm}
              size="md"
              borderRadius="md"
              bg="purple.500"
              color="white"
              _hover={{ bg: 'purple.600' }}
            >
              确认
            </Button>
          </Flex>
        </Box>
      </MyModal>

      {/* 离职确认弹窗 */}
      <MyModal
        isOpen={isDisableOpen}
        onClose={onDisableClose}
        title="设为离职"
        iconSrc="close"
        w="400px"
      >
        <Box p={6}>
          <Text fontSize="14px" color="gray.700" mb={4} lineHeight="1.6">
            该教师将不在教学与行政管理体系内可用,您也可同步禁用该教师账号。
          </Text>
          <Flex align="center" gap={3} mb={6}>
            <Checkbox
              colorScheme="purple"
              isChecked={disableAccount}
              onChange={(e) => setDisableAccount(e.target.checked)}
              size="md"
            >
              <Text fontSize="14px" color="gray.700">
                同步禁用账号
              </Text>
            </Checkbox>
          </Flex>
          <Flex justify="flex-end" gap={3}>
            <Button
              variant="outline"
              onClick={onDisableClose}
              size="md"
              borderRadius="md"
              borderColor="gray.300"
              color="gray.700"
              _hover={{ bg: 'gray.50' }}
            >
              取消
            </Button>
            <Button
              colorScheme="purple"
              onClick={handleDisableConfirm}
              size="md"
              borderRadius="md"
              bg="purple.500"
              color="white"
              _hover={{ bg: 'purple.600' }}
            >
              确认
            </Button>
          </Flex>
        </Box>
      </MyModal>

      {/* 恢复确认弹窗 */}
      <MyModal
        isOpen={isRecoverOpen}
        onClose={onRecoverClose}
        title="设为在职"
        iconSrc="check"
        w="400px"
      >
        <Box p={6}>
          <Text fontSize="14px" color="gray.700" mb={6} lineHeight="1.6">
            该教师将重新纳入教学与行政管理体系,若账号曾被禁用,将自动恢复正常。
          </Text>
          <Flex justify="flex-end" gap={3}>
            <Button
              variant="outline"
              onClick={onRecoverClose}
              size="md"
              borderRadius="md"
              borderColor="gray.300"
              color="gray.700"
              _hover={{ bg: 'gray.50' }}
            >
              取消
            </Button>
            <Button
              colorScheme="purple"
              onClick={handleRecoverConfirm}
              size="md"
              borderRadius="md"
              bg="purple.500"
              color="white"
              _hover={{ bg: 'purple.600' }}
            >
              确认
            </Button>
          </Flex>
        </Box>
      </MyModal>
    </MyBox>
  );
};

export default TeacherList;
