// 资源类型
export type ResourceType =
  | '试卷'
  | '教案'
  | '学案'
  | '作业'
  | '试题'
  | '素材'
  | '微课'
  | '备课整合'
  | '教参';

export interface ResourceItem {
  id: string;
  title: string;
  type: ResourceType;
  fileType: string;
  fileSize: string;
  date: string;
  subject: string;
  grade: string;
  version?: string;
  tags?: string[];
  downloadCount?: number;
  previewUrl?: string;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface TabOption {
  label: string;
  value: string;
  id: number;
}

export interface TopicItem {
  id: string;
  title: string;
  type: '专题' | '合集';
  description?: string;
  resourceCount?: number;
}
