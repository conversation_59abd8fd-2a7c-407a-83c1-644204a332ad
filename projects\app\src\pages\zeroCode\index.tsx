import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import React, { ForwardRefExoticComponent, RefAttributes, useEffect, useState } from 'react';
import CreateApp from './components/CreateApp';
import MyApp from './components/MyApp';
import Dataset from './components/Dataset';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';
import { useChatStore } from '@/store/useChatStore';
import { useTenantStore } from '@/store/useTenantStore';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';

export type ZeroCodeProps = {
  currentTab: ZeroCodeTabType['value'];
  TabRender: React.ComponentType<any>;
};

// 应用类型
export type ZeroCodeTabType = {
  name: string;
  value: 'create' | 'my' | 'dataset';
  component: React.ComponentType<ZeroCodeProps>;
  id: string;
};
const tabs: ZeroCodeTabType[] = [
  {
    name: '创建应用',
    value: 'create',
    component: CreateApp,
    id: 'zero_code_space1'
  },
  {
    name: '我的应用',
    value: 'my',
    component: MyApp,
    id: 'zero_code_space2'
  },
  {
    name: '知识库',
    value: 'dataset',
    component: Dataset,
    id: 'zeroCodeDataset'
  }
];

const ZeroCode = ({}: {}) => {
  const { currentStep, setCurrentStep } = useChatStore();
  const { tenant } = useTenantStore();
  console.log(currentStep);

  const router = useRouter();
  if (!router.query.tab) {
    router.push({
      pathname: '/zeroCode',
      query: { tab: 'create' }
    });
  }
  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr={respDims(32, GLOBAL_DIMS_MIN_SCALE)}
            py={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
            position="relative"
            id={tab.id}
            {...(tab.value === router.query.tab
              ? {
                  color: 'primary.500',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: respDims(5, GLOBAL_DIMS_MIN_SCALE),
                    borderRadius: respDims(5, GLOBAL_DIMS_MIN_SCALE),
                    bg: 'linear-gradient(116deg, #DC7EFF 19.7%, #601CFF 86.86%)'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize={respDims(18, GLOBAL_DIMS_MIN_SCALE)}
            fontWeight="bold"
            cursor="pointer"
            color="#303133"
            css={{
              opacity: '1!important'
            }}
            onClick={() => {
              router.push({
                pathname: '/zeroCode',
                query: {
                  tab: tab.value
                }
              });
            }}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };

  const currentTabComponent = tabs.find((tab) => tab.value === router.query.tab)?.component;

  return (
    <Flex
      h="100%"
      px={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
      py={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
      bgImage={`url(${
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/v2/gradient_bg6.png'
      })`}
      bgRepeat="no-repeat"
      bgSize="100% 100%"
    >
      {currentTabComponent &&
        React.createElement(currentTabComponent, {
          currentTab: router.query.tab as ZeroCodeTabType['value'],
          TabRender
        })}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default ZeroCode;
