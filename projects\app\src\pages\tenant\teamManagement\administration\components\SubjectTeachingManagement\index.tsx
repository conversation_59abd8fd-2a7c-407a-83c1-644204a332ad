import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import { Box, useDisclosure } from '@chakra-ui/react';
import { Table, Segmented, Empty } from 'antd';
import { ColumnType } from 'antd/es/table';
import SvgIcon from '@/components/SvgIcon';
import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import {
  getSubjectTeacherTree,
  editSubjectManager
} from '@/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';
import SettingSubjectsModal from '../SettingSubjectsModal';

interface SubjectTeachingManagementProps {
  onEditTeachers: (
    role: 'leader' | 'teacher',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string
  ) => void;
  refreshList: () => void;
  semesterData: {
    year: string;
    type: 1 | 2;
  };
  highlightedTmbIds: number[];
  semesterId: string;
}

interface RecordType {
  className: string;
  isGrade: boolean;
}

interface Grade {
  [subjectName: string]: Department;
}

const SubjectTeachingManagement: React.FC<SubjectTeachingManagementProps> = ({
  onEditTeachers,
  refreshList,
  semesterData,
  highlightedTmbIds,
  semesterId
}) => {
  const [activeGrade, setActiveGrade] = useState<string>(() => {
    return localStorage.getItem('activeGrade') || '一年级';
  });
  const [hoveredCell, setHoveredCell] = useState<{ id: string; users: TmbUser[] } | null>(null);
  const [copiedUsers, setCopiedUsers] = useState<TmbUser[]>([]);
  const tableRef = useRef<HTMLDivElement>(null);
  const scrollPosition = useRef<{ scrollTop: number; scrollLeft: number }>({
    scrollTop: 0,
    scrollLeft: 0
  });
  const [treeData, setTreeData] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 添加弹窗控制状态
  const {
    isOpen: isOpenSettingModal,
    onOpen: onOpenSettingModal,
    onClose: onCloseSettingModal
  } = useDisclosure();
  const [modalSettingId, setModalSettingId] = useState<string>('');
  const [modalTitle, setModalTitle] = useState<string>('');
  const [modalDeptName, setModalDeptName] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<
    { name: string; id: string; tmbId?: string }[]
  >([]);

  // 添加学科相关状态
  const [teachType, setTeachType] = useState<number>(4);
  const [subjectId, setSubjectId] = useState<number>(1);

  // 获取学科教师数据的函数
  const fetchSubjectTeacherData = useCallback(async () => {
    if (!semesterId) return;

    try {
      setLoading(true);
      const res = await getSubjectTeacherTree(semesterId);
      if (res?.treeList && Array.isArray(res.treeList)) {
        // 检查树形数据是否有效
        const hasInvalidData = res.treeList.some((item: Department) => {
          // 检查id是否存在，如果不存在则使用deptId
          const hasValidId = !!item.id || !!item.deptId;
          if (!hasValidId) {
          }
          return !hasValidId;
        });

        if (hasInvalidData) {
          console.warn('警告：部分学科教师数据缺少ID');
        }

        // 对数据进行处理，确保每个节点都有id
        const processedData = res.treeList.map((item: Department) => {
          // 如果id不存在但deptId存在，则使用deptId作为id
          if (!item.id && item.deptId) {
            return { ...item, id: item.deptId };
          }
          return item;
        });

        setTreeData(processedData);
      } else {
        setTreeData([]);
      }
    } catch (err) {
      Toast.error('获取数据失败，请重试');
      setTreeData([]);
    } finally {
      setLoading(false);
    }
  }, [semesterId]);

  // 获取学科教师数据
  useEffect(() => {
    if (!semesterId) return;
    fetchSubjectTeacherData();
  }, [semesterId, fetchSubjectTeacherData]);

  const renderTeachers = (teacherInfo: TmbUser[]) => {
    return teacherInfo.map((t, index) => (
      <React.Fragment key={t.tmbId}>
        <span
          style={{
            color: highlightedTmbIds.includes(Number(t.tmbId)) ? 'red' : 'inherit'
          }}
        >
          {t.userName}
        </span>
        {index < teacherInfo.length - 1 && ', '}
      </React.Fragment>
    ));
  };

  const handleGradeChange = (value: any) => {
    setActiveGrade(value);
    localStorage.setItem('activeGrade', value);
  };

  // 按年级和学科分组数据
  const groupedData = useMemo(() => {
    return treeData.reduce((acc: Record<string, Grade>, item) => {
      if (!acc[item.deptName]) {
        acc[item.deptName] = {};
      }
      acc[item.deptName][item.subjectName] = item;
      return acc;
    }, {});
  }, [treeData]);

  // 获取所有年级
  const grades = useMemo(() => {
    return Object.keys(groupedData).sort((a, b) => {
      const gradeOrder = [
        '一年级',
        '二年级',
        '三年级',
        '四年级',
        '五年级',
        '六年级',
        '初一',
        '初二',
        '初三'
      ];
      return gradeOrder.indexOf(a) - gradeOrder.indexOf(b);
    });
  }, [groupedData]);

  // 获取当前年级的学科
  const subjects = useMemo(() => {
    return Object.keys(groupedData[activeGrade] || {});
  }, [groupedData, activeGrade]);

  const handlePasteAction = async (id: string) => {
    if (!id) {
      Toast.error('无法粘贴到此处：缺少目标ID');
      return;
    }

    if (copiedUsers.length === 0) {
      Toast.info('没有可粘贴的内容');
      return;
    }

    try {
      console.log('handlePasteAction被调用:', { id, copiedUsers });

      // 记录当前滚动条位置
      scrollPosition.current.scrollTop = tableRef.current?.scrollTop || 0;
      scrollPosition.current.scrollLeft = tableRef.current?.scrollLeft || 0;

      // 获取目标位置已经存在的教师列表
      const existingUsers = treeData.find((node) => node.id === id)?.tmbUserList || [];
      // 过滤掉已经存在的教师
      const newUsers = copiedUsers.filter(
        (user) => !existingUsers.some((existingUser) => existingUser.tmbId === user.tmbId)
      );

      if (newUsers.length === 0) {
        Toast.info('目标位置已经包含所有要粘贴的教师');
        return;
      }

      // 提取tmbIds数组
      const tmbIdsArray = newUsers.map((user) => Number(user.tmbId));

      try {
        await handleModalClose(true, tmbIdsArray, id);
      } catch (error) {
        console.error('调用handleModalClose处理粘贴失败:', error);
        throw error; // 继续抛出错误以便外部catch捕获
      }

      Toast.success('粘贴成功');
      refreshList();
    } catch (error) {
      console.error('粘贴失败:', error);
      Toast.error('粘贴失败，请重试');
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && hoveredCell) {
        e.preventDefault();
        setCopiedUsers(hoveredCell.users);
        Toast.success('复制成功');
      }
    };

    const handlePaste = (e: ClipboardEvent) => {
      if (hoveredCell) {
        e.preventDefault();
        handlePasteAction(hoveredCell.id);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('paste', handlePaste);
    };
  }, [hoveredCell, copiedUsers]);

  useEffect(() => {
    // 恢复滚动条位置
    if (tableRef.current) {
      tableRef.current.scrollTop = scrollPosition.current.scrollTop;
      tableRef.current.scrollLeft = scrollPosition.current.scrollLeft;
    }
  }, [treeData]);

  // 处理弹窗关闭
  const handleModalClose = async (
    submitted: boolean,
    selectedTmbIds?: number[],
    deptId?: string
  ) => {
    if (submitted) {
      try {
        // 确保参数有效性
        const targetDeptId = deptId || modalSettingId;
        const targetTmbIds = selectedTmbIds || [];

        if (!targetDeptId) {
          console.error('无效的部门ID:', { deptId, modalSettingId });

          // 尝试从treeData中查找有效的部门ID
          const matchingDept = treeData.find(
            (item) =>
              (item.deptName === modalDeptName.split(' ')[0] &&
                item.subjectName === modalDeptName.split(' ')[1]) ||
              item.deptName === modalDeptName
          );

          if (matchingDept && (matchingDept.id || matchingDept.deptId)) {
            const foundId = matchingDept.id || matchingDept.deptId;
            console.log('使用找到的ID继续操作:', foundId);

            try {
              // 提取teachType和subjectId
              let matchingTeachType = teachType;
              let matchingSubjectId = subjectId;

              if ((matchingDept as any).teachType !== undefined) {
                matchingTeachType = (matchingDept as any).teachType;
              }
              if (matchingDept.subjectId) {
                matchingSubjectId = Number(matchingDept.subjectId);
              }

              // 调用API
              await editSubjectManager({
                deptId: foundId,
                teachType: matchingTeachType,
                subjectId: matchingSubjectId,
                semesterId: Number(semesterId),
                tmbIds: selectedTmbIds || []
              });

              Toast.success('操作成功');
              // 重新获取数据
              await fetchSubjectTeacherData();
              refreshList();
              onCloseSettingModal();
              return;
            } catch (innerError) {
              console.error('使用匹配ID调用API失败:', innerError);
              Toast.error('操作失败，请重试');
            }
          }

          Toast.error('操作失败：无法获取有效的部门ID，请重新选择');
          onCloseSettingModal();
          return;
        }

        // 在treeData中查找对应的节点，获取teachType和subjectId
        const targetNode = treeData.find(
          (node) => node.id === targetDeptId || node.deptId === targetDeptId
        );
        let teachTypeValue = teachType; // 默认使用当前设置的teachType
        let subjectIdValue = subjectId; // 默认使用当前设置的subjectId

        if (targetNode) {
          // 如果找到对应的节点，从中提取teachType和subjectId
          if ((targetNode as any).teachType !== undefined) {
            teachTypeValue = (targetNode as any).teachType;
          }
          if (targetNode.subjectId) {
            subjectIdValue = Number(targetNode.subjectId);
          }
        }

        // 调用接口提交数据
        await editSubjectManager({
          deptId: targetDeptId,
          teachType: teachTypeValue, // 使用从树形数据中获取的值
          subjectId: subjectIdValue, // 使用从树形数据中获取的值
          semesterId: Number(semesterId),
          tmbIds: targetTmbIds
        });

        Toast.success('操作成功');

        // 重新获取学科教师树数据
        await fetchSubjectTeacherData();
        refreshList();
      } catch (error) {
        console.error('提交数据失败:', error);
        Toast.error('操作失败，请重试');
      }
    }
    onCloseSettingModal();
  };

  // 处理编辑教师
  const handleEditTeachers = (
    role: 'leader' | 'teacher',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string,
    teacherInfo?: TmbUser[]
  ) => {
    if (!id) {
      console.warn('尝试编辑时缺少部门ID:', { role, grade, subject, className });

      // 尝试通过名称查找对应的部门
      let foundDeptId: string | null = null;

      if (role === 'leader' && subject) {
        // 查找学科组长对应的部门
        const gradeSubject = groupedData[grade]?.[subject];
        if (gradeSubject && (gradeSubject.id || gradeSubject.deptId)) {
          foundDeptId = gradeSubject.id || gradeSubject.deptId;
          console.log('找到学科组长对应的部门ID:', { grade, subject, foundDeptId });
        }
      } else if (role === 'teacher' && subject && className) {
        // 查找班级任课教师对应的部门
        const gradeSubject = groupedData[grade]?.[subject];
        if (gradeSubject?.children) {
          const classData = gradeSubject.children.find((c) => c.deptName === className);
          if (classData && (classData.id || classData.deptId)) {
            foundDeptId = classData.id || classData.deptId;
            console.log('找到班级任课教师对应的部门ID:', {
              grade,
              subject,
              className,
              foundDeptId
            });
          }
        }
      }

      if (foundDeptId) {
        // 如果找到了ID，使用找到的ID继续操作
        console.log('使用找到的ID继续操作:', foundDeptId);
        id = foundDeptId;
      } else {
        Toast.error('无法获取部门信息，请刷新页面后重试');
        return;
      }
    }

    let title = '';
    let deptName = '';
    // 从treeData中获取teachType和subjectId
    let typeValue = 6; // 默认值
    let subjId = 1; // 默认值

    // 根据角色和ID查找对应的数据项，从中提取teachType和subjectId
    if (role === 'leader' && subject) {
      // 查找年级学科组长对应的数据
      const gradeSubject = groupedData[grade]?.[subject];
      if (gradeSubject) {
        // 如果数据中包含teachType (使用类型断言)，则使用该值
        if ((gradeSubject as any).teachType !== undefined) {
          typeValue = (gradeSubject as any).teachType;
        } else if (gradeSubject.subDeptType !== undefined) {
          // 根据subDeptType推断teachType
          typeValue = gradeSubject.subDeptType === 1 ? 6 : 6;
        }

        // 提取subjectId
        if (gradeSubject.subjectId) {
          subjId = Number(gradeSubject.subjectId);
        }

        console.log('从年级学科数据中获取参数:', {
          role,
          id,
          grade,
          subject,
          teachType: typeValue,
          subjectId: subjId,
          originalData: {
            teachType: (gradeSubject as any).teachType,
            subDeptType: gradeSubject.subDeptType,
            subjectId: gradeSubject.subjectId
          }
        });
      }
    } else if (role === 'teacher' && subject && className) {
      // 查找班级任课教师对应的数据
      const gradeSubject = groupedData[grade]?.[subject];
      if (gradeSubject?.children) {
        const classData = gradeSubject.children.find((c) => c.deptName === className);
        if (classData) {
          // 如果数据中包含teachType (使用类型断言)，则使用该值
          if ((classData as any).teachType !== undefined) {
            typeValue = (classData as any).teachType;
          } else if (classData.subDeptType !== undefined) {
            // 根据subDeptType推断teachType
            typeValue = classData.subDeptType === 1 ? 6 : 6;
          }

          // 提取subjectId
          if (classData.subjectId) {
            subjId = Number(classData.subjectId);
          }

          console.log('从班级学科数据中获取参数:', {
            role,
            id,
            grade,
            subject,
            className,
            teachType: typeValue,
            subjectId: subjId,
            originalData: {
              teachType: (classData as any).teachType,
              subDeptType: classData.subDeptType,
              subjectId: classData.subjectId
            }
          });
        }
      }
    }

    switch (role) {
      case 'leader':
        title = '学科组长设置';
        deptName = `${grade} ${subject}`;
        // typeValue = 1; // 不再使用动态值
        break;
      case 'teacher':
        title = '任课教师设置';
        deptName = `${grade}${className}(${subject})`;
        // typeValue = 2; // 不再使用动态值
        break;
      default:
        title = '';
    }

    // 转换教师数据格式
    const formattedUsers = teacherInfo
      ? teacherInfo.map((user) => ({
          name: user.userName,
          id: String(user.tmbId),
          tmbId: String(user.tmbId)
        }))
      : [];

    // 从subject中提取学科ID（假设subject格式为"语文(1)"或者只是"语文"）
    if (subject) {
      const match = subject.match(/.*\((\d+)\)/);
      if (match && match[1]) {
        subjId = Number(match[1]);
      } else {
        // 如果没有ID格式，可能需要从其他地方获取学科ID
        const subjectData = groupedData[activeGrade]?.[subject];
        if (subjectData && subjectData.subjectId) {
          subjId = Number(subjectData.subjectId);
        }
      }
    }

    // 设置弹窗数据
    setModalSettingId(id);
    setModalTitle(title);
    setModalDeptName(deptName);
    setSelectedUsers(formattedUsers);
    setTeachType(typeValue);
    setSubjectId(subjId);

    // 打开弹窗
    onOpenSettingModal();
  };

  // 表格列定义
  const columns: ColumnType<RecordType>[] = useMemo(
    () => [
      {
        title: '年级班级/学科',
        dataIndex: 'className',
        key: 'className',
        width: 128,
        fixed: 'left' as 'left',
        onCell: () => ({
          style: {
            backgroundColor: '#f9f9f9'
          }
        })
      },
      ...subjects.map((subject) => ({
        title: subject,
        key: subject,
        width: 150,
        render: (_: any, record: RecordType) => {
          const subjectData = groupedData[activeGrade]?.[subject];
          let teacherInfo: TmbUser[] = [];
          let role: string = '';
          let classData: Department | undefined;
          let cellId: string | null = null;

          if (record.isGrade) {
            teacherInfo = (subjectData?.tmbUserList || []) as unknown as TmbUser[];
            role = '组长';
            // 确保获取到有效的ID，如果id不存在则尝试使用deptId
            cellId = subjectData?.id || subjectData?.deptId || null;
          } else {
            classData = subjectData?.children?.find((c) => c.deptName === record.className);
            teacherInfo = (classData?.tmbUserList || []) as unknown as TmbUser[];
            role = '教师';
            // 确保获取到有效的ID，如果id不存在则尝试使用deptId
            cellId = classData?.id || classData?.deptId || null;
          }

          return (
            <Box
              onMouseEnter={() => {
                setHoveredCell({ id: cellId!, users: teacherInfo });
              }}
              onMouseLeave={() => {
                setHoveredCell(null);
              }}
              onKeyDown={(e) => {
                if ((e.ctrlKey || e.metaKey) && e.key === 'c' && teacherInfo.length > 0) {
                  e.preventDefault();
                  setCopiedUsers(teacherInfo);
                  Toast.success('复制成功');
                }
              }}
              onPaste={(e) => {
                e.preventDefault();
                if (cellId) {
                  handlePasteAction(cellId);
                }
              }}
              tabIndex={0}
              style={{
                position: 'relative',
                height: '100%',
                width: '100%',
                backgroundColor: hoveredCell?.id === cellId ? 'primary.50' : 'transparent',
                transition: 'background-color 0.3s',
                cursor: 'pointer'
              }}
            >
              <Box padding="8px">
                <span>
                  {role}:{teacherInfo.length > 0 ? renderTeachers(teacherInfo) : '未设置'}
                </span>
                <span
                  style={{
                    position: 'absolute',
                    right: '5px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    opacity: hoveredCell?.id === cellId ? 1 : 0,
                    transition: 'opacity 0.3s'
                  }}
                >
                  <SvgIcon
                    name="editIcon"
                    w="16px"
                    h="16px"
                    cursor="pointer"
                    onClick={() =>
                      handleEditTeachers(
                        record.isGrade ? 'leader' : 'teacher',
                        cellId,
                        activeGrade,
                        subject,
                        record.className,
                        teacherInfo
                      )
                    }
                  />
                </span>
              </Box>
            </Box>
          );
        }
      }))
    ],
    [activeGrade, groupedData, subjects, onEditTeachers, highlightedTmbIds, hoveredCell]
  );

  // 生成表格数据
  const tableData = useMemo(() => {
    if (treeData.length === 0) {
      return [];
    }
    const data: RecordType[] = [];
    const classNames =
      groupedData[activeGrade]?.[subjects[0]]?.children?.map((c) => c.deptName) || [];
    classNames.forEach((className) => {
      data.push({ className, isGrade: false });
    });
    return data;
  }, [activeGrade, groupedData, subjects, treeData]);

  if (loading) {
    return (
      <Box w="100%" textAlign="center" py="50px">
        正在加载数据...
      </Box>
    );
  }

  if (treeData.length === 0) {
    return (
      <Box w="390px" height="100%" margin="auto" pt="10%" textAlign="center">
        <SvgIcon name="empty" w="100px" h="100px" cursor="pointer" />
        {semesterData.year && semesterData.type ? (
          <Box fontSize="16px" color="#303133" textAlign="left" mt={4} w="390px">
            {`当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂无学科教学管理数据。`}
          </Box>
        ) : (
          <Empty description="请选择学期后进行操作。" />
        )}
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" alignItems="center" justifyContent="space-between" mb="6px">
        <Segmented
          value={activeGrade}
          size="large"
          onChange={handleGradeChange}
          style={{ marginBottom: '8px' }}
          options={grades}
        />
      </Box>
      <Box ref={tableRef}>
        <Table
          bordered
          dataSource={tableData}
          columns={columns}
          pagination={false}
          rowKey="className"
          scroll={{ x: 'max-content' }}
          style={{ borderRadius: 'none' }}
        />
      </Box>

      {/* 添加弹窗组件 */}
      {isOpenSettingModal && (
        <SettingSubjectsModal
          settingId={modalSettingId}
          title={modalTitle}
          deptName={modalDeptName}
          onClose={handleModalClose}
          selectedUsers={selectedUsers}
          onSuccess={refreshList}
          semesterId={semesterId}
          deptId={modalSettingId} // 将modalSettingId作为deptId传递给子组件
        />
      )}
    </Box>
  );
};

export default SubjectTeachingManagement;
