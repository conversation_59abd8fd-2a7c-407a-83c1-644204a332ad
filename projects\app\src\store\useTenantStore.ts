import { getTenantDetail } from '@/api/tenant';
import { TenantType } from '@/types/api/tenant';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

const industryAliasMap: Record<any, string> = {
  0: '', // 其他
  1: '学校', // 教育
  2: '', // 金融
  3: '', // 互联网
  4: '', // 医疗
  5: '', // 政府
  6: '', // 企业
  7: '' // 职教
};

const redirectHomePathMap: Record<any, string> = {
  1: '/home/<USER>', // 普教
  6: '/home/<USER>', // 企业
  7: '/home/<USER>' // 职教
};

type State = {
  tenant?: TenantType;
  industryAlias?: string;
  redirectHomePath: string;
  loadTenant: (force?: boolean) => Promise<TenantType>;
};

export const useTenantStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        tenant: undefined,
        redirectHomePath: '/home/<USER>',
        industryAlias: undefined,
        loadTenant(force = false) {
          if (!force && get().tenant) {
            return Promise.resolve(get().tenant!);
          }
          return getTenantDetail().then((res) => {
            set((state) => {
              state.tenant = res;
              state.industryAlias = industryAliasMap[res.industry] || '组织';
              // 归一重定向地址
              state.redirectHomePath = redirectHomePathMap[res.industry] || '/home/<USER>';
            });
            return res;
          });
        }
      })),
      {
        name: 'tenantStore',
        partialize: () => ({})
      }
    )
  )
);
