import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, Flex, Button, BoxProps, ModalBody } from '@chakra-ui/react';
import { Select } from 'antd';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useTranslation } from 'next-i18next';
import { getTeacherList } from '@/api/tenant/teamManagement/administration';
import MyModal from '@/components/MyModal';
import { TeacherInfo } from '@/types/api/tenant/teamManagement/administration';
import styles from '../../../index.module.scss';

interface FormData {
  userIds: { name: string; id: string; tmbId?: string }[];
}

const MAX_COUNT = 5;

const SettingSubjectsModal = ({
  settingId,
  title,
  deptName,
  onClose,
  onSuccess,
  selectedUsers,
  deptId,
  semesterId,

  ...props
}: {
  settingId: string;
  title: string;
  deptName: string;
  selectedUsers: { name: string; id: string; tmbId?: string }[];
  onClose: (submitted: boolean, selectedTmbIds?: number[], settingId?: string) => void;
  onSuccess: () => void | Promise<void>;
  deptId: string; // 从父组件传递的deptId
  semesterId: string;
} & BoxProps) => {
  const { t } = useTranslation();
  const [teachers, setTeachers] = useState<TeacherInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      userIds: []
    }
  });

  // 监听userIds的变化
  const selectedUserIds = watch('userIds');

  const onSubmit: SubmitHandler<FormData> = (data) => {
    setSubmitting(true);
    try {
      // 提取用户ID并转换为数字数组
      const tmbIds = data.userIds.map((user) => Number(user.tmbId));

      console.log('提交数据:', {
        tmbIds,
        settingId,
        deptId
      });

      // 调用onClose回调，将结果返回给父组件
      onClose(true, tmbIds, deptId || settingId);
      onSuccess();
    } catch (error) {
      console.error('提交数据失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    console.log('🎯 弹窗接收到的selectedUsers:', selectedUsers);
    if (selectedUsers.length > 0) {
      setValue('userIds', selectedUsers);
      console.log('✅ 已设置表单默认值:', selectedUsers);
    } else {
      console.log('⚠️ selectedUsers为空，无法设置默认值');
    }
  }, [selectedUsers, setValue]);

  useEffect(() => {
    const fetchTeachers = async () => {
      setLoading(true);
      try {
        const response = await getTeacherList(settingId);
        if (response && Array.isArray(response)) {
          setTeachers(response);
        } else {
          setTeachers([]);
        }
      } catch (error) {
        console.error('获取教师数据失败:', error);
        setTeachers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTeachers();
  }, [settingId]);

  return (
    <MyModal isOpen={true} title={title} isCentered>
      <ModalBody>
        <Box p="20px" {...props}>
          {deptName && (
            <FormControl>
              <Flex alignItems="center" whiteSpace="nowrap">
                <FormLabel color="#4E5969" fontSize="14px">
                  {deptName}
                </FormLabel>
              </Flex>
            </FormControl>
          )}

          <FormControl mt="14px" isInvalid={!!errors.userIds}>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <Flex flexDirection="column">
                <Controller
                  name="userIds"
                  control={control}
                  rules={{ required: '请选择用户' }}
                  render={({ field }) => {
                    const currentValues = field.value.map((user) => user.id);

                    return (
                      <Select
                        mode="multiple"
                        showSearch
                        maxCount={MAX_COUNT}
                        value={currentValues}
                        className={styles['formItem']}
                        onChange={(selectedIds) => {
                          const selectedTeachers = teachers
                            .filter((teacher) => selectedIds.includes(String(teacher.tmbId)))
                            .map((teacher) => ({
                              name: teacher.name,
                              id: String(teacher.tmbId),
                              tmbId: String(teacher.tmbId)
                            }));
                          field.onChange(selectedTeachers);
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        style={{ width: '460px', height: '38px' }}
                        placeholder="请选择用户"
                        loading={loading}
                        filterOption={(input, option) => {
                          if (!option) return false;
                          const childrenAsString = option.children?.toString() || '';
                          return childrenAsString.toLowerCase().includes(input.toLowerCase());
                        }}
                        notFoundContent={loading ? '加载中...' : '暂无数据'}
                      >
                        {teachers.map((teacher) => (
                          <Select.Option key={teacher.tmbId} value={String(teacher.tmbId)}>
                            {teacher.name}
                          </Select.Option>
                        ))}
                      </Select>
                    );
                  }}
                />
                {errors.userIds && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.userIds.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  variant={'grayBase'}
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit)}
                  isLoading={submitting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SettingSubjectsModal;
