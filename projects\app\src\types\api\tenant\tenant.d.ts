import { RequestPageParams } from '@/types';

export interface TenantResourcesParams extends RequestPageParams {
  endTime?: string; // 结束时间
  pageNum?: number; // 页码
  pageSize?: number; // 页大小
  resourceTypeId?: number; // 资源类型ID
  startTime?: string; // 开始时间
  status?: number; // 状态 (0, 1, 2等)
  title?: string; // 标题/文件名
  tmbIds?: number[]; // 租户成员ID数组
}

// 租户用户类型定义
export interface TenantUser {
  attributionId: number;
  attributionToName: string;
}

// 获取资源租户列表响应类型
export interface TenantUsersResponse {
  code: number;
  data: TenantUser[];
  msg: string;
  success: boolean;
}

// 更新资源状态参数
export interface UpdateResourcesParams {
  resourceIds: number[]; // 资源ID数组
  status: number; // 要设置的状态值：1=上架，2=下架，3=删除
  rejection: string;
}
