import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { TenantSceneNavbarType } from '@/types/api/scene';
import { tenantSceneNavbar } from '@/api/scene';

type State = {
  scenes: TenantSceneNavbarType[];
  loadScenes: (force?: boolean) => Promise<TenantSceneNavbarType[]>;
};

export const useSceneStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        scenes: [],
        async loadScenes(force?: boolean) {
          if (get().scenes.length > 0 && !force) return get().scenes;
          const res = await tenantSceneNavbar();
          set((state) => {
            state.scenes = res;
          });
          return res;
        }
      })),
      {
        name: 'sceneStore',
        partialize: (state) => ({})
      }
    )
  )
);
