export const fileImgs = [
  { suffix: 'pdf', src: 'file/fill/pdf' },
  { suffix: 'csv', src: 'file/fill/csv' },
  { suffix: '(doc|docs)', src: 'file/fill/doc' },
  { suffix: 'txt', src: 'file/fill/txt' },
  { suffix: 'md', src: 'file/fill/markdown' },
  { suffix: 'html', src: 'file/fill/html' }
];

export function getFileIcon(name = '', defaultImg = 'file/fill/file') {
  return fileImgs.find((item) => new RegExp(item.suffix, 'gi').test(name))?.src || defaultImg;
}
