import { WorkflowStatusEnum } from '@/constants/api/workflow';

export type CreateWorkflowParams = {
  name: string;
  tenantAppId: string;
};

export interface ListWorkflowsParams {
  tenantAppId: string;
}

export interface CopyWorkflowParams {
  id: string;
}

export type DetailRequest = {
  id: string;
  tmbId?: string;
};

export type TenantWorkflowsPageRequest = RequestPageParams & {
  appName?: string;
  ascs?: string;
  descs?: string;
  name?: string;
  searchKey?: string;
  source?: number;
  status?: WorkflowStatusEnum;
  tenantId?: number;
  tmbId?: number;
  tenantName?: string;
  userName?: string;
};

export type UpdateWorkflowParams = {
  id: string;
  name: string;
  tenantAppId?: string;
};

export type UpdateWorkflowStatusParams = {
  id: string;
  status: WorkflowStatusEnum;
};

export type TenantWorkflow = {
  appName: string;
  createTime: string;
  finalAppId: string;
  id: string;
  industry: number;
  isDeleted: number;
  name: string;
  processNum: number;
  source: number;
  status: WorkflowStatusEnum;
  tenantAppId: string;
  tenantId: string;
  tenantName: string;
  tmbId: string;
  updateTime: string;
  userName: string;
  workflowId: number;
};

export type PagingData<T> = {
  current?: number;
  size?: number;
  records: T[];
  pages: number;
  total?: number;
};

export type RequestPageParams = { current?: number; size?: number };

export interface CreateWorkflowProcessParams {
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
  tenantWorkflowId: string;
}

export interface DeleteWorkflowProcessParams {
  id: string;
  tmbId?: string;
}

export interface ListWorkflowProcessesParams {
  id: string;
  tmbId?: string;
}

export interface ReSortWorkflowProcessesParams {
  reSortList: TenantWorkflowProcess[];
}

export interface UpdateWorkflowProcessParams {
  id: string;
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
}

export interface TenantWorkflowProcess {
  id: string;
  name?: string;
  intro?: string;
  tenantAppId?: string;
  tenantPromptId?: string;
  tenantWorkflowId?: string;
  ignoreContext?: number;
  isDeleted?: number;
  isAdd?: boolean;
  status?: number;
  sort?: number;
  createTime?: string;
  updateTime?: string;
  appAvatarUrl?: string;
  appIntro?: string;
  appName?: string;
  appSource?: DataSource;
  finalAppId?: string;
  hiddenContent?: string;
  inputContent?: string;
  proContent?: string;
  promptSource?: DataSource;
  workflowProcessId?: number;
}
