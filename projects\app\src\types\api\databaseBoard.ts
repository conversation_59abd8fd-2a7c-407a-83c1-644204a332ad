export interface ResourceDashboardParams {
  endDate: string;
  startDate: string;
  gradeId?: number;
  subjectId?: number;
}

export interface ResourceTypeDistribution {
  formatName: string;
  count: number;
}

export interface ResourceDashboardOverview {
  totalFiles: number;
  newResources: number;
  totalViews: number;
  totalDownloads: number;
  resourceTypeDistribution: ResourceTypeDistribution[];
}

export interface ResourceTrendItem {
  period: string; // 日期周期，例如 "2025-08-07"
  viewCount: number; // 查看次数
  downloadCount: number; // 下载次数
}

export interface ResourceDashboardTrend {
  trendData: ResourceTrendItem[];
}

// 热门资源项
export interface PopularResourceItem {
  id: string; // 资源ID
  name: string; // 资源名称
  type: string; // 资源类型
  visitNumber: number; // 访问量
  downloadNumber: number; // 下载量
}

// 热门资源列表
export interface PopularResourceList {
  hotResourceRanking: PopularResourceItem[];
}

// 用户使用排行项
export interface UserUsageRankingItem {
  tmbId: number;
  tmbName: string;
  viewCount: number;
  downloadCount: number;
  totalActions: number;
  avatar: string;
  subjects: {
    subjectId: number;
    subjectName: string;
  }[];
}

// 用户上传排行项
export interface UserUploadRankingItem {
  tmbId: number;
  tmbName: string;
  uploadCount: number;
  avatar: string;
  subjects: {
    subjectId: number;
    subjectName: string;
  }[];
}

// 用户排行数据
export interface UserRanking {
  userUsageRanking: UserUsageRankingItem[];
  userUploadRanking: UserUploadRankingItem[];
}

// 作业仪表盘概览数据
export interface HomeworkDashboardOverview {
  totalHomework: number;
  overallSubmitRate: number;
  lateSubmitRate: number;
  averageAccuracyRate: number;
}

// 学科作业数量统计
export interface SubjectHomeworkCount {
  subjectId: number;
  subjectName: string;
  homeworkCount: number;
}

// 年级作业统计项
export interface GradeHomeworkStat {
  gradeId: number;
  gradeName: string;
  homeworkCount: number;
  submitRate: number;
  accuracyRate: number;
  subjectHomeworkCounts: SubjectHomeworkCount[];
}

// 年级作业统计列表
export interface GradeHomeworkStats {
  gradeStats: GradeHomeworkStat[];
}

// 班级学科统计项
export interface SubjectStat {
  subjectId: number;
  subjectName: string;
  homeworkCount: number;
  accuracyRate: number;
}

// 班级学科统计
export interface ClassSubjectStat {
  clazzId: number;
  clazzName: string;
  submitRate: number;
  subjectStats: SubjectStat[];
}

// 薄弱知识点项
export interface WeakKnowledgePoint {
  knowledgePointId: number;
  knowledgePointName: string;
  subjectId: number;
  subjectName: string;
  errorRate: number;
  totalAttempts: number;
  wrongAttempts: number;
}

// 高频错题项
export interface HighFrequencyErrorQuestion {
  questionId: number;
  questionContent: string;
  subjectId: number | null;
  subjectName: string;
  errorRate: number;
  totalAttempts: number;
  wrongAttempts: number;
}

// 学校年级信息项
export interface GradeListItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: number;
  sort: number;
  lastUpgradeTime: string;
  lastSnapTime: string;
  graduateStatus: number;
  yearId: number;
}

export interface TenantSubjectItem {
  id: string | number;
  name: string;
  [key: string]: any;
}

/** 学科列表响应 */
export interface TenantSubjectListResponse {
  data: TenantSubjectItem[];
}
