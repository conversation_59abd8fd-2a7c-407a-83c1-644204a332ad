import React from 'react';
import { BoxProps } from '@chakra-ui/react';
import AddStudentModal from './AddStudentModal/index';

interface StudentEditProps extends BoxProps {
  studentId: string;
  mode?: 'edit' | 'view';
  isOpen: boolean;
  onClose: (submited: boolean, studentId?: string) => void;
  onSuccess: () => void;
}

const StudentEdit: React.FC<StudentEditProps> = ({
  studentId,
  mode = 'edit',
  isOpen,
  onClose,
  onSuccess,
  ...props
}) => {
  if (!isOpen) return null;

  return (
    <AddStudentModal
      studentId={studentId}
      mode={mode === 'view' ? 'view' : 'edit'}
      onClose={onClose}
      onSuccess={onSuccess}
      {...props}
    />
  );
};

export default StudentEdit;
