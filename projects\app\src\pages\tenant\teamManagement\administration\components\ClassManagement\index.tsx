import { Box, Button, Flex } from '@chakra-ui/react';
import React, { useState, useEffect, useCallback } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Tree } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import {
  getSchoolDeptTree,
  getTeacherManageListByClazz,
  deleteSchoolDept
} from '@/api/tenant/teamManagement/administration';
import EditClassModal from '../EditClassModal';
import TeacherManagementModal from '../TeacherManagementModal';
import StudentManagementModal from '../StudentManagementModal';
import {
  DeptTreeResponse,
  ClazzTeacherManageListResponse
} from '@/types/api/tenant/teamManagement/administration';

// 模拟班级教师数据
interface TeacherInfo {
  subject: string;
  name: string;
}

interface ClassInfo {
  name: string;
  teachers: TeacherInfo[];
}

// 确保title属性可以接受React.ReactNode
interface CustomDataNode extends DataNode {
  title: React.ReactNode;
}

// 组件属性接口
interface ClassManagementProps {
  // 学期ID
  semesterId: string;
  // 自定义回调函数
  onEditClass?: (section: string, grade: string, classInfo: ClassInfo) => void;
  onManageTeachers?: (section: string, grade: string, classInfo: ClassInfo) => void;
  onManageStudents?: (section: string, grade: string, classInfo: ClassInfo) => void;
  // onDelete?: (section: string, grade: string, classInfo: ClassInfo) => void;
  // 刷新父组件数据的回调
  onRefresh?: () => void;
  // 树形数据变化回调
  onTreeDataChange?: (treeData: DeptTreeResponse | DeptTreeResponse[] | null) => void;
  // 刷新触发器
  refreshTrigger?: number;
  // 学期状态
  semesterStatus?: 0 | 1 | 2 | null;
}

const ClassManagement: React.FC<ClassManagementProps> = ({
  onEditClass,
  onManageTeachers,
  onManageStudents,
  semesterId,
  onRefresh,
  onTreeDataChange,
  refreshTrigger,
  semesterStatus
  // onDelete
}) => {
  // 编辑弹窗状态
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingClassData, setEditingClassData] = useState<{
    sectionKey: string;
    gradeName: string;
    classInfo: ClassInfo;
    classId?: string; // 添加班级ID
  } | null>(null);

  // 教师管理弹窗状态
  const [isTeacherModalOpen, setIsTeacherModalOpen] = useState(false);
  const [managingClassData, setManagingClassData] = useState<{
    classId: string;
    className: string;
  } | null>(null);

  // 学生管理弹窗状态
  const [isStudentModalOpen, setIsStudentModalOpen] = useState(false);
  const [managingStudentClassData, setManagingStudentClassData] = useState<{
    classId: string;
    className: string;
  } | null>(null);

  // 学校部门树数据状态
  const [schoolDeptTreeData, setSchoolDeptTreeData] = useState<DeptTreeResponse | null>(null);
  // 班级教师管理数据状态
  const [clazzTeacherManageData, setClazzTeacherManageData] =
    useState<ClazzTeacherManageListResponse | null>(null);
  const [loading, setLoading] = useState(false);

  // 树形组件展开状态管理
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  // 获取默认展开的节点keys，只展开学段节点
  const getDefaultExpandedKeys = (
    treeData: DeptTreeResponse | DeptTreeResponse[] | null
  ): string[] => {
    if (!treeData) {
      return [];
    }

    const expandedKeys: string[] = [];

    // 递归函数：只收集学段节点的keys
    const collectStageKeys = (
      node: DeptTreeResponse,
      level: number = 0,
      path: string = ''
    ): void => {
      const currentPath = path ? `${path} -> ${node.deptName}` : node.deptName;

      // 只展开学段节点（level === 0，即小学、初中、高中）
      if (level === 0) {
        expandedKeys.push(`dept-${node.id}`);
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          collectStageKeys(child, level + 1, currentPath);
        });
      }
    };

    // 如果是数组，处理所有顶级节点
    if (Array.isArray(treeData)) {
      treeData.forEach((node) => {
        collectStageKeys(node, 0);
      });
    } else {
      // 如果是单个对象，处理该对象（这是根节点）
      collectStageKeys(treeData, 0);
    }

    return expandedKeys;
  };

  // 获取学校部门树数据和教师管理列表
  const fetchData = useCallback(
    async (preserveExpandedState = false) => {
      // 检查 semesterId 是否存在
      if (!semesterId) {
        console.warn('semesterId 不存在，跳过数据获取');
        return;
      }

      try {
        setLoading(true);

        // 并发获取两个接口的数据
        const [deptTreeResponse, teacherManageResponse] = await Promise.all([
          getSchoolDeptTree(semesterId),
          getTeacherManageListByClazz(semesterId)
        ]);

        setSchoolDeptTreeData(deptTreeResponse);
        setClazzTeacherManageData(teacherManageResponse);
        console.log('学校部门树数据:', deptTreeResponse);
        console.log('班级教师管理列表:', teacherManageResponse);

        // 如果不需要保持展开状态，则设置默认展开状态
        if (!preserveExpandedState) {
          const defaultKeys = getDefaultExpandedKeys(deptTreeResponse);
          setExpandedKeys(defaultKeys);
        }

        // 通知父组件树形数据变化
        if (onTreeDataChange) {
          onTreeDataChange(deptTreeResponse);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        Toast.error('获取数据失败');
      } finally {
        setLoading(false);
      }
    },
    [semesterId, onTreeDataChange]
  );

  // 初始化和semesterId变化时获取数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 响应外部刷新触发器
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      fetchData(true); // 保持展开状态
    }
  }, [refreshTrigger, fetchData]);

  const onDelete = (id: string) => {
    MessageBox.confirm({
      title: '删除',
      content: '请确认是否删除该班级？删除前请确保该班级内已无学生',
      onOk: async () => {
        try {
          await deleteSchoolDept({ id });
          Toast.success('删除成功');
          // 刷新树数据，保持展开状态
          await fetchData(true);
        } catch (error) {
          console.error('删除班级失败:', error);
          Toast.error('删除失败');
        }
      }
    });
  };

  // 处理教师管理
  const handleManageTeachers = (classNode: DeptTreeResponse) => {
    setManagingClassData({
      classId: classNode.id,
      className: classNode.deptName
    });
    setIsTeacherModalOpen(true);
  };

  // 处理学生管理
  const handleManageStudents = (classNode: DeptTreeResponse) => {
    setManagingStudentClassData({
      classId: classNode.id,
      className: classNode.deptName
    });
    setIsStudentModalOpen(true);
  };

  // 根据 schoolDeptTreeData 构建树形数据
  const buildTreeDataFromAPI = (): CustomDataNode[] => {
    if (!schoolDeptTreeData) {
      return [];
    }

    // 递归处理树节点
    const processNode = (node: DeptTreeResponse): CustomDataNode => {
      const hasChildren = node.children && node.children.length > 0;

      // 如果是叶子节点（班级），渲染班级信息
      if (!hasChildren) {
        // 获取该班级的教师信息
        const getTeacherInfo = (classId: string) => {
          if (!clazzTeacherManageData) return '';

          // 查找对应班级的教师数据
          const classData = clazzTeacherManageData.find(
            (item) => item.deptId.toString() === classId
          );

          if (!classData || !classData.clazzTeacherRespList) return '';

          const teacherInfoList: string[] = [];

          // 遍历教师配置列表
          classData.clazzTeacherRespList.forEach((teacherConfig) => {
            const teacherNames = teacherConfig.tmbUserList.map((user) => user.userName).join('、');

            if (teacherConfig.teachTypeName === '班主任') {
              teacherInfoList.push(`班主任(${teacherNames})`);
            } else if (teacherConfig.teachTypeName === '学科教师' && teacherConfig.subjectName) {
              teacherInfoList.push(`${teacherConfig.subjectName}(${teacherNames})`);
            }
          });

          return teacherInfoList.join(', ');
        };

        const teacherInfo = getTeacherInfo(node.id);

        return {
          title: (
            <Flex justifyContent="space-between" alignItems="center" width="100%">
              <Flex alignItems="center" width="100%" height="54px">
                <Box
                  color="rgba(0, 0, 0, 0.90)"
                  fontSize="14px"
                  fontWeight="400"
                  lineHeight="22px"
                  mr={4}
                >
                  {node.deptName}
                </Box>
                {/* 渲染教师信息 */}
                {teacherInfo && (
                  <Box
                    color="rgba(0, 0, 0, 0.65)"
                    fontSize="12px"
                    fontWeight="400"
                    lineHeight="20px"
                  >
                    {teacherInfo}
                  </Box>
                )}
              </Flex>
              <Box position="absolute" right="16px">
                {renderActionButtons(node)}
              </Box>
            </Flex>
          ),
          key: `dept-${node.id}`,
          isLeaf: true
        };
      }

      // 如果有子节点，递归处理
      return {
        title: node.deptName,
        key: `dept-${node.id}`,
        children: node.children.map((child) => processNode(child))
      };
    };

    // 如果 schoolDeptTreeData 是数组
    if (Array.isArray(schoolDeptTreeData)) {
      return schoolDeptTreeData.map((node) => processNode(node));
    }

    // 如果 schoolDeptTreeData 是单个对象
    return [processNode(schoolDeptTreeData)];
  };

  // 渲染操作按钮组件
  const renderActionButtons = (node: DeptTreeResponse) => (
    <Flex gap="8px" alignItems="center">
      <Button
        width="67px"
        height="30px"
        padding="3px"
        bg="white"
        borderRadius="7px"
        border="1px solid #7D4DFF"
        color="#7D4DFF"
        fontSize="14px"
        fontWeight="500"
        lineHeight="22px"
        variant="outline"
        onClick={() => handleEditClass(node)}
        isDisabled={semesterStatus !== 1} // 只有进行中的学期才能编辑
      >
        编辑信息
      </Button>
      <Button
        width="67px"
        height="30px"
        padding="3px"
        bg="white"
        borderRadius="7px"
        border="1px solid #7D4DFF"
        color="#7D4DFF"
        fontSize="14px"
        fontWeight="500"
        lineHeight="22px"
        variant="outline"
        onClick={() => handleManageTeachers(node)}
        isDisabled={semesterStatus !== 1} // 只有进行中的学期才能管理教师
      >
        教师管理
      </Button>
      <Button
        width="67px"
        height="30px"
        padding="3px"
        bg="white"
        borderRadius="7px"
        border="1px solid #7D4DFF"
        color="#7D4DFF"
        fontSize="14px"
        fontWeight="500"
        lineHeight="22px"
        variant="outline"
        onClick={() => handleManageStudents(node)}
        isDisabled={semesterStatus !== 1} // 只有进行中的学期才能管理学生
      >
        学生管理
      </Button>
      <Button
        width="67px"
        height="30px"
        padding="3px"
        bg="white"
        borderRadius="7px"
        border="1px solid #D1D5DB"
        color="#4E5969"
        fontSize="14px"
        fontWeight="500"
        lineHeight="22px"
        variant="outline"
        onClick={() => onDelete(node.id)}
        isDisabled={semesterStatus !== 1} // 只有进行中的学期才能删除
      >
        删除
      </Button>
    </Flex>
  );

  // 处理编辑班级（API数据）
  const handleEditClass = (classNode: DeptTreeResponse) => {
    setEditingClassData({
      sectionKey: '', // 可以根据需要设置
      gradeName: classNode.parentName || '',
      classInfo: {
        name: classNode.deptName,
        teachers: classNode.teachers || []
      },
      classId: classNode.id // 添加班级ID
    });
    setIsEditModalOpen(true);
  };

  // 构建树形数据
  const buildTreeData = (): CustomDataNode[] => {
    return buildTreeDataFromAPI();
  };

  // 处理树形组件展开/收起事件
  const handleExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue as string[]);
  };

  return (
    <>
      <Box borderRadius="3px" border="1px solid #DCDCDC" overflow="hidden">
        {loading ? (
          <Box p={4} textAlign="center">
            加载中...
          </Box>
        ) : (
          <Tree
            switcherIcon={() => <DownOutlined />}
            expandedKeys={expandedKeys}
            onExpand={handleExpand}
            treeData={buildTreeData()}
            blockNode={true}
            className="custom-tree"
            style={{ width: '100%' }}
          />
        )}
        <style jsx global>{`
          .custom-tree .ant-tree-treenode {
            padding: 0 !important;
            width: 100%;
          }

          .custom-tree .ant-tree-node-content-wrapper {
            flex: 1;
            padding: 0 !important;
          }

          /* 折叠按钮 */
          .custom-tree .ant-tree-switcher {
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #f2f3f5 !important;
            border-top: 1px solid #f2f3f5 !important;
            border-left: 1px solid #f2f3f5 !important;
            border-radius: 0 !important;
          }

          /* 禁用折叠按钮悬停背景色 */
          .custom-tree .ant-tree-switcher:hover {
            background: transparent !important;
          }

          /* 禁用内容区域悬停背景色 */
          .custom-tree .ant-tree-node-content-wrapper:hover {
            background: transparent !important;
          }

          .custom-tree .ant-tree-treenode-selected::before,
          .custom-tree .ant-tree-treenode:hover::before {
            background: transparent !important;
          }

          .custom-tree .ant-tree-node-selected {
            background: transparent !important;
          }

          .custom-tree
            .ant-tree-treenode:not(.ant-tree-treenode-leaf)
            > .ant-tree-node-content-wrapper {
            border-bottom: 1px solid #f2f3f5 !important;
            border-top: 1px solid #f2f3f5 !important;
            height: 46px;
            display: flex;
            align-items: center;
            padding-left: 16px !important;
            border-radius: 0 !important;
          }

          /* 只为顶层父节点（小学、初中、高中）设置背景颜色 */
          .custom-tree .ant-tree-treenode:not(:has(.ant-tree-indent-unit)) {
            background-color: #f8fafc !important;
          }

          .custom-tree .ant-tree-treenode-leaf > .ant-tree-node-content-wrapper {
            border-bottom: 1px solid #e7e7e7;
            padding: 0 !important;
            width: 100%;
            height: 54px;
          }

          .custom-tree .ant-tree-treenode-leaf > .ant-tree-node-content-wrapper:hover {
            background: transparent;
          }
        `}</style>
      </Box>

      {/* 编辑班级信息弹窗 */}
      <EditClassModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingClassData(null);
        }}
        classId={editingClassData?.classId}
        initialData={
          editingClassData
            ? {
                schoolSection: editingClassData.sectionKey,
                grade: editingClassData.gradeName,
                className: editingClassData.classInfo.name
              }
            : undefined
        }
        onRefresh={() => {
          fetchData(true);
        }}
      />

      {/* 教师管理弹窗 */}
      <TeacherManagementModal
        isOpen={isTeacherModalOpen}
        onClose={() => {
          setIsTeacherModalOpen(false);
          setManagingClassData(null);
        }}
        onRefresh={() => {
          fetchData(true);
        }}
        classId={managingClassData?.classId}
        semesterId={semesterId}
        className={managingClassData?.className}
      />

      {/* 学生管理弹窗 */}
      <StudentManagementModal
        isOpen={isStudentModalOpen}
        onClose={() => {
          setIsStudentModalOpen(false);
          setManagingStudentClassData(null);
        }}
        onRefresh={() => {
          fetchData(true);
        }}
        classId={managingStudentClassData?.classId}
        className={managingStudentClassData?.className}
        semesterId={semesterId}
      />
    </>
  );
};

export default ClassManagement;
