import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  Select,
  Textarea
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { getTenantDetail, updateTenantDetail, getIndustryList } from '@/api/tenant';
import PageContainer from '@/components/PageContainer';
import UploadImage from '@/components/UploadImage';
import { useTenantStore } from '@/store/useTenantStore';
import { serviceSideProps } from '@/utils/i18n';

interface FormData {
  name: string;
  industry: number;
  domain: string;
  fullName?: string;
  avatar?: string;
  avatarUrl?: string;
  fullNameImg?: string;
  fullNameImgUrl?: string;
  backgroundImg?: string;
  backgroundImgUrl?: string;
  sidebarImg?: string;
  sidebarImgUrl?: string;
}

const TenantInfo = ({
  tenantId
}: {
  tenantId: string;
} & BoxProps) => {
  interface IndustryListType {
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    name: string;
    code: number;
  }
  const [currentTenantId, setCurrentTenantId] = useState(tenantId);

  const [industryList, setIndustryList] = useState<IndustryListType[]>([]);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { loadTenant } = useTenantStore(); // 获取 loadTenant 方法

  useEffect(() => {
    // 获取行业列表数据
    const fetchIndustryList = async () => {
      try {
        const res = await getIndustryList();
        setIndustryList(res);
      } catch (error) {}
    };

    fetchIndustryList();
  }, []);

  const industryOptions = useMemo(() => {
    return industryList.map((option) => ({
      label: option.name, // 假设每个选项都有一个 label 属性
      value: option.code
    }));
  }, [industryList]);

  const [industry, setIndustry] = useState<number>(getValues('industry'));

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: FormData) => {
      return updateTenantDetail({ id: currentTenantId, ...data });
    },
    onSuccess(res) {
      loadTenant(true); // 强制重新加载租户数据
    },
    successToast: '更新成功'
  });

  useEffect(() => {
    getTenantDetail().then((res) => {
      setValue('name', res.name);
      setValue('fullName', res.fullName);
      setValue('domain', res.domain);
      setValue('backgroundImg', res.backgroundImg);
      setValue('backgroundImgUrl', res.backgroundImgUrl);
      setValue('sidebarImg', res.sidebarImg);
      setValue('sidebarImgUrl', res.sidebarImgUrl);
      setValue('fullNameImg', res.fullNameImg);
      setValue('fullNameImgUrl', res.fullNameImgUrl);
      setValue('avatar', res.avatar);
      setValue('avatarUrl', res.avatarUrl);
      setIndustry(res.industry);
      setValue('industry', res.industry);
      setCurrentTenantId(res.id); // 使用局部状态存储 tenantId
    });
  }, [tenantId, setValue]);

  useEffect(() => {
    setValue('industry', industry);
  }, [industry, setValue]);

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
  };

  return (
    <PageContainer p="24px">
      <Box mr="calc(50% - 222px)">
        <FormLabel color="#303313" fontSize="16px">
          组织信息
        </FormLabel>
        <FormControl isInvalid={!!errors.name}>
          <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
            <FormLabel color="#4E5969" fontSize="14px">
              <Box
                _before={{
                  content: '"*"',
                  color: '#F53F3F'
                }}
              >
                组织简称
              </Box>
            </FormLabel>
            <Flex flexDirection="column">
              <Input
                borderRadius="4px"
                w="440px"
                {...register('name', {
                  required: '请输入组织简称',
                  minLength: {
                    value: 2,
                    message: '至少输入2个字符'
                  },
                  maxLength: {
                    value: 20,
                    message: '最多输入不超过20个字符'
                  }
                })}
                h="36px"
                bg="#fff"
                placeholder="请输入组织简称"
              />
              {errors.name && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.name.message}
                </Box>
              )}
            </Flex>
          </Flex>
        </FormControl>
        <FormControl mt="24px" isInvalid={!!errors.fullName}>
          <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
            <FormLabel color="#4E5969" fontSize="14px">
              <Box
                _before={{
                  content: '"*"',
                  color: '#F53F3F'
                }}
              >
                组织全称
              </Box>
            </FormLabel>
            <Flex flexDirection="column">
              <Textarea
                borderRadius="4px"
                w="440px"
                {...register('fullName', {
                  required: '请输入组织全称',
                  validate: {
                    minLength: (value: string | undefined) => {
                      if (value === undefined) return '请输入组织全称';
                      const noSpacesValue = value.replace(/\s+/g, '');
                      return noSpacesValue.length >= 2 || '至少输入2个字符';
                    },
                    maxLength: (value: string | undefined) => {
                      if (value === undefined) return '请输入组织全称';
                      const noSpacesValue = value.replace(/\s+/g, '');
                      return noSpacesValue.length <= 30 || '最多输入不超过30个字符';
                    }
                  }
                })}
                h="36px"
                bg="#fff"
                placeholder="请输入组织全称"
              />
              {errors.fullName && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.fullName.message}
                </Box>
              )}
            </Flex>
          </Flex>
        </FormControl>
        <FormControl mt="24px">
          <Flex
            alignItems="center"
            whiteSpace="nowrap"
            justifyContent="end"
            css={{
              '& .ant-select-selector': {
                borderRadius: '2px'
              }
            }}
          >
            <FormLabel color="#4E5969" fontSize="14px">
              <Box
                _before={{
                  content: '"*"',
                  color: '#F53F3F'
                }}
              >
                所属类型
              </Box>
            </FormLabel>
            <Select
              borderRadius="4px"
              w="440px"
              h="36px"
              bg="#fff"
              disabled
              onChange={(e) => setIndustry(Number(e.target.value))}
              value={industry}
              placeholder="请选择所属类型"
            >
              {industryOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          </Flex>
        </FormControl>
        <FormControl mt="24px" isInvalid={!!errors.domain}>
          <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
            <FormLabel color="#4E5969" fontSize="14px">
              <Box
                _before={{
                  content: '"*"',
                  color: '#F53F3F'
                }}
              >
                域名
              </Box>
            </FormLabel>

            <Flex flexDirection="column">
              <Input
                disabled
                borderRadius="4px"
                w="440px"
                {...register('domain', { required: '请输入域名' })}
                placeholder="请输入域名"
                h="36px"
                bg="#fff"
              />
              {errors.domain && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.domain.message}
                </Box>
              )}
            </Flex>
          </Flex>
        </FormControl>
        <FormControl mt="24px" display="flex" justifyContent="end">
          <FormLabel color="#4E5969" fontSize="14px">
            <Box>图片上传</Box>
          </FormLabel>

          <Flex w="440px" ml="20px">
            <Box w="110px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  徽标
                </Box>
              </FormLabel>

              <UploadImage
                isPublic={true}
                imageUrl={getValues('avatarUrl')}
                onImageSelect={(fileKey, fileUrl) => handleImageSelect('avatar', fileKey, fileUrl)}
                maxWidthOrHeight={300}
                showPlaceholderAsBox={true}
                placeholder="选择徽标"
                {...register('avatar', { required: '请选择徽标' })}
              />
              {errors.avatar && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.avatar.message}
                </Box>
              )}
            </Box>
            <Box w="110px">
              <FormLabel color="#4E5969" fontSize="14px">
                全称图片
              </FormLabel>
              <UploadImage
                isPublic={true}
                imageUrl={getValues('fullNameImgUrl')}
                onImageSelect={(fileKey, fileUrl) =>
                  handleImageSelect('fullNameImg', fileKey, fileUrl)
                }
                showPlaceholderAsBox={true}
                maxSizeMB={5}
                placeholder="选择图片"
              />
            </Box>
            <Box w="110px">
              <FormLabel color="#4E5969" fontSize="14px">
                登录封面
              </FormLabel>
              <UploadImage
                isPublic={true}
                imageUrl={getValues('sidebarImgUrl')}
                onImageSelect={(fileKey, fileUrl) =>
                  handleImageSelect('sidebarImg', fileKey, fileUrl)
                }
                maxSizeMB={5}
                showPlaceholderAsBox={true}
                placeholder="选择封面"
              />
            </Box>
            <Box w="110px">
              <FormLabel color="#4E5969" fontSize="14px">
                登录背景
              </FormLabel>
              <UploadImage
                imageUrl={getValues('backgroundImgUrl')}
                onImageSelect={(fileKey, fileUrl) =>
                  handleImageSelect('backgroundImg', fileKey, fileUrl)
                }
                isPublic={true}
                showPlaceholderAsBox={true}
                maxSizeMB={5}
                placeholder="选择背景"
              />
            </Box>
          </Flex>
        </FormControl>
        <FormControl mt="36px">
          <Flex justifyContent="end">
            <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
            <Flex w="440px" justifyContent="end">
              <Button
                h="36px"
                ml="24px"
                borderRadius="8px"
                onClick={handleSubmit(onSubmit as any)}
                isLoading={isSubmiting}
              >
                确定
              </Button>
            </Flex>
          </Flex>
        </FormControl>
      </Box>
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default TenantInfo;
