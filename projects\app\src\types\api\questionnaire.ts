// 问卷状态枚举
export enum QuestionnaireStatus {
  Submit = 1, // 已提交
  UnSubmit = 0 // 未提交
}

// 题型枚举
export enum QuestionType {
  SingleChoice = 1, // 单选题
  MultipleChoice = 2, // 多选题
  TextInput = 3 // 填空题
}

// 计分规则枚举
export enum ScoreRule {
  NoScore = 0, // 不计分
  ByOption = 1, // 按选项
  ByTime = 2 // 按时间
}

// 是否必填枚举
export enum IsRequired {
  No = 0, // 否
  Yes = 1 // 是
}

// 是否为文本输入项枚举
export enum IsTextInput {
  No = 0, // 否
  Yes = 1 // 是
}

export enum QuestionAnswersStatus {
  UnSubmit = 0, // 未提交
  Submit = 1 // 已提交
}

// 选项接口
export interface QuestionOption {
  id: string; // 选项ID
  questionnaireId: string; // 问卷ID
  questionId: string; // 题目ID
  content: string; // 选项内容
  score: number; // 分数
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  isTextInput: IsTextInput; // 是否为文本输入项 0-否 1-是
  nextQuestionId?: number; // 下一题目ID（可选）
}

// 题目接口
export interface Question {
  id: string; // 题目ID
  questionnaireId: string; // 问卷ID
  questionType: QuestionType; // 题型 1-单选题 2-多选题 3-填空题
  isRequired: IsRequired; // 是否必填 0-否 1-是
  content: string; // 问题内容
  sortOrder: number; // 排序
  options: QuestionOption[]; // 选项列表
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  maxOptionCount: number; // 最多选项数
  scoreRule: ScoreRule; // 计分规则 0-不计分 1-按选项 2-按时间
}

// 问卷接口
export interface Questionnaire {
  id: string; // 问卷ID
  name: string; // 问卷名称
  startDescription: string; // 问卷开头说明
  endDescription: string; // 问卷结尾说明
  questionCount: number; // 问题数
  startTime: string; // 问卷开始时间
  endTime: string; // 问卷结束时间
  schoolCode: string; // 学校code
  schoolName: string; // 学校名称
  status: QuestionnaireStatus; // 状态：1-待发布 2-已发布 3-进行中 4-已结束
  questions: Question[]; // 题目列表
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// API 请求参数接口
export interface GetQuestionnaireByCodeParams {
  schoolCode: string; // 学校code
}

// 答案提交状态枚举
export enum AnswerStatus {
  NotSubmitted = 0, // 未提交
  Submitted = 1 // 已提交
}

// 选择选项接口（用于提交答案）
export interface SelectedOption {
  optionId: number; // 选项ID
  textInput: string; // 文本输入
}

// 提交答案参数接口（填空题）
export interface SubmitTextAnswerParams {
  questionnaireId: number; // 问卷ID
  questionId: number; // 题目ID
  sessionId: string; // 用户会话标识，用于识别同一用户
  selectedOptions: string[]; // 选择的选项（填空题为空数组）
  textAnswer: string; // 答案，填空题必填
  answerTime: number; // 答题时间(秒)
  status?: QuestionAnswersStatus;
}

// 提交答案参数接口（选择题）
export interface SubmitChoiceAnswerParams {
  questionnaireId: number; // 问卷ID
  questionId: number; // 题目ID
  sessionId: string; // 用户会话标识，用于识别同一用户
  selectedOptions: SelectedOption[]; // 选择的选项
  textAnswer: string; // 答案，填空题必填
  answerTime: number; // 答题时间(秒)
  status?: QuestionAnswersStatus;
}

// 提交答案响应接口
export interface SubmitAnswerResponse {
  success: boolean; // 是否提交成功
  remainingQuestions: number; // 剩余未答题目数量
}

// 查询用户问卷结果参数接口
export interface GetUserQuestionnaireResultParams {
  questionnaireId: number; // 问卷ID
  sessionId: string; // 用户会话标识，用于识别同一用户
}

// 用户答题选项详情
export interface UserSelectedOption {
  optionId: number; // 选项ID
  optionContent: string; // 选项内容
  textInput: string; // 文本输入
}

// 用户答题详情
export interface UserQuestionAnswer {
  questionId: number; // 题目ID
  questionContent: string; // 题目内容
  questionType: QuestionType; // 题目类型 1-单选题 2-多选题 3-填空题
  selectedOptions: UserSelectedOption[]; // 选择的选项
  textAnswer: string; // 填空题答案
  answerTime: number; // 答题时间(秒)
  status: QuestionAnswersStatus;
}

// 用户问卷结果
export interface UserQuestionnaireResult {
  questionnaireId: number; // 问卷ID
  questionnaireName: string; // 问卷名称
  ip: string; // 用户IP
  sessionId: string; // 用户会话标识，用于识别同一用户
  startTime: string; // 答卷开始时间
  endTime: string; // 答卷结束时间
  status: AnswerStatus; // 状态 0-未提交 1-已提交
  questionAnswers: UserQuestionAnswer[]; // 答题详情
}

// 创建问卷参数
export interface CreateQuestionnaireParams {
  name: string;
  startDescription: string;
  endDescription: string;
  startTime: string;
  endTime: string;
  schoolCode: string;
  questions: Omit<Question, 'id' | 'questionnaireId' | 'createTime' | 'updateTime'>[];
}

// 更新问卷参数
export interface UpdateQuestionnaireParams {
  id: string;
  name?: string;
  startDescription?: string;
  endDescription?: string;
  startTime?: string;
  endTime?: string;
  status?: QuestionnaireStatus;
}

// 删除问卷参数
export interface DeleteQuestionnaireParams {
  id: string;
}

// 问卷列表查询参数
export interface ListQuestionnairesParams {
  schoolCode?: string;
  status?: QuestionnaireStatus;
  page?: number;
  pageSize?: number;
}

// 问卷列表响应
export interface ListQuestionnairesResponse {
  code: number;
  success: boolean;
  data: {
    list: Questionnaire[];
    total: number;
    page: number;
    pageSize: number;
  };
  msg: string;
}
