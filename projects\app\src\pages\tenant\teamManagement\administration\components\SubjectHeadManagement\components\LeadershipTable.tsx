import React from 'react';
import { Box } from '@chakra-ui/react';
import { Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import { vwDims } from '@/utils/chakra';
import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import { LeadershipRowData, HoveredCellState } from '../types';
import { TABLE_CONFIG } from '../constants';
import TeacherCell from './TeacherCell';

interface LeadershipTableProps {
  subjects: string[];
  leaders: Record<string, TmbUser[]>;
  treeData: Department[];
  hoveredCell: HoveredCellState | null;
  highlightedTmbIds: number[];
  onCellHover: (cellData: HoveredCellState | null) => void;
  onEditTeacher: (
    nodeId: string | null,
    grade: string,
    subject: string,
    role?: 'leader' | 'phase_leader' | 'teacher'
  ) => void;
}

/**
 * 总负责人表格组件
 */
const LeadershipTable: React.FC<LeadershipTableProps> = ({
  subjects,
  leaders,
  treeData,
  hoveredCell,
  highlightedTmbIds,
  onCellHover,
  onEditTeacher
}) => {
  // 创建负责人表格数据
  const leadershipData: LeadershipRowData[] = [{ key: 'headmaster', role: '负责人' }];

  // 创建表格列定义
  const leadershipColumns: ColumnType<LeadershipRowData>[] = [
    {
      title: <div style={{ textAlign: 'center' }}>学科</div>,
      dataIndex: 'role',
      key: 'role',
      width: vwDims(TABLE_CONFIG.FIXED_COLUMN_WIDTH),
      fixed: 'left',
      align: 'center' as const,
      onCell: () => ({
        style: {
          backgroundColor: '#f9f9f9'
        }
      }),
      render: (role) => (
        <Box display="flex" alignItems="center" justifyContent="center">
          {role}
        </Box>
      )
    },
    // 使用subjects数组，该数组保持了API返回的原始顺序
    ...subjects.map((subject) => ({
      title: <div style={{ textAlign: 'center' }}>{subject}</div>,
      dataIndex: subject,
      key: subject,
      width: TABLE_CONFIG.COLUMN_WIDTH,
      align: 'center' as const,
      onCell: () => {
        const cellId = `school-${subject}`;
        const teacherInfo = leaders[subject] || [];

        return {
          onMouseEnter: () => onCellHover({ id: cellId, users: teacherInfo }),
          onMouseLeave: () => onCellHover(null),
          onClick: () => {
            // 找到对应科目的节点，确保传递正确的ID
            const subjectNode = treeData.find((item) => item.subjectName === subject);
            const nodeId = subjectNode ? subjectNode.id || subjectNode.deptId : null;
            console.log(`点击总负责人[${subject}]单元格, 找到ID:`, nodeId);
            onEditTeacher(nodeId, '全校', subject, 'leader');
          },
          style: { cursor: 'pointer' }
        };
      },
      render: (_: unknown, _record: LeadershipRowData) => {
        const cellId = `school-${subject}`;
        // 使用leaders中的数据来获取总负责人，与科目严格一一对应
        const teacherInfo = leaders[subject] || [];
        const canEdit = true; // 总学科负责人对任何学科都可以设置

        return (
          <TeacherCell
            cellId={cellId}
            teacherInfo={teacherInfo}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            canEdit={canEdit}
          />
        );
      }
    }))
  ];

  return (
    <Table
      title={() => '总负责人'}
      columns={leadershipColumns}
      dataSource={leadershipData}
      pagination={false}
      bordered
      scroll={{ x: 'max-content' }}
      rowKey="key"
      rowClassName="white-background-row"
      style={{ marginBottom: 16 }}
    />
  );
};

export default LeadershipTable;
