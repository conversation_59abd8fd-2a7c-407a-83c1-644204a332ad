/** 通用账户信息类型定义 */

/** 账户类型枚举 */
export enum AccountType {
  /** 未绑定 */
  UNBOUND = 0,
  /** 学生 */
  STUDENT = 1,
  /** 教师 */
  TEACHER = 2
}

/** 通用账户信息 */
export interface CommonAccountItem {
  /** tmbId（创建用户入参用这个） */
  id: string;
  /** 用户名 */
  username: string;
  /** 账号 */
  account: string;
  /** 账号类型：0-未绑定，1-学生，2-老师 */
  type: AccountType;
}

/** 通用账户列表响应 */
export interface CommonAccountListResponse {
  data: CommonAccountItem[];
}

/** 根据类型筛选账户的请求参数 */
export interface FilterAccountByTypeParams {
  /** 账户类型：0-未绑定，1-学生，2-老师 */
  type?: AccountType;
}

/** 获取未绑定账户列表 */
export interface UnboundAccountItem extends CommonAccountItem {
  type: AccountType.UNBOUND;
}

/** 获取学生账户列表 */
export interface StudentAccountItem extends CommonAccountItem {
  type: AccountType.STUDENT;
}

/** 获取教师账户列表 */
export interface TeacherAccountItem extends CommonAccountItem {
  type: AccountType.TEACHER;
}
