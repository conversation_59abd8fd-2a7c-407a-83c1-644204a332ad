import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Text,
  IconButton,
  Divider,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton
} from '@chakra-ui/react';
import { useState, useEffect, useRef } from 'react';
import { DeleteIcon } from '@chakra-ui/icons';
import {
  getAccountList,
  getTenantSubjectList,
  getCurrentSemester,
  getSchoolDeptTree,
  updateTeacher,
  getTeacherDetail
} from '@/api/teacher';
import type {
  TeacherPageItem,
  TeacherAccountItem,
  TenantSubjectItem,
  SchoolDeptTreeItem,
  TeacherUpdateParams
} from '@/types/api/teacher';
import {
  SCHOOL_MANAGE_TEACHER_TYPE_MAP,
  SCHOOL_SUBJECT_TEACHER_TYPE_MAP,
  SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT,
  SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT
} from '@/constants/teacher';
import { extractSubjectListFromResponse } from '@/utils/subjectUtils';

interface TeachersEditInformationProps {
  teacher?: TeacherPageItem;
  onSuccess?: () => void;
  isOpen?: boolean;
  onClose?: () => void;
}

const GENDER_OPTIONS = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
];

// 定义类型，包含 filteredClassTree
interface AdminJob {
  teachType: string;
  deptId: string;
  disabled: boolean;
  filteredClassTree: SchoolDeptTreeItem[];
}
interface TeachJob {
  teachType: string;
  deptId: string;
  subjectId: string;
  disabled: boolean;
  filteredClassTree: SchoolDeptTreeItem[];
}

const TeachersEditInformation = ({
  teacher,
  onSuccess,
  isOpen = true,
  onClose = () => {}
}: TeachersEditInformationProps) => {
  const toast = useToast();
  const [accountList, setAccountList] = useState<TeacherAccountItem[]>([]);
  const [subjectList, setSubjectList] = useState<TenantSubjectItem[]>([]);
  const [classTree, setClassTree] = useState<SchoolDeptTreeItem[]>([]);
  const [semesterId, setSemesterId] = useState<string | number>('');
  const [form, setForm] = useState({
    id: '',
    name: '',
    tmbId: '',
    phone: '',
    gender: 1
  });
  const [adminJobs, setAdminJobs] = useState<AdminJob[]>([
    { teachType: '', deptId: '', disabled: false, filteredClassTree: [] }
  ]);
  const [teachJobs, setTeachJobs] = useState<TeachJob[]>([
    {
      teachType: '',
      deptId: '',
      subjectId: '',
      disabled: false,
      filteredClassTree: []
    }
  ]);
  const [loading, setLoading] = useState(false);
  const keyCounterRef = useRef(0);

  // 拉取教师详情并初始化
  useEffect(() => {
    if (teacher?.id) {
      console.log('开始获取教师详情，teacher:', teacher);
      getTeacherDetail({ id: teacher.id }).then((res: any) => {
        console.log('获取到的教师详情:', res);
        
        // 初始化行政职务，需要等待班级树加载完成后设置filteredClassTree
        const initialAdminJobs = res.schoolManageTeachersDetail && res.schoolManageTeachersDetail.length > 0
          ? res.schoolManageTeachersDetail.map((role: any) => ({
              teachType: role.teachType ? String(role.teachType) : '',
              deptId: role.deptId ? String(role.deptId) : '',
              disabled: false,
              filteredClassTree: [] // 暂时为空，后续会更新
            }))
          : []; // 如果没有职务，不创建空项
        
        // 初始化教学职务，需要等待班级树加载完成后设置filteredClassTree
        const initialTeachJobs = res.schoolSubjectTeachersDetail && res.schoolSubjectTeachersDetail.length > 0
          ? res.schoolSubjectTeachersDetail.map((role: any) => ({
              teachType: role.teachType ? String(role.teachType) : '',
              deptId: role.deptId ? String(role.deptId) : '',
              subjectId: role.subjectId ? String(role.subjectId) : '',
              disabled: false,
              filteredClassTree: [] // 暂时为空，后续会更新
            }))
          : []; // 如果没有职务，不创建空项
        
        console.log('初始化行政职务:', initialAdminJobs);
        console.log('初始化教学职务:', initialTeachJobs);
        
        setAdminJobs(initialAdminJobs);
        setTeachJobs(initialTeachJobs);
        setForm({
          id: res.id || '',
          name: res.name || '',
          tmbId: res.tmbId || '',
          phone: res.phone || '',
          gender: res.gender || 1
        });
        
        console.log('教师详情数据加载完成');
      }).catch((error) => {
        console.error('获取教师详情失败:', error);
      });
    }
  }, [teacher]);

  // 当班级树加载完成后，为现有职务设置filteredClassTree
  useEffect(() => {
    if (classTree.length > 0 && (adminJobs.length > 0 || teachJobs.length > 0)) {
      console.log('班级树已加载，开始为现有职务设置filteredClassTree');
      
      // 检查是否需要更新
      const needsUpdate = adminJobs.some(job => job.teachType && job.teachType !== '' && job.filteredClassTree.length === 0) ||
                         teachJobs.some(job => job.teachType && job.teachType !== '' && job.filteredClassTree.length === 0);
      
      if (needsUpdate) {
        // 更新行政职务的filteredClassTree
        const updatedAdminJobs = adminJobs.map((job, index) => {
          if (job.teachType && job.teachType !== '' && job.filteredClassTree.length === 0) {
            const filteredTree = getFilteredClassTree(classTree, parseInt(job.teachType), true);
            console.log(`行政职务 ${index} teachType: ${job.teachType}, filteredTree:`, filteredTree);
            return {
              ...job,
              filteredClassTree: filteredTree
            };
          }
          return job;
        });
        
        // 更新教学职务的filteredClassTree
        const updatedTeachJobs = teachJobs.map((job, index) => {
          if (job.teachType && job.teachType !== '' && job.filteredClassTree.length === 0) {
            const filteredTree = getFilteredClassTree(classTree, parseInt(job.teachType), false);
            console.log(`教学职务 ${index} teachType: ${job.teachType}, filteredTree:`, filteredTree);
            return {
              ...job,
              filteredClassTree: filteredTree
            };
          }
          return job;
        });
        
        setAdminJobs(updatedAdminJobs);
        setTeachJobs(updatedTeachJobs);
      }
    }
  }, [classTree, adminJobs, teachJobs]);

  useEffect(() => {
    getAccountList().then((res) => {
      setAccountList(res || []);
    });
    getTenantSubjectList().then((res) => {
      const list = extractSubjectListFromResponse(res);
      setSubjectList(list);
    });
    getCurrentSemester().then((res) => {
      console.log('当前学期:', res);
      if (res && res.id) {
        setSemesterId(res.id);
        getSchoolDeptTree({ semesterId: res.id }).then((treeRes) => {
          console.log('getSchoolDeptTree 返回:', treeRes);
          if (!treeRes) {
            console.error('班级树接口返回为空');
            setClassTree([]);
            return;
          }
          
          // 处理不同的返回格式
          let treeData: SchoolDeptTreeItem[] = [];
          if (Array.isArray(treeRes)) {
            treeData = treeRes;
          } else if (treeRes.data && Array.isArray(treeRes.data)) {
            treeData = treeRes.data;
          } else {
            console.error('班级树接口返回格式无效，treeRes:', treeRes);
            setClassTree([]);
            return;
          }
          
          // 处理班级树数据，确保 deptName 字段有合适的值
          const processedTreeData = processClassTreeData(treeData);
          console.log('设置班级树数据:', processedTreeData);
          console.log('班级树数据结构示例:', processedTreeData[0]);
          setClassTree(processedTreeData);
        }).catch((error) => {
          console.error('获取班级树失败:', error);
          setClassTree([]);
        });
      } else {
        console.warn('当前学期信息为空，无法获取班级树');
        setClassTree([]);
      }
    });
  }, []);

  // 处理班级树数据，确保 deptName 字段有合适的值
  const processClassTreeData = (tree: SchoolDeptTreeItem[]): SchoolDeptTreeItem[] => {
    return tree.map(node => {
      const processedNode = {
        ...node,
        deptName: (node as any).deptName || node.name || `节点${node.id}`,
        children: node.children ? processClassTreeData(node.children) : undefined
      };
      console.log(`处理节点: id=${node.id}, name=${node.name}, deptName=${(node as any).deptName} -> ${processedNode.deptName}`);
      return processedNode;
    });
  };

  // 递归获取所有叶子节点（班级）
  function getAllLeafNodes(tree: SchoolDeptTreeItem[]): SchoolDeptTreeItem[] {
    let result: SchoolDeptTreeItem[] = [];
    for (const node of tree) {
      if (!node.children || node.children.length === 0) {
        result.push(node);
      } else {
        result = result.concat(getAllLeafNodes(node.children));
      }
    }
    console.log('getAllLeafNodes 结果:', result);
    return result;
  }

  // 过滤班级树到指定层级
  const getFilteredClassTree = (
    tree: SchoolDeptTreeItem[],
    teachType: number,
    isAdmin: boolean
  ) => {
    let classLimit = isAdmin
      ? SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT[String(teachType)]
      : SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT[teachType];
    
    if (!classLimit || classLimit.includes('不可选')) {
      return [];
    }
    
    if (classLimit.includes('学段')) {
      return tree;
    }
    
    if (classLimit.includes('年级')) {
      const result = tree.flatMap((node) => node.children || []);
      return result;
    }
    
    if (classLimit.includes('班级')) {
      const result = getAllLeafNodes(tree);
      return result;
    }
    
    // 如果没有匹配的限制条件，默认返回所有叶子节点（班级）
    const result = getAllLeafNodes(tree);
    return result;
  };

  // 修改 handleAdminJobChange，切换 teachType 时过滤班级树
  const handleAdminJobChange = (idx: number, key: string, value: any) => {
    console.log('=== 行政职务变更 ===', { idx, key, value });
    
    const newAdminJobs = adminJobs.map((item, i) => {
      if (i === idx) {
        if (key === 'teachType') {
          const teachTypeNum = Number(value);
          const filteredTree = getFilteredClassTree(classTree, teachTypeNum, true);
          
          return {
            ...item,
            teachType: value,
            deptId: '',
            filteredClassTree: filteredTree
          };
        }
        return { ...item, [key]: value };
      }
      return item;
    });
    
    setAdminJobs(newAdminJobs);
  };

  // 行政职务操作
  const handleAddAdminJob = () => {
    const newAdminJobs = [
      ...adminJobs,
      { teachType: '', deptId: '', disabled: false, filteredClassTree: [] }
    ];
    setAdminJobs(newAdminJobs);
  };
  const handleRemoveAdminJob = (idx: number) => {
    const newAdminJobs = adminJobs.filter((_, i) => i !== idx);
    setAdminJobs(newAdminJobs);
  };
  // 教学职务操作
  const handleAddTeachJob = () => {
    console.log('添加教学职务，当前职务:', teachJobs);
    const newTeachJobs = [
      ...teachJobs,
      {
        teachType: '',
        deptId: '',
        subjectId: '',
        disabled: false,
        filteredClassTree: []
      }
    ];
    console.log('添加后的教学职务:', newTeachJobs);
    setTeachJobs(newTeachJobs);
  };
  const handleRemoveTeachJob = (idx: number) => {
    console.log('删除教学职务，索引:', idx);
    console.log('删除前的教学职务:', teachJobs);
    const newTeachJobs = teachJobs.filter((_, i) => i !== idx);
    console.log('删除后的教学职务:', newTeachJobs);
    setTeachJobs(newTeachJobs);
  };
  // 修改 handleTeachJobChange，切换 teachType 时过滤班级树
  const handleTeachJobChange = (idx: number, key: string, value: any) => {
    setTeachJobs(
      teachJobs.map((item, i) => {
        if (i === idx) {
          if (key === 'teachType') {
            return {
              ...item,
              teachType: value,
              deptId: '',
              subjectId: '',
              filteredClassTree: getFilteredClassTree(classTree, Number(value), false)
            };
          }
          return { ...item, [key]: value };
        }
        return item;
      })
    );
  };

  // 递归渲染班级树，优先显示 deptName，将七年级改为初一
  const renderClassOptions = (tree: SchoolDeptTreeItem[], parentPath: string = '', context: string = ''): JSX.Element[] => {
    if (!tree || tree.length === 0) {
      return [];
    }
    
    return tree.flatMap((node, index) => {
      // 优先使用 deptName，如果没有则使用 name，最后使用 id
      const nodeName = (node as any).deptName || node.name || String(node.id);
      const displayName = formatClassName(nodeName, node.id);
      const uniqueKey = context ? `${context}-${parentPath || 'root'}-${node.id}-${index}-${keyCounterRef.current++}` : `${parentPath || 'root'}-${node.id}-${index}-${keyCounterRef.current++}`;
      
      return [
        <option key={uniqueKey} value={node.id}>
          {displayName}
        </option>,
        ...(node.children ? renderClassOptions(node.children, uniqueKey, context) : [])
      ];
    });
  };

  // 格式化班级名称，将七年级改为初一
  const formatClassName = (name: string, nodeId: string | number): string => {
    if (!name || name.trim() === '') {
      return `节点${nodeId}`;
    }

    // 将七年级改为初一
    let formattedName = name.replace(/七年级/g, '初一');

    // 如果是班级级别，显示为"年级+班级"格式
    if (formattedName.includes('班')) {
      // 在班级树中查找当前节点的父级年级
      const findGradeForClass = (tree: SchoolDeptTreeItem[], classId: string | number): string => {
        for (const node of tree) {
          if (node.children) {
            for (const child of node.children) {
              if (child.id === classId) {
                return node.deptName || node.name || '';
              }
              if (child.children) {
                for (const grandChild of child.children) {
                  if (grandChild.id === classId) {
                    return child.deptName || child.name || '';
                  }
                }
              }
            }
          }
        }
        return '';
      };

      const gradeName = findGradeForClass(classTree, nodeId);
      if (gradeName) {
        const formattedGrade = gradeName.replace(/七年级/g, '初一');
        const result = `${formattedGrade}${formattedName}`;
        return result;
      }
    }

    return formattedName;
  };



  // 提交表单
  const handleSubmit = async () => {
    if (!form.name || !form.tmbId || !form.phone) {
      toast({ title: '请填写完整信息', status: 'warning' });
      return;
    }
    setLoading(true);
    try {
      // 只提交有效的职务（有 teachType 的）
      const validAdminJobs = adminJobs.filter((j) => j.teachType && j.teachType !== '');
      const validTeachJobs = teachJobs.filter((j) => j.teachType && j.teachType !== '');
      
      const params: TeacherUpdateParams = {
        id: form.id,
        tmbId: Number(form.tmbId),
        name: form.name,
        phone: form.phone,
        gender: Number(form.gender),
        schoolManageTeachersDetail: validAdminJobs.map((j) => ({
          teachType: Number(j.teachType),
          deptId: j.deptId || '',
          deptName: ''
        })),
        schoolSubjectTeachersDetail: validTeachJobs.map((j) => ({
          teachType: Number(j.teachType),
          deptId: j.deptId || '',
          deptName: '',
          subjectId: j.subjectId ? String(j.subjectId) : ''
        }))
      };

      // 打印提交参数
      console.log('提交参数 params:', params);

      await updateTeacher(params);
      toast({ title: '保存成功', status: 'success' });
      onClose();
      if (onSuccess) onSuccess();
    } catch (e) {
      toast({ title: '保存失败', status: 'error' });
      console.error('保存失败:', e);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <Box
        position="fixed"
        top="50%"
        left="50%"
        transform="translate(-50%, -50%)"
        w="684px"
        h="748px"
        bg="white"
        borderRadius="lg"
        overflow="hidden"
        zIndex={1400}
        boxShadow="xl"
        display="flex"
        flexDirection="column"
        maxH="748px"
        minH="748px"
      >
        <Box
          position="relative"
          pb={4}
          borderBottom="1px solid"
          borderColor="gray.200"
          px={6}
          pt={6}
        >
          <Text fontWeight="bold" fontSize="20px">
            编辑信息
          </Text>
          <IconButton
            position="absolute"
            top={4}
            right={4}
            aria-label="关闭"
            icon={<Text fontSize="16px">×</Text>}
            variant="ghost"
            size="sm"
            onClick={onClose}
          />
        </Box>
        <Box p={6} overflow="auto" w="100%" flex={1} maxH="calc(748px - 80px)">
          <Flex gap={4} mb={3}>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>
                * 姓名：
              </FormLabel>
              <Input
                placeholder="请输入姓名"
                value={form.name}
                onChange={(e) => setForm((f) => ({ ...f, name: e.target.value }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              />
            </FormControl>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>
                * 账号：
              </FormLabel>
              <Select
                value={form.tmbId}
                onChange={(e) => setForm((f) => ({ ...f, tmbId: e.target.value }))}
                placeholder={teacher?.account}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              >
                {accountList.map((s) => (
                  <option key={s.id} value={s.id}>
                    {s.account}
                  </option>
                ))}
              </Select>
              <Text color="gray.400" fontSize="12px" mt={1}>
                需从现有成员管理账号中选取
              </Text>
            </FormControl>
          </Flex>
          <Flex gap={4} mb={4}>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>
                * 手机号：
              </FormLabel>
              <Input
                placeholder="请输入手机号"
                value={form.phone}
                onChange={(e) => setForm((f) => ({ ...f, phone: e.target.value }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              />
            </FormControl>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>
                * 性别：
              </FormLabel>
              <Select
                value={form.gender}
                onChange={(e) => setForm((f) => ({ ...f, gender: Number(e.target.value) }))}
                size="sm"
                borderRadius="md"
                bg="#F2F3F5"
                border="none"
                _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                _hover={{ bg: '#F2F3F5' }}
              >
                {GENDER_OPTIONS.map((opt, genderIndex) => (
                  <option key={`gender-${opt.value}-${genderIndex}`} value={opt.value}>
                    {opt.label}
                  </option>
                ))}
              </Select>
            </FormControl>
          </Flex>
          {/* 行政职务 */}
          <Text fontWeight="bold" mt={6} mb={3} fontSize="16px">
            行政职务
          </Text>
          {adminJobs.length === 0 ? (
            <Text color="gray.500" fontSize="14px" mb={2}>
              暂无行政职务
            </Text>
          ) : (
            adminJobs.map((item, idx) => {
            const teachTypeNum = Number(item.teachType);
            const classLimit = SCHOOL_MANAGE_TEACHER_TYPE_CLASS_LIMIT[teachTypeNum];
            const classDisabled = classLimit && classLimit.includes('不可选');
            return (
              <Flex key={`admin-job-${idx}`} align="center" mb={2} gap={2}>
                <Select
                  w="180px"
                  value={item.teachType}
                  onChange={(e) => {
                    console.log('=== Select onChange 触发 ===', { idx, value: e.target.value });
                    handleAdminJobChange(idx, 'teachType', e.target.value);
                  }}
                  placeholder="职务类型"
                  size="sm"
                  borderRadius="md"
                  bg="#F2F3F5"
                  border="none"
                  _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                  _hover={{ bg: '#F2F3F5' }}
                >
                  {Object.entries(SCHOOL_MANAGE_TEACHER_TYPE_MAP).map(([value, label], typeIndex) => (
                    <option key={`admin-type-${idx}-${value}-${typeIndex}`} value={String(value)}>
                      {label}
                    </option>
                  ))}
                </Select>
                <Select
                  w="180px"
                  value={item.deptId}
                  onChange={(e) => handleAdminJobChange(idx, 'deptId', e.target.value)}
                  isDisabled={!item.teachType || !!item.disabled || !!classDisabled}
                  placeholder="班级"
                  size="sm"
                  borderRadius="md"
                  bg="#F2F3F5"
                  border="none"
                  _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                  _hover={{ bg: '#F2F3F5' }}
                >
                  {renderClassOptions(item.filteredClassTree || [], '', `admin-${idx}`)}
                </Select>
                {item.disabled ? null : (
                  <IconButton
                    aria-label="删除"
                    icon={<DeleteIcon />}
                    colorScheme="red"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAdminJob(idx)}
                  />
                )}
              </Flex>
            );
          })
          )}
          <Button
            size="sm"
            variant="outline"
            onClick={handleAddAdminJob}
            mb={4}
            colorScheme="purple"
            leftIcon={<Text fontSize="16px">+</Text>}
            borderColor="purple.500"
            color="purple.500"
            _hover={{ bg: 'purple.50' }}
          >
            添加职务
          </Button>
          

          {/* 教学职务 */}
          <Text fontWeight="bold" mt={6} mb={3} fontSize="16px">
            教学职务
          </Text>
          {teachJobs.length === 0 ? (
            <Text color="gray.500" fontSize="14px" mb={2}>
              暂无教学职务
            </Text>
          ) : (
            teachJobs.map((item, idx) => {
            const teachTypeNum = Number(item.teachType);
            const classLimit = SCHOOL_SUBJECT_TEACHER_TYPE_CLASS_LIMIT[teachTypeNum];
            const classDisabled = classLimit && classLimit.includes('不可选');
            return (
              <Flex key={`teach-job-${idx}-${item.teachType}-${item.deptId}-${item.subjectId}`} align="center" mb={2} gap={2}>
                <Select
                  w="180px"
                  value={item.teachType}
                  onChange={(e) => handleTeachJobChange(idx, 'teachType', e.target.value)}
                  placeholder="职务类型"
                  size="sm"
                  borderRadius="md"
                  bg="#F2F3F5"
                  border="none"
                  _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                  _hover={{ bg: '#F2F3F5' }}
                >
                  {Object.entries(SCHOOL_SUBJECT_TEACHER_TYPE_MAP).map(([value, label], typeIndex) => (
                    <option key={`teach-type-${idx}-${value}-${typeIndex}`} value={value}>
                      {label}
                    </option>
                  ))}
                </Select>
                <Select
                  w="180px"
                  value={item.deptId}
                  onChange={(e) => handleTeachJobChange(idx, 'deptId', e.target.value)}
                  isDisabled={!item.teachType || !!item.disabled || !!classDisabled}
                  placeholder="班级"
                  size="sm"
                  borderRadius="md"
                  bg="#F2F3F5"
                  border="none"
                  _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                  _hover={{ bg: '#F2F3F5' }}
                >
                  {(() => {
                    const options = renderClassOptions(item.filteredClassTree || [], '', `teach-${idx}`);
                    console.log(`教学职务 ${idx} 班级选项:`, options);
                    return options;
                  })()}
                </Select>
                <Select
                  w="180px"
                  value={item.subjectId}
                  onChange={(e) => handleTeachJobChange(idx, 'subjectId', e.target.value)}
                  isDisabled={item.teachType !== '9' && (!item.deptId || !!item.disabled)}
                  placeholder="学科"
                  size="sm"
                  borderRadius="md"
                  bg="#F2F3F5"
                  border="none"
                  _focus={{ bg: '#F2F3F5', border: 'none', boxShadow: 'none' }}
                  _hover={{ bg: '#F2F3F5' }}
                >
                  {subjectList.map((sub, subIndex) => (
                    <option key={`teach-subject-${idx}-${sub.subjectId}-${subIndex}`} value={sub.subjectId}>
                      {sub.subjectName}
                    </option>
                  ))}
                </Select>
                {item.disabled ? null : (
                  <IconButton
                    aria-label="删除"
                    icon={<DeleteIcon />}
                    colorScheme="red"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      console.log('点击删除教学职务按钮，索引:', idx);
                      handleRemoveTeachJob(idx);
                    }}
                  />
                )}
              </Flex>
            );
          })
          )}
          <Button
            size="sm"
            variant="outline"
            onClick={handleAddTeachJob}
            mb={4}
            colorScheme="purple"
            leftIcon={<Text fontSize="16px">+</Text>}
            borderColor="purple.500"
            color="purple.500"
            _hover={{ bg: 'purple.50' }}
          >
            添加职务
          </Button>
          {/* 底部按钮 */}
          <Divider my={6} />
          <Flex justify="flex-end" gap={4}>
            <Button variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button colorScheme="purple" isLoading={loading} onClick={handleSubmit}>
              保存
            </Button>
          </Flex>
        </Box>
      </Box>
    </Modal>
  );
};

export default TeachersEditInformation;
