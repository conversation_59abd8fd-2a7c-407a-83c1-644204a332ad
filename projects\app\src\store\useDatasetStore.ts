import {
  delDatasetById,
  getAllDataset,
  getDatasetById,
  getDatasets,
  putDatasetById
} from '@/api/dataset';
import { DatasetTypeEnum } from '@/constants/api/dataset';
import { PermissionTypeEnum } from '@/constants/permission';
import { DatasetSimpleItemType } from '@/fastgpt/global/core/dataset/type';
import { DatasetItemType, UpdateDatasetProps } from '@/types/api/dataset';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

type State = {
  myDatasets: DatasetItemType[];
  allDatasets: DatasetSimpleItemType[];
  loadDatasets: (parentId?: string) => Promise<any>;
  loadAllDatasets: () => Promise<any>;
  datasetDetail?: DatasetItemType;
  loadDatasetDetail: (id: string, init?: boolean) => Promise<DatasetItemType>;
  updateDataset: (data: UpdateDatasetProps) => Promise<any>;
  delDatasetById: (id: string) => Promise<any>;
};

const updateKeyPairs: [keyof UpdateDatasetProps, keyof DatasetItemType][] = [
  ['parentId', 'parentId'],
  ['finalParentId', 'finalParentId'],
  ['name', 'name'],
  ['avatarUrl', 'avatarUrl'],
  ['intro', 'intro'],
  ['permission', 'permission']
];

export const useDatasetStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        myDatasets: [],
        allDatasets: [],
        async loadDatasets(parentId) {
          const res = await getDatasets({ parentId: parentId });

          set((state) => {
            state.myDatasets = res;
          });
          return res;
        },
        async loadAllDatasets() {
          const res = await getAllDataset();

          set((state) => {
            state.allDatasets = res;
          });

          return res;
        },
        datasetDetail: undefined,
        async loadDatasetDetail(id: string, init = false) {
          if (!id || (id === get().datasetDetail?.id && !init)) return get().datasetDetail!;

          const data = await getDatasetById(id);

          set((state) => {
            state.datasetDetail = data;
          });

          return data;
        },
        async updateDataset(data) {
          await putDatasetById(data);

          if (get().datasetDetail?.id === data.id) {
            set((state) => {
              state.datasetDetail = {
                ...get().datasetDetail!,
                ...data
              };
            });
          }

          set((state) => {
            state.myDatasets = state.myDatasets = state.myDatasets.map((item) => {
              if (item.id !== data.id) {
                return item;
              }
              const newItem = {
                ...item
              };

              for (let pair of updateKeyPairs) {
                if (data[pair[0]] !== undefined) {
                  (newItem as any)[pair[1]] = data[pair[0]];
                }
              }

              return newItem;
            });
          });
        },
        async delDatasetById(id) {
          delDatasetById(id).then(() => {
            set((state) => {
              state.myDatasets = state.myDatasets.filter((item) => item.id !== id);
            });
          });
        }
      })),
      {
        name: 'datasetStore',
        partialize: () => ({})
      }
    )
  )
);
