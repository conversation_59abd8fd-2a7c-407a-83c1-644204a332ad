import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { Box, Button, Input, Text } from '@chakra-ui/react';
import { SelectProps, Select } from 'antd';
import { useEffect } from 'react';
import styled from '@emotion/styled';
import SmartHomework from './SmartHomework';
import LayeredHomework from './Layeredhomework';
import WritingTasks from './WritingTasks';
import CustomHomework from './CustomHomework';
import { useSearchParams } from 'next/navigation';
import { getHomeworkDetail } from '@/api/homeworkDetail';
import { useHomeworkStore } from '@/store/useHomework';

function HomeworkScope() {
  const { homeworkDetail, homeworkTypeDetail } = useHomeworkStore();
  console.log('HomeworkScope', homeworkDetail?.taskType, homeworkTypeDetail);
  // 根据homeworkDetail?.taskType 判断是哪种作业  1-智慧作业；2-分层作业；3-写作任务；4-自定义作业
  return (
    <Box color={'#1D2129'}>
      {homeworkDetail?.taskType === 1 && <SmartHomework></SmartHomework>}
      {homeworkDetail?.taskType === 2 && <LayeredHomework></LayeredHomework>}
      {homeworkDetail?.taskType === 3 && <WritingTasks></WritingTasks>}
      {homeworkDetail?.taskType === 4 && <CustomHomework></CustomHomework>}
    </Box>
  );
}

export default HomeworkScope;
