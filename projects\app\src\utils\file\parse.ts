import mammoth from 'mammoth';

export const convertFileToHtml = async (file: Blob): Promise<string> => {
  const reader = new FileReader();
  return new Promise((resolve, reject) => {
    reader.onload = async (e) => {
      const content = e?.target?.result;
      try {
        const result = await mammoth.convertToHtml(
          {
            arrayBuffer: content as ArrayBuffer
          },
          {
            styleMap: ["p[style-name='文档正文'] => p.body-text", 'table => table.custom-table']
          }
        );
        resolve(result.value);
      } catch (error) {
        reject('Error converting document: ' + error);
      }
    };
    reader.readAsArrayBuffer(file);
  });
};
