/**
 * 作业操作按钮工具函数
 * 根据学生状态、批改模式、提交方式来决定显示哪些按钮
 */

export interface ButtonConfig {
  show: boolean;
  text: string;
  type: 'primary' | 'secondary' | 'correction';
  action: string;
}

export interface HomeworkButtonsConfig {
  view: ButtonConfig;
  urge: ButtonConfig;
  helpRecord: ButtonConfig;
  manualCorrect: ButtonConfig;
  aiCorrect: ButtonConfig;
  confirm: ButtonConfig;
  modify: ButtonConfig;
  reCorrect: ButtonConfig;
}

/**
 * 获取学生操作按钮配置
 * @param studentStatus 学生状态：0-待提交，1-未提交，2-待批改，3-AI批改中，4-待确认，5-已完成，6-待订正，7-批改失败
 * @param correctMethod 批改方式：1-AI批改，2-手动批改
 * @param submitMethod 提交方式：1-学生在线答题，2-学生平板拍照,3-教师帮录
 * @returns 按钮配置对象
 */
export function getStudentActionButtons(
  studentStatus: number,
  correctMethod: number,
  submitMethod: number
): HomeworkButtonsConfig {
  const defaultConfig: HomeworkButtonsConfig = {
    view: { show: false, text: '查看', type: 'secondary', action: 'view' }, // AI批改中状态不显示任何按钮
    urge: { show: false, text: '催交', type: 'primary', action: 'urge' },
    helpRecord: { show: false, text: '帮录', type: 'primary', action: 'helpRecord' },
    manualCorrect: { show: false, text: '手动批改', type: 'primary', action: 'manualCorrect' },
    aiCorrect: { show: false, text: 'AI批改', type: 'correction', action: 'aiCorrect' },
    confirm: { show: false, text: '确认', type: 'primary', action: 'confirm' },
    modify: { show: false, text: '修改', type: 'secondary', action: 'modify' },
    reCorrect: { show: false, text: '重新批改', type: 'primary', action: 'reCorrect' }
  };

  // 根据新的状态表配置按钮显示
  switch (studentStatus) {
    case 0: // 待提交
    case 1: // 未提交
    case 6: // 待订正
      // 同时显示催交和帮录按钮
      defaultConfig.urge.show = true;
      defaultConfig.helpRecord.show = true;
      defaultConfig.view.show = true;
      break;

    case 2: // 待批改
      // 学生已提交作业，可以查看提交内容
      defaultConfig.view.show = true;
      if (correctMethod === 1) {
        // AI批改模式 - 无操作按钮
        // 不显示任何按钮
      } else if (correctMethod === 2) {
        // 手动批改模式 - 显示批改和AI批改
        defaultConfig.manualCorrect.show = true;
        defaultConfig.aiCorrect.show = true;
      }
      break;

    case 3: // 批改中（AI）
      // AI批改中 - 可以查看提交内容和批改进度
      defaultConfig.view.show = true;
      break;

    case 4: // 待确认
      // 批改完成待确认 - 可以查看批改结果
      defaultConfig.view.show = true;
      if (correctMethod === 1) {
        // AI批改模式 - 显示确认和重新批改
        defaultConfig.confirm.show = true;
        defaultConfig.reCorrect.show = true;
      } else if (correctMethod === 2) {
        // 手动批改模式 - 显示确认和AI批改
        defaultConfig.confirm.show = true;
        defaultConfig.aiCorrect.show = true;
      }
      break;

    case 8: // 批改中（手动）
      // 手动批改中 - 可以查看提交内容，显示AI批改
      defaultConfig.view.show = true;
      defaultConfig.aiCorrect.show = true;
      break;

    case 5: // 已完成
      // 已完成 - 只显示查看
      defaultConfig.view.show = true;
      break;

    case 7: // 批改失败
      // 批改失败 - 可以查看提交内容和失败原因
      defaultConfig.view.show = true;
      // 批改失败状态在表格中未明确定义，保持与待订正相同的逻辑
      defaultConfig.urge.show = true;
      defaultConfig.helpRecord.show = true;
      break;

    default:
      // 默认不显示任何按钮
      break;
  }

  return defaultConfig;
}

/**
 * 获取批量操作按钮配置
 * @param selectedStudents 选中的学生列表
 * @param correctMethod 批改方式
 * @param submitMethod 提交方式
 * @returns 批量按钮配置
 */
export function getBatchActionButtons(
  selectedStudents: any[],
  correctMethod: number,
  submitMethod: number
) {
  const batchConfig = {
    batchUrge: false,
    batchHelpRecord: false,
    batchAiCorrect: false,
    batchManualCorrect: false,
    batchConfirm: false,
    batchReModify: false,
    batchExport: false
  };

  if (selectedStudents.length === 0) {
    return batchConfig;
  }

  // 分析选中学生的状态
  const statusCounts = selectedStudents.reduce(
    (acc, student) => {
      acc[student.status] = (acc[student.status] || 0) + 1;
      return acc;
    },
    {} as Record<number, number>
  );

  // 待提交/未提交/待订正学生
  const pendingStudents = (statusCounts[0] || 0) + (statusCounts[1] || 0) + (statusCounts[6] || 0);
  if (pendingStudents > 0) {
    // 同时显示批量催交和批量帮录
    batchConfig.batchUrge = true;
    batchConfig.batchHelpRecord = true;
  }

  // 待批改学生
  const toBeCorrectedStudents = statusCounts[2] || 0;
  if (toBeCorrectedStudents > 0) {
    if (correctMethod === 1) {
      batchConfig.batchAiCorrect = true;
    } else if (correctMethod === 2) {
      batchConfig.batchManualCorrect = true;
      batchConfig.batchAiCorrect = true;
    }
  }

  // 待确认学生
  const toBeConfirmedStudents = statusCounts[4] || 0; // 待确认状态是4
  if (toBeConfirmedStudents > 0) {
    batchConfig.batchConfirm = true;
    batchConfig.batchReModify = true;
  }

  // 已完成学生
  const completedStudents = statusCounts[5] || 0;
  if (completedStudents > 0 && submitMethod === 2) {
    batchConfig.batchExport = true;
  }

  return batchConfig;
}

/**
 * 获取状态显示文本和样式
 * @param status 学生状态
 * @returns 状态配置
 */
export function getStatusConfig(status: number) {
  const statusMap: Record<
    number,
    { text: string; background: string; color: string; isGradient?: boolean }
  > = {
    0: { text: '待提交', background: '#F2F3F5', color: '#4E5969' },
    1: { text: '未提交', background: '#FFEFEF', color: '#F53F3F' },
    2: { text: '待批改', background: '#FFF6E3', color: '#B77D00' },
    3: { text: 'AI批改中', background: '#F3F0FF', color: 'transparent', isGradient: true },
    4: { text: '待确认', background: '#FFF6E3', color: '#B77D00' },
    5: { text: '已完成', background: '#E8FFEA', color: '#00B42A' },
    6: { text: '待订正', background: '#FFEFEF', color: '#F53F3F' },
    7: { text: '批改失败', background: '#FFEFEF', color: '#F53F3F' },
    8: { text: '手动批改中', background: '#FFF6E3', color: '#B77D00' }
  };

  return statusMap[status] || { text: '-', background: '#F2F3F5', color: '#4E5969' };
}

/**
 * 获取状态中文名称（用于日志输出）
 * @param status 学生状态
 * @returns 中文状态名称
 */
export function getStatusName(status: number): string {
  const statusNames: Record<number, string> = {
    0: '待提交',
    1: '未提交',
    2: '待批改',
    3: 'AI批改中',
    4: '待确认',
    5: '已完成',
    6: '待订正',
    7: '批改失败',
    8: '手动批改中'
  };

  return statusNames[status] || `未知状态(${status})`;
}

/**
 * 获取批改方式中文名称
 * @param correctMethod 批改方式
 * @returns 中文名称
 */
export function getCorrectMethodName(correctMethod: number): string {
  return correctMethod === 1 ? 'AI批改' : '手动批改';
}

/**
 * 获取提交方式中文名称
 * @param submitMethod 提交方式：1-学生在线答题，2-学生平板拍照，3-教师帮录
 * @returns 中文名称
 */
export function getSubmitMethodName(submitMethod: number): string {
  switch (submitMethod) {
    case 1:
      return '学生在线答题';
    case 2:
      return '学生平板拍照';
    case 3:
      return '教师帮录';
    default:
      return '未知提交方式';
  }
}
