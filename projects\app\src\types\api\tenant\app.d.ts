// =================== chat logs
import { AppStatus, Sort, Source, VariableInputEnum, Config } from '@/constants/api/app';
import { DatasetSearchModeEnum } from '@/constants/api/dataset';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { AppSchema } from '@/fastgpt/global/core/app/type';
import { AppDetailInfo } from '../app';

export interface AppLabelType {
  id: string;
  tenantSceneIds: string[];
  labelName: string;
  tenantLabelIds?: string[];
  sort: number;
}

export type getTenantAppListParams = {
  tenantId?: string;
};

export type GetPersonalAppListParams = {
  tenantId?: string;
  tmbId?: string;
};

export type AppLabelAppType = AppLabelType & {
  appCount: number;
  isDeleted?: number;
};

export type AppModalDataType = {
  id?: string;
  name?: string;
  avatarUrl?: string;
  tenantSceneIds: string[];
  tenantLabelIds?: string[];
  permission?: PermissionTypeEnum;
  intro?: string;
  industry?: string;
  status: 1 | 2;
};

export type LabelItemType = {
  id: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: number;
  tenantLabelId: number;
  tenantSceneId: number;
  tenantSceneName: string;
  tenantLabelName: string;
};

export type TenantAppListItemType = {
  id: string;
  name: string;
  avatarUrl: string;
  finalAppId: string;
  tenantId: string;
  intro: string;
  isOwner: boolean;
  canWrite: boolean;
  permission: PermissionTypeEnum;
  tenantSceneIds: string[];
  tenantLabelIds: string[];
  tmbId: string;
  mode: number;
  appId: string;
  labelList?: LabelItemType[];
  industry?: string;
  status: StatusType;
  createUsername: string;
  updateTime: string;
  createTime: string;
  sort: Sort;
  source: DataSource;
  type: AppTypeEnum;
  tenantName: string;
  userName: string;
  updateUsername: string;
  config: Config;
  copySourceId?: string | number;
  isPublished?: string | number;
};
export type StatusType = AppStatus.Online | AppStatus.OffLine;

export type CreateAppParams = {
  name?: string;
  avatarUrl?: string;
  tenantSceneIds?: string[];
  tenantLabelIds?: string[];
  type?: any;
  intro?: string;
  modules?: AppSchema['modules'];
  edges?: AppSchema['edges'];
  mode?: number;
  chatConfig?: AppSchema['chatConfig'];
};

export interface deleteAppParams {
  id: string;
  tmbId: string;
}
export interface TenantAppUpdateParams {
  id?: string;
  name?: string;
  sceneId?: string[];
  labelId?: string[];
  simpleTemplateId?: string;
  type?: AppTypeEnum;

  avatarUrl?: string;
  intro?: string;
  modules?: AppSchema['modules'];
  edges?: AppSchema['edges'];
  nodes?: AppSchema['modules'];
  chatConfig?: AppSchema['chatConfig'];

  permission?: PermissionTypeEnum;
}

export type AppLabelListParams = {
  sceneId: string;
};

export type AppLabelDetailParams = {
  id: string;
};

export interface AppLabelUpdateParams {
  id?: string;
  sceneId?: string;
  name?: string;
  sort?: number;
  isDelete?: number;
}

export interface AppLabelReorderParams {
  param: AppLabelUpdateParams[];
}

export type SelectedDatasetType = { datasetId: string; vectorModel: VectorModelItemType }[];

export interface TenantAppDetailInfo extends AppDetailInfo {}

export type transitionWorkflowBody = {
  id: string;
  createNew?: boolean;
};

export type transitionWorkflowResponse = {
  id?: string;
};

export type TenantAppUpdateConfigParams = {
  config: number;
  id: string;
  updateUsername: string;
};
