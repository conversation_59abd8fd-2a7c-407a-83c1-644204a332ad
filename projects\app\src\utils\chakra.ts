import { isMobile, mobileBreakpoint } from './mobile';

// chakra 断点，src/web/styles/theme.ts有相关配置，其中base定为mobile尺寸
const breakpoints = ['base', 'sm', 'md', 'lg', 'xl', '2xl'];
const designIndex = 4; // xl位置

const phoneRatio = 0.55;

const pcDimMinFactor = 0.5; // 传入尺寸乘这个因子得出缩小后的最小尺寸
const pcDimScale = 1.0; // 对所有传入值进行缩放
const pcDimStrideScale = 0.75; // 结果公式：resultDim = minDim + (designDim - minDim) * strideScale * breakpointIndex

// 创建一个立即执行的函数，它只会运行一次，然后将结果缓存到 isMaxFuncSupported
const isMaxFuncSupported = (() => {
  // 检查CSS.supports是否存在，以防在非浏览器环境（如SSR）中运行
  if (typeof window !== 'undefined' && window.CSS && window.CSS.supports) {
    // 如果在浏览器环境，则精确判断
    return window.CSS.supports('width', 'max(1px, 1vw)');
  }
  // 在所有其他未知环境（如SSR、老旧JS引擎等）中，默认其不支持，以保证安全降级
  return false;
})();

export const getRootFontSize = (width: number, height: number) => {
  if (isMobile) {
    return `${(width * 16) / 750}px`;
  }
  if (width >= mobileBreakpoint) {
    return '16px';
  }
  if (width / height > phoneRatio) {
    return `${(height * phoneRatio * 16) / 750}px`;
  }
  return `${(width * 16) / 750}px`;
};

export const updateRootFontSize = () => {
  document.documentElement.style.fontSize = getRootFontSize(
    document.documentElement.clientWidth,
    document.documentElement.clientHeight
  );
};

/**
 * rpx对应的实际尺寸，手机屏幕固定为750rpx，平板屏幕宽度可以大于750rpx，因此适配时水平填充的尽量使用弹性布局
 * 使用时按750rpx作为标准即可，平板中将自动缩小到合适的尺寸
 *
 * @param rpx
 * @returns
 */
export const rpxDim = (rpx: number) => `${rpx / 16}rem`;

export type RespDimsArg =
  | `${number}rpx`
  | `${number}fpx`
  | number
  | `${number}ms`
  | 'px'
  | 'cm'
  | 'mm'
  | 'in'
  | 'pt'
  | 'pc'
  | '%'
  | 'em'
  | 'rem'
  | 'vw'
  | 'vh'
  | 'ex'
  | 'ch';

const cache: Record<string, string[]> = {};

/**
 * 计算响应式尺寸，chakra响应式值为一个数组，对应不同breakpoint
 * PC端基于1920x1080设计图值，移动端基于750rpx设计图值
 * PC端：
 *   respDims(250) 1920时为250px，小于1920时使用缩放策略，计算结果单位默认为px
 *   respDims(250, '0.8ms') 1920时为250px，小于1920时使用缩放策略，计算结果单位默认为250 * 0.8 = 200 px
 *   respDims(250, 100, 'vw') 1920时为250vw，小于1920时取值不小于100vw
 *   respDims(-250, -100, 'cm') 1920时为-250cm，小于1920时取值大于等于-100cm（尺寸计算基于绝对值）
 *   respDims(‘16fp’) 1920时为16px，小于1920时取值不小于14px
 * 移动端：
 *   respDims('24rpx') 手机屏幕宽固定为750rpx，24rpx结果值=实际屏幕宽度x24/750，平板屏幕可以是大于750rpx
 * 多端:
 *   respDims('24rpx', '16fpx')
 *   respDims('24rpx', 250)
 * 参数顺序约定为：移动端rpx、PC端fpx或number、PC端结果单位
 *
 * @param args rpx 转为移动端响应尺寸，手机屏幕宽度规定为750rpx，平板屏幕宽度可以大于750rpx
 *             fpx 字体相关尺寸转为pc端响应尺寸，对于小于12像素的字体有特殊处理
 *             number 转为pc端响应尺寸
 *               第一个number为1920x1080设计图值
 *               第二个number为pc端时使用的最小值
 *             ms min scale最小值相对于设计值的缩小因子
 *             px cm mm ...为number计算结果单位
 *
 * @returns 响应式尺寸数组
 */
export const respDims = (...args: RespDimsArg[]) => {
  let rpxVal: number | undefined;
  let fpxVal: number | undefined;
  let designVal: number | undefined;
  let minVal: number | undefined;
  let unit = 'px';

  const key = args.join(',');
  const value = cache[key];
  if (value) {
    return value;
  }

  for (let arg of args) {
    if (typeof arg === 'number') {
      if (designVal === undefined) {
        designVal = arg * pcDimScale;
      } else if (minVal === undefined) {
        minVal = arg * pcDimScale;
      }
    } else if (typeof arg === 'string') {
      if (arg.endsWith('rpx')) {
        rpxVal = parseFloat(arg);
      } else if (arg.endsWith('fpx')) {
        fpxVal = parseFloat(arg) * pcDimScale;
      } else if (arg.endsWith('ms')) {
        if (designVal !== undefined && minVal === undefined) {
          minVal = designVal * parseFloat(arg);
        }
      } else {
        unit = arg;
      }
    }
  }

  let dims: string[];

  if (isMobile && rpxVal !== undefined) {
    dims = [rpxDim(rpxVal)];
  } else if (fpxVal !== undefined) {
    const size = fpxVal < 0 ? -fpxVal : fpxVal;
    // 不同屏幕尺寸使用不同选择器，设置不同的px值，主要用于字体，避免缩小时全部字体归为12px
    // 最小值关系：12 -> 12, 13 -> 12.5, 14 -> 13, 15 -> 13.5, 16 -> 14
    // 最小值公式：12 + (size - 12) * 0.5 = 6 + size * 0.5
    const sign = fpxVal < 0 ? '-' : '';
    const min = (size >= 12 ? 6 : size * 0.45) + size * 0.5;
    const q = ((size > min ? size - min : 0) / designIndex) * pcDimStrideScale;
    dims = breakpoints.map((_, i) => `${sign}${min + q * i}${unit}`);

    if (rpxVal !== undefined) {
      dims[0] = rpxDim(rpxVal);
    }
  } else if (designVal !== undefined) {
    if (minVal === undefined) {
      minVal = designVal * pcDimMinFactor;
    }
    const q = ((designVal - minVal) / designIndex) * pcDimStrideScale;
    dims = breakpoints.map((_, i) => `${minVal! + q * i}${unit}`);

    if (rpxVal !== undefined) {
      dims[0] = rpxDim(rpxVal);
    }
  } else if (rpxVal !== undefined) {
    dims = [rpxDim(rpxVal)];
  } else {
    dims = [];
  }

  cache[key] = dims;

  return dims;
};

/**
 * 精确响应式尺寸函数，实现平滑缩放，无断点跳跃。
 * 智能支持多种调用方式，兼具简洁性和灵活性。
 *
 * @param designValue 在1920px设计宽度下的原始值。
 * @param minValueOrOptions 可选。可以直接传入一个 `number` 作为最小像素值，或者传入一个 options 对象进行复杂配置。
 * @param options 当第二个参数为 `minValue` 时，可以传入第三个参数作为 options 对象。
 * @param options.designWidth 设计稿宽度，默认为1920。
 *   [重要]：此参数为高级选项，仅在项目中同时存在多种设计稿基准（如部分页面基于1080px，部分基于1920px）时使用。
 *   在常规开发中，应遵循项目统一的设计稿基准，无需修改此值。
 * @param options.minValue 可选。一个绝对的最小像素值。当vw计算值小于此值时，将采用此值。
 *
 * @returns 返回一个基于vw的字符串，或是一个 `max()` CSS函数字符串。
 *
 * @example
 * // 1. 简单缩放
 * vwDims(26) -> "1.354vw"
 *
 * // 2. 传入最小值 (推荐的简洁方式)
 * vwDims(300, 150) -> "max(150px, 15.625vw)"
 *
 * // 3. 传入最小值并指定设计宽度
 * vwDims(300, 150, { designWidth: 1000 }) -> "max(150px, 30vw)"
 *
 * // 4. 使用纯对象配置 (灵活性)
 * vwDims(300, { minValue: 150, designWidth: 1000 }) -> "max(150px, 30vw)"
 */
export const vwDims = (
  designValue: number,
  minValueOrOptions?: number | { designWidth?: number; minValue?: number },
  options?: { designWidth?: number }
) => {
  // 1. 参数归一化：将所有可能的入参形式，统一解析为一个最终的配置对象。
  const defaults = { designWidth: 1920, minValue: undefined };
  let userOptions: { designWidth?: number; minValue?: number } = {};

  if (typeof minValueOrOptions === 'number') {
    userOptions = { ...options, minValue: minValueOrOptions };
  } else if (typeof minValueOrOptions === 'object' && minValueOrOptions !== null) {
    userOptions = minValueOrOptions;
  }

  const { designWidth, minValue } = { ...defaults, ...userOptions };

  // 2. 核心计算：基于设计值和设计宽度，计算出基础的vw单位字符串。
  const vwString = `${(100 * designValue) / designWidth}vw`;

  // 3. 边界处理：如果定义了最小值，则根据环境支持情况返回max()或降级。
  if (minValue !== undefined) {
    if (isMaxFuncSupported) {
      return `max(${minValue}px, ${vwString})`;
    }
    // 在不支持max()的旧环境中，优雅降级，只应用基础的vw值。
    return vwString;
  }

  return vwString;
};
