import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import { NodeInfo } from '../types';
import { DEFAULT_VALUES } from '../constants';

/**
 * 递归查找符合条件的节点
 */
export const findNodeById = (nodes: Department[], id: string): Department | null => {
  for (const node of nodes || []) {
    if (node.id === id || node.deptId === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 递归查找教师信息
 */
export const findTeachersByNodeId = (nodes: Department[], id: string): TmbUser[] => {
  for (const node of nodes || []) {
    if (node.id === id) {
      return node.tmbUserList || [];
    }
    if (node.children && node.children.length > 0) {
      const found = findTeachersByNodeId(node.children, id);
      if (found.length > 0) {
        return found;
      }
    }
  }
  return [];
};

/**
 * 根据年级和科目查找匹配的节点
 */
export const findMatchingNode = (
  nodes: Department[],
  grade: string,
  subject: string
): Department | null => {
  for (const node of nodes || []) {
    // 检查当前节点是否匹配
    if (
      (node.deptName === grade || node.deptName?.includes(grade)) &&
      node.subjectName === subject
    ) {
      return node;
    }

    // 递归检查子节点
    if (node.children && node.children.length > 0) {
      const found = findMatchingNode(node.children, grade, subject);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 从节点数据中提取关键信息
 */
export const extractNodeInfo = (
  node: Department | null,
  id: string,
  grade: string,
  subject?: string
): NodeInfo => {
  const teachTypeValue = node
    ? (node as any).teachType || DEFAULT_VALUES.TEACH_TYPE
    : DEFAULT_VALUES.TEACH_TYPE;
  const subjectIdValue = node
    ? node.subjectId
      ? Number(node.subjectId)
      : DEFAULT_VALUES.SUBJECT_ID
    : DEFAULT_VALUES.SUBJECT_ID;

  const isPhaseLeader =
    grade.includes('小学部') || grade.includes('初中部') || grade.includes('高中部');

  return {
    id: id || '',
    deptId: id || '',
    teachType: teachTypeValue,
    subjectId: subjectIdValue,
    subject: subject,
    isSubjectLeader: grade === '全校',
    isPhaseLeader: isPhaseLeader,
    grade: grade,
    title: grade === '全校' ? `${subject}总负责人` : `${grade} ${subject}`
  };
};

/**
 * 转换教师数据格式
 */
export const formatTeacherData = (teacherInfo: TmbUser[]) => {
  return teacherInfo.map((user) => ({
    name: user.userName,
    id: String(user.tmbId),
    tmbId: String(user.tmbId)
  }));
};

/**
 * 生成唯一ID
 */
export const generateUniqueId = (prefix: string, identifier: string): string => {
  return `${prefix}-${identifier}-${Date.now()}`;
};

/**
 * 判断某个学科在指定学段是否存在数据
 * @param treeData - 完整的树形数据
 * @param subjectName - 学科名称
 * @param stageName - 学段名称（如"小学"、"初中"、"高中"）
 * @returns 是否存在该学段的数据
 */
export const hasStageData = (
  treeData: Department[],
  subjectName: string,
  stageName: string
): boolean => {
  // 找到对应的学科节点
  const subjectNode = treeData.find((item) => item.subjectName === subjectName);
  if (!subjectNode?.children) {
    return false;
  }

  // 规范化学段名称（移除'部'后缀进行匹配）
  const normalizedStageName = stageName.replace(/部$/, '');

  // 检查是否存在对应的学段节点
  return subjectNode.children.some(
    (child) =>
      child.deptName === normalizedStageName || child.deptName?.includes(normalizedStageName)
  );
};

/**
 * 判断某个学科在指定学段的指定年级是否存在数据
 * @param treeData - 完整的树形数据
 * @param subjectName - 学科名称
 * @param stageName - 学段名称（如"小学"、"初中"、"高中"）
 * @param gradeName - 年级名称（如"一年级"、"初三"）
 * @returns 是否存在该年级的数据
 */
export const hasGradeData = (
  treeData: Department[],
  subjectName: string,
  stageName: string,
  gradeName: string
): boolean => {
  // 找到对应的学科节点
  const subjectNode = treeData.find((item) => item.subjectName === subjectName);
  if (!subjectNode?.children) {
    return false;
  }

  // 规范化学段名称（移除'部'后缀进行匹配）
  const normalizedStageName = stageName.replace(/部$/, '');

  // 找到对应的学段节点
  const stageNode = subjectNode.children.find(
    (child) =>
      child.deptName === normalizedStageName || child.deptName?.includes(normalizedStageName)
  );

  if (!stageNode?.children) {
    return false;
  }

  // 检查是否存在对应的年级节点
  return stageNode.children.some((child) => child.deptName === gradeName);
};

/**
 * 判断某个学科是否存在（用于总负责人级别的判断）
 * @param treeData - 完整的树形数据
 * @param subjectName - 学科名称
 * @returns 是否存在该学科
 */
export const hasSubjectData = (treeData: Department[], subjectName: string): boolean => {
  return treeData.some((item) => item.subjectName === subjectName);
};

// Next.js 页面组件默认导出
const DataUtilsPage = () => {
  return null;
};
export default DataUtilsPage;
