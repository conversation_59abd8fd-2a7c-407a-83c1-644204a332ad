import React, { useState } from 'react';
import { Box, Flex, Text, Button, VStack, HStack, Progress, Input } from '@chakra-ui/react';
import { ArrowLeftIcon, ArrowRightIcon } from '@chakra-ui/icons';
import { respDims } from '@/utils/chakra';
import { IsTextInput } from '@/types/api/questionnaire';

// 题目类型枚举
export enum QuestionType {
  SINGLE_CHOICE = 'single_choice',
  MULTIPLE_CHOICE = 'multiple_choice',
  TEXT_INPUT = 'text_input',
  RATING = 'rating'
}

// 选项接口（支持文本输入）
export interface QuestionOption {
  id: string;
  content: string;
  isTextInput: IsTextInput;
}

// 题目数据接口
export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  description?: string;
  required?: boolean;
  options?: QuestionOption[];
  maxRating?: number;
  placeholder?: string;
}

// 答案数据接口
export interface Answer {
  questionId: string;
  value: string | string[] | number;
  selectedOptions?: {
    optionId: string;
    textInput?: string;
  }[];
}

// 简化的单选题组件（支持文本输入）
const SimpleRadioQuestion: React.FC<{
  question: Question;
  value?: string;
  onChange: (value: string) => void;
  selectedOptions?: { optionId: string; textInput?: string }[];
  onSelectedOptionsChange?: (options: { optionId: string; textInput?: string }[]) => void;
}> = ({ question, value, onChange, selectedOptions = [], onSelectedOptionsChange }) => {
  const [textInputs, setTextInputs] = useState<{ [optionId: string]: string }>({});

  if (!question.options) return null;

  const handleOptionClick = (option: QuestionOption) => {
    onChange(option.content);

    // 如果选择的是文本输入选项，确保在selectedOptions中有对应记录
    if (option.isTextInput === IsTextInput.Yes && onSelectedOptionsChange) {
      const existingOption = selectedOptions.find((so) => so.optionId === option.id);
      if (!existingOption) {
        const newSelectedOptions = [
          ...selectedOptions.filter((so) => so.optionId !== option.id),
          { optionId: option.id, textInput: textInputs[option.id] || '' }
        ];
        onSelectedOptionsChange(newSelectedOptions);
      }
    }
  };

  const handleTextInputChange = (optionId: string, text: string) => {
    setTextInputs((prev) => ({ ...prev, [optionId]: text }));

    if (onSelectedOptionsChange) {
      const newSelectedOptions = selectedOptions.map((so) =>
        so.optionId === optionId ? { ...so, textInput: text } : so
      );

      // 如果该选项还不在selectedOptions中，添加它
      if (!selectedOptions.find((so) => so.optionId === optionId)) {
        newSelectedOptions.push({ optionId, textInput: text });
      }

      onSelectedOptionsChange(newSelectedOptions);
    }
  };

  const getSelectedOption = () => {
    return question.options?.find((option) => option.content === value);
  };

  const selectedOption = getSelectedOption();

  return (
    <VStack spacing={4} align="stretch">
      {question.options.map((option, index) => {
        const isSelected = value === option.content;
        const isTextInputOption = option.isTextInput === IsTextInput.Yes;

        return (
          <VStack key={index} spacing={2} align="stretch">
            <Box
              p={4}
              borderWidth="2px"
              borderRadius="12px"
              borderColor={isSelected ? 'primary.500' : 'myGray.200'}
              bg={isSelected ? 'primary.50' : 'white'}
              cursor="pointer"
              onClick={() => handleOptionClick(option)}
              transition="all 0.2s"
              _hover={{
                borderColor: 'primary.300',
                bg: isSelected ? 'primary.100' : 'primary.25'
              }}
            >
              <HStack>
                <Box
                  w={5}
                  h={5}
                  borderRadius="full"
                  borderWidth="2px"
                  borderColor={isSelected ? 'primary.500' : 'myGray.300'}
                  bg={isSelected ? 'primary.500' : 'white'}
                />
                <Text fontSize="md" fontWeight={isSelected ? 'semibold' : 'medium'}>
                  {option.content}
                </Text>
              </HStack>
            </Box>

            {/* 如果是文本输入选项且被选中，显示输入框 */}
            {isTextInputOption && isSelected && (
              <Input
                placeholder="请填写其他选项内容"
                value={textInputs[option.id] || ''}
                onChange={(e) => handleTextInputChange(option.id, e.target.value)}
                borderRadius="8px"
                borderColor="myGray.300"
                _focus={{
                  borderColor: 'primary.500',
                  boxShadow: '0 0 0 1px var(--chakra-colors-primary-500)'
                }}
                bg="white"
                ml={4}
              />
            )}
          </VStack>
        );
      })}
    </VStack>
  );
};

// 模拟问卷数据
const mockQuestions: Question[] = [
  {
    id: '1',
    type: QuestionType.SINGLE_CHOICE,
    title: '在您的日常教学或工作中，您是否使用过AI工具？',
    required: true,
    options: [
      {
        id: '1-1',
        content: '经常使用，已成为工作习惯',
        isTextInput: IsTextInput.No
      },
      {
        id: '1-2',
        content: '偶尔使用，在特定场景下会尝试',
        isTextInput: IsTextInput.No
      },
      {
        id: '1-3',
        content: '完全不了解或从未使用过',
        isTextInput: IsTextInput.No
      },
      {
        id: '1-4',
        content: '其他',
        isTextInput: IsTextInput.Yes
      }
    ]
  },
  {
    id: '2',
    type: QuestionType.SINGLE_CHOICE,
    title: '您对AI工具的整体满意度如何？',
    required: true,
    options: [
      {
        id: '2-1',
        content: '非常满意',
        isTextInput: IsTextInput.No
      },
      {
        id: '2-2',
        content: '比较满意',
        isTextInput: IsTextInput.No
      },
      {
        id: '2-3',
        content: '一般',
        isTextInput: IsTextInput.No
      },
      {
        id: '2-4',
        content: '不太满意',
        isTextInput: IsTextInput.No
      },
      {
        id: '2-5',
        content: '非常不满意',
        isTextInput: IsTextInput.No
      },
      {
        id: '2-6',
        content: '其他原因',
        isTextInput: IsTextInput.Yes
      }
    ]
  }
];

const QuestionnaireTestPage: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [isCompleted, setIsCompleted] = useState(false);
  const [selectedOptionsMap, setSelectedOptionsMap] = useState<{
    [questionId: string]: { optionId: string; textInput?: string }[];
  }>({});

  const currentQuestion = mockQuestions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / mockQuestions.length) * 100;

  // 处理答案更新
  const handleAnswerChange = (questionId: string, value: string | string[] | number) => {
    setAnswers((prev) => {
      const existingIndex = prev.findIndex((answer) => answer.questionId === questionId);
      const newAnswer: Answer = {
        questionId,
        value,
        selectedOptions: selectedOptionsMap[questionId] || []
      };

      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = newAnswer;
        return updated;
      } else {
        return [...prev, newAnswer];
      }
    });
  };

  // 处理选项更新（包含文本输入）
  const handleSelectedOptionsChange = (
    questionId: string,
    options: { optionId: string; textInput?: string }[]
  ) => {
    setSelectedOptionsMap((prev) => ({
      ...prev,
      [questionId]: options
    }));

    // 同时更新答案中的selectedOptions
    setAnswers((prev) => {
      const existingIndex = prev.findIndex((answer) => answer.questionId === questionId);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          selectedOptions: options
        };
        return updated;
      }
      return prev;
    });
  };

  // 获取当前题目的答案
  const getCurrentAnswer = (questionId: string) => {
    return answers.find((answer) => answer.questionId === questionId)?.value;
  };

  // 检查当前题目是否已回答
  const isCurrentQuestionAnswered = () => {
    if (!currentQuestion.required) return true;
    const answer = getCurrentAnswer(currentQuestion.id);
    return answer !== undefined && answer !== null && answer !== '';
  };

  // 下一题
  const handleNext = () => {
    if (currentQuestionIndex < mockQuestions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    } else {
      setIsCompleted(true);
    }
  };

  // 上一题
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  if (isCompleted) {
    return (
      <Box
        minH="100vh"
        bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        display="flex"
        alignItems="center"
        justifyContent="center"
        p={8}
      >
        <Box
          bg="white"
          borderRadius="20px"
          p={12}
          maxW="600px"
          w="100%"
          textAlign="center"
          boxShadow="0 20px 40px rgba(0,0,0,0.1)"
        >
          <Text fontSize="2xl" fontWeight="bold" color="primary.500" mb={4}>
            问卷完成！
          </Text>
          <Text fontSize="lg" color="myGray.600" mb={6}>
            感谢您的参与，您的反馈对我们非常重要。
          </Text>

          {/* 显示答案详情 */}
          <Box textAlign="left" mb={8} p={4} bg="gray.50" borderRadius="12px">
            <Text fontSize="md" fontWeight="bold" mb={4}>
              您的答案：
            </Text>
            {answers.map((answer, index) => {
              const question = mockQuestions.find((q) => q.id === answer.questionId);
              const selectedOption = answer.selectedOptions?.[0];

              return (
                <Box key={index} mb={3} p={3} bg="white" borderRadius="8px">
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700" mb={1}>
                    问题 {index + 1}: {question?.title}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    答案: {answer.value}
                  </Text>
                  {selectedOption?.textInput && (
                    <Text fontSize="sm" color="primary.600" mt={1}>
                      补充说明: {selectedOption.textInput}
                    </Text>
                  )}
                </Box>
              );
            })}
          </Box>
          <Button
            colorScheme="purple"
            size="lg"
            onClick={() => {
              setCurrentQuestionIndex(0);
              setAnswers([]);
              setIsCompleted(false);
            }}
          >
            重新开始
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p={8}>
      {/* 头部 */}
      <Flex align="center" justify="space-between" mb={8} color="white">
        <HStack spacing={4}>
          <ArrowLeftIcon boxSize={6} />
          <Text fontSize="xl" fontWeight="bold">
            调研问卷测试
          </Text>
        </HStack>
        <Text fontSize="md">
          {currentQuestionIndex + 1} / {mockQuestions.length}
        </Text>
      </Flex>

      {/* 进度条 */}
      <Progress
        value={progress}
        colorScheme="purple"
        bg="whiteAlpha.300"
        borderRadius="full"
        mb={8}
        h={2}
      />

      {/* 问题卡片 */}
      <Box
        bg="white"
        borderRadius="20px"
        p={8}
        mb={8}
        boxShadow="0 10px 30px rgba(0,0,0,0.1)"
        minH="400px"
      >
        <VStack spacing={6} align="stretch">
          {/* 题目类型标签 */}
          <Box>
            <Text fontSize="sm" color="primary.500" fontWeight="medium" mb={2}>
              单选题
            </Text>
            <Text fontSize="xl" fontWeight="bold" color="myGray.900" lineHeight="1.4">
              {currentQuestionIndex + 1}、{currentQuestion.title}
            </Text>
          </Box>

          {/* 问题组件 */}
          <SimpleRadioQuestion
            question={currentQuestion}
            value={getCurrentAnswer(currentQuestion.id) as string}
            onChange={(value: string) => handleAnswerChange(currentQuestion.id, value)}
            selectedOptions={selectedOptionsMap[currentQuestion.id] || []}
            onSelectedOptionsChange={(options) =>
              handleSelectedOptionsChange(currentQuestion.id, options)
            }
          />
        </VStack>
      </Box>

      {/* 底部按钮 */}
      <Flex justify="space-between" align="center">
        <Button
          variant="ghost"
          color="white"
          leftIcon={<ArrowLeftIcon />}
          onClick={handlePrevious}
          isDisabled={currentQuestionIndex === 0}
          _hover={{ bg: 'whiteAlpha.200' }}
        >
          上一题
        </Button>

        <Button
          colorScheme="purple"
          rightIcon={<ArrowRightIcon />}
          onClick={handleNext}
          isDisabled={!isCurrentQuestionAnswered()}
          size="lg"
          px={8}
        >
          {currentQuestionIndex === mockQuestions.length - 1 ? '完成' : '下一题'}
        </Button>
      </Flex>
    </Box>
  );
};

export default QuestionnaireTestPage;
