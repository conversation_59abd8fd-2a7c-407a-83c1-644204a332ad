const applyComputedStyles = (nodes: Node[]) => {
  nodes.forEach((node) => {
    if (node instanceof HTMLElement) {
      Object.assign(node.style, window.getComputedStyle(node));
    }
    if (node.childNodes?.length) {
      applyComputedStyles(Array.from(node.childNodes));
    }
  });
};

export const cloneWithStyles = <T extends Node>(node: T) => {
  const clone = node.cloneNode(true) as T;
  applyComputedStyles([clone]);
  return clone;
};

export const convertHtmlToXhtml = (htmlContent: string) => {
  // 使用 DOMParser 解析 HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  // 转换文档为字符串
  const xhtmlString = new XMLSerializer().serializeToString(doc);
  // 替换不符合 XHTML 的部分
  return xhtmlString
    .replace(/<br>/g, '<br/>')
    .replace(/<img(.*?)>/g, '<img$1 />') // 将 <img> 转为自闭合标签
    .replace(/<input(.*?)>/g, '<input$1 />') // 处理其他类似标签
    .replace(/<strong>/g, '<b>')
    .replace(/<\/strong>/g, '</b>')
    .replace(/&nbsp;/g, '')
    .replace(/   /g, '')
    .replace(/  /g, '')
    .replace(/\u00A0/g, '') // 不换行空格(Non-breaking space)
    .replace(/\u2002/g, '') // 半角空格(En Space)
    .replace(/\u2003/g, '') // 全角空格(Em Space)
    .replace(/\u2004/g, '') // 三分之一空格(Three-Per-Em Space)
    .replace(/\u2005/g, '') // 四分之一空格(Four-Per-Em Space)
    .replace(/\u2006/g, '') // 六分之一空格(Six-Per-Em Space)
    .replace(/\u2007/g, '') // 数字空格(Figure Space)
    .replace(/\u2008/g, '') // 标点空格(Punctuation Space)
    .replace(/\u2009/g, '') // 窄空格(Thin Space)
    .replace(/\u200A/g, '') // 头发空格(Hair Space)
    .replace(/\u200B/g, '') // 零宽空格(Zero Width Space)
    .replace(/\u202F/g, '') // 窄不换行空格(Narrow No-Break Space)
    .replace(/\u205F/g, '') // 中空格(Medium Mathematical Space)
    .replace(/\u3000/g, ''); // 全角空格(Ideographic Space)
};
