import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  CloseButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody
} from '@chakra-ui/react';
import { Select } from 'antd';
import {
  getClassTeachers,
  getTeacherList,
  updateTeacherBatch
} from '@/api/tenant/teamManagement/administration';
import {
  TeacherInfo,
  UpdateTeacherBatchParams,
  TeacherConfigItem
} from '@/types/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';

interface SubjectTeachers {
  [key: string]: string[]; // 存储教师姓名数组
}

// 科目配置接口
interface SubjectConfig {
  key: string;
  label: string;
}

interface TeacherManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRefresh?: () => void;
  classId?: string;
  semesterId?: string;
  initialData?: SubjectTeachers;
  className?: string;
}

const TeacherManagementModal: React.FC<TeacherManagementModalProps> = ({
  isOpen,
  onClose,
  onRefresh,
  classId,
  semesterId,
  initialData,
  className
}) => {
  const [dataLoading, setDataLoading] = useState(false); // 数据加载状态
  const [saveLoading, setSaveLoading] = useState(false); // 保存状态
  const [subjectTeachers, setSubjectTeachers] = useState<SubjectTeachers>({});
  const [subjects, setSubjects] = useState<SubjectConfig[]>([]);
  const [teacherList, setTeacherList] = useState<TeacherInfo[]>([]);
  const [teacherListLoading, setTeacherListLoading] = useState(false);
  const [originalClassTeachersData, setOriginalClassTeachersData] = useState<any[]>([]);

  // 获取班级任课老师数据
  useEffect(() => {
    const fetchClassTeachers = async () => {
      if (classId && semesterId && isOpen) {
        try {
          setDataLoading(true);

          // 并发获取班级任课老师数据和教师列表
          const [response, teacherListResponse] = await Promise.all([
            getClassTeachers(classId!, semesterId!),
            getTeacherList(classId!)
          ]);

          console.log('班级任课老师数据:', response);
          console.log('在职老师列表:', teacherListResponse);

          // 设置教师列表
          if (teacherListResponse && Array.isArray(teacherListResponse)) {
            setTeacherList(teacherListResponse);
          }

          // 根据API返回的数据结构转换为组件需要的格式
          if (response && Array.isArray(response)) {
            // 保存原始数据，用于后续获取subjectId
            setOriginalClassTeachersData(response);

            const convertedData: SubjectTeachers = {};
            const dynamicSubjects: SubjectConfig[] = [];

            // 遍历接口返回的数据，动态生成科目配置
            response.forEach((item) => {
              // 提取教师姓名数组
              const teacherNames = item.tmbUserList.map((user) => user.userName);

              // 根据teachTypeName分类
              if (item.teachTypeName === '班主任') {
                const key = 'headTeacher';
                convertedData[key] = teacherNames;
                // 添加到科目配置中（如果还没有）
                if (!dynamicSubjects.find((s) => s.key === key)) {
                  dynamicSubjects.push({ key, label: '班主任' });
                }
              } else if (item.teachTypeName === '学科教师') {
                // 学科教师根据subjectName动态生成key
                if (item.subjectName) {
                  const key = item.subjectName; // 直接使用科目名称作为key
                  convertedData[key] = teacherNames;
                  // 添加到科目配置中（如果还没有）
                  if (!dynamicSubjects.find((s) => s.key === key)) {
                    dynamicSubjects.push({ key, label: item.subjectName });
                  }
                }
              }
            });

            setSubjectTeachers(convertedData);
            setSubjects(dynamicSubjects);
          }
        } catch (error) {
          console.error('获取数据失败:', error);
          Toast.error('获取数据失败');
        } finally {
          setDataLoading(false);
        }
      } else if (initialData && isOpen) {
        setSubjectTeachers(initialData);
      } else if (isOpen) {
        // 设置默认数据
        setSubjectTeachers({
          headTeacher: [],
          chinese: [],
          math: [],
          english: []
        });
      }
    };

    fetchClassTeachers();
  }, [classId, semesterId, initialData, isOpen]);

  // 获取在职老师列表（现在在 useEffect 中已经加载，这个函数作为备用）
  const fetchTeacherList = async () => {
    if (!classId || teacherList.length > 0) return; // 如果已有数据则不重复加载

    try {
      setTeacherListLoading(true);
      const response = await getTeacherList(classId);
      console.log('补充加载在职老师列表:', response);
      // 根据实际API返回结构处理数据 - 接口直接返回数组
      if (response && Array.isArray(response)) {
        setTeacherList(response);
      }
    } catch (error) {
      console.error('获取在职老师列表失败:', error);
      Toast.error('获取在职老师列表失败');
    } finally {
      setTeacherListLoading(false);
    }
  };

  // 处理科目教师选择变化
  const handleSubjectTeachersChange = (subjectKey: string, selectedTeacherIds: string[]) => {
    setSubjectTeachers((prev) => ({
      ...prev,
      [subjectKey]: selectedTeacherIds
    }));
  };

  const handleSave = async () => {
    if (!classId || !semesterId) {
      Toast.error('缺少必要参数');
      return;
    }

    try {
      setSaveLoading(true);

      // 构建所有教师配置项
      const teacherConfigList: TeacherConfigItem[] = [];

      subjects.forEach((subject) => {
        const selectedTeacherNames = subjectTeachers[subject.key] || [];

        // 根据教师姓名找到对应的 tmbId
        const tmbIds = selectedTeacherNames
          .map((teacherName) => {
            const teacher = teacherList.find((t) => t.name === teacherName);
            return teacher ? teacher.tmbId : null;
          })
          .filter((tmbId): tmbId is number => tmbId !== null);

        // 确定 teachType 和 subjectId
        if (subject.key === 'headTeacher') {
          // 班主任配置 - 无论是否有选中的老师都要添加配置项
          teacherConfigList.push({
            deptId: parseInt(classId),
            teachType: 5, // 班主任
            semesterId: parseInt(semesterId),
            tmbIds // 可能为空数组，表示删除所有班主任
          });
        } else {
          // 任课老师配置 - 从原始班级任课老师数据中获取科目ID
          const originalSubjectData = originalClassTeachersData.find(
            (item) => item.subjectName === subject.label && item.teachTypeName === '学科教师'
          );

          if (originalSubjectData && originalSubjectData.subjectId) {
            // 任课老师也无论是否有选中的老师都要添加配置项
            teacherConfigList.push({
              deptId: parseInt(classId),
              teachType: 7, // 任课老师
              semesterId: parseInt(semesterId),
              tmbIds, // 可能为空数组，表示删除该科目的所有老师
              subjectId: originalSubjectData.subjectId
            });
          } else {
            console.warn(`科目 ${subject.label} 未找到对应的科目ID，跳过配置`);
          }
        }
      });

      // 构建最终的请求参数
      const updateParams: UpdateTeacherBatchParams = {
        list: teacherConfigList
      };

      // 一次性调用批量更新接口
      await updateTeacherBatch(updateParams);

      Toast.success('保存成功');

      // 调用刷新回调
      if (onRefresh) {
        onRefresh();
      }

      onClose();
    } catch (error) {
      console.error('保存教师信息失败:', error);
      Toast.error('保存失败');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} isCentered size="4xl">
      <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
      <ModalContent bg="white" borderRadius="8px" minW="800px" maxW="800px">
        <ModalBody p={0}>
          {/* 标题栏 */}
          <Flex
            justifyContent="space-between"
            alignItems="center"
            p="24px 24px 20px 24px"
            borderBottom="1px solid #F2F3F5"
          >
            <Text fontSize="18px" fontWeight="500" color="#1D2129">
              教师管理
            </Text>
            <CloseButton onClick={handleCancel} />
          </Flex>

          {/* 表单内容 */}
          <Box
            p="32px"
            maxH="400px"
            overflowY="auto"
            css={{
              '&::-webkit-scrollbar': {
                width: '6px'
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '3px'
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '3px'
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#a8a8a8'
              }
            }}
          >
            {dataLoading ? (
              <Flex justifyContent="center" alignItems="center" minH="200px">
                <Text color="#6B7280" fontSize="14px">
                  加载中...
                </Text>
              </Flex>
            ) : (
              subjects.map((subject, index) => (
                <Box key={subject.key} mb={index === subjects.length - 1 ? '40px' : '24px'}>
                  <Flex alignItems="center" minH="48px">
                    <Text
                      color="#1D2129"
                      fontSize="14px"
                      fontWeight="400"
                      minW="80px"
                      textAlign="left"
                    >
                      {subject.label}：
                    </Text>
                    <Box flex="1">
                      <Select
                        mode="multiple"
                        placeholder={`请选择${subject.label}`}
                        value={subjectTeachers[subject.key]}
                        onChange={(selectedIds) =>
                          handleSubjectTeachersChange(subject.key, selectedIds)
                        }
                        onDropdownVisibleChange={(open) => {
                          // 教师列表现在在弹窗打开时就会加载，这里作为备用加载
                          if (open && teacherList.length === 0) {
                            fetchTeacherList();
                          }
                        }}
                        loading={teacherListLoading}
                        style={{
                          width: '100%',
                          minHeight: '48px',
                          borderRadius: '8px'
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        maxTagCount="responsive"
                        tagRender={(props) => {
                          const { label, closable, onClose } = props;
                          return (
                            <div
                              style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                backgroundColor: '#F3F4F6',
                                border: '1px solid #E5E7EB',
                                borderRadius: '6px',
                                padding: '4px 8px',
                                margin: '2px',
                                fontSize: '14px',
                                color: '#374151',
                                height: '32px'
                              }}
                            >
                              <span style={{ color: '#7C3AED' }}>{label}</span>
                              {closable && (
                                <span
                                  onClick={onClose}
                                  style={{
                                    marginLeft: '6px',
                                    cursor: 'pointer',
                                    color: '#9CA3AF',
                                    fontSize: '14px',
                                    fontWeight: 'bold'
                                  }}
                                >
                                  ×
                                </span>
                              )}
                            </div>
                          );
                        }}
                        suffixIcon={
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path
                              d="M3 4.5L6 7.5L9 4.5"
                              stroke="#9CA3AF"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        }
                      >
                        {teacherList.map((teacher) => (
                          <Select.Option key={teacher.id} value={teacher.name}>
                            {teacher.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Box>
                  </Flex>
                </Box>
              ))
            )}
          </Box>

          {/* 操作按钮 */}
          <Box p="0 32px 32px 32px">
            <Flex justifyContent="flex-end" gap="16px">
              <Button
                variant="outline"
                borderColor="#E5E6EB"
                color="#6B7280"
                bg="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                _hover={{
                  borderColor: '#D1D5DB',
                  bg: '#F9FAFB'
                }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                bg="#7C3AED"
                color="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                isLoading={saveLoading}
                loadingText="保存中..."
                _hover={{
                  bg: '#6D28D9'
                }}
                _active={{
                  bg: '#5B21B6'
                }}
                onClick={handleSave}
              >
                保存
              </Button>
            </Flex>
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default TeacherManagementModal;
