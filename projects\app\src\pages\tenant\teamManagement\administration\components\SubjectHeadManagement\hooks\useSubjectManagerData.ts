import { useState, useEffect, useMemo } from 'react';
import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import { getSubjectManagerTree } from '@/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';
import { SchoolData, TopLevelData } from '../types';
import { DEPARTMENT_KEYWORDS } from '../constants';

/**
 * 学科负责人数据管理 Hook
 */
export const useSubjectManagerData = (semesterId: string) => {
  const [treeData, setTreeData] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取学科负责人数据
  const fetchSubjectManagerData = async () => {
    if (!semesterId) return;

    setLoading(true);
    try {
      const res = await getSubjectManagerTree(semesterId);
      console.log('学科负责人数据:', res);

      if (res?.treeList) {
        // 详细记录每层数据的结构
        if (res.treeList.length > 0) {
          // 检查第一层结构 - 科目层
          console.log('第一层数据结构(科目层)示例:', {
            id: res.treeList[0].id,
            deptId: res.treeList[0].deptId,
            deptName: res.treeList[0].deptName,
            subjectName: res.treeList[0].subjectName,
            subjectId: res.treeList[0].subjectId,
            teachType: (res.treeList[0] as any).teachType
          });

          // 检查第二层结构 - 学段层
          if (res.treeList[0].children && res.treeList[0].children.length > 0) {
            console.log('第二层数据结构(学段层)示例:', {
              id: res.treeList[0].children[0].id,
              deptId: res.treeList[0].children[0].deptId,
              deptName: res.treeList[0].children[0].deptName,
              subjectName: res.treeList[0].children[0].subjectName,
              subjectId: res.treeList[0].children[0].subjectId,
              teachType: (res.treeList[0].children[0] as any).teachType
            });

            // 检查第三层结构 - 年级层
            if (
              res.treeList[0].children[0].children &&
              res.treeList[0].children[0].children.length > 0
            ) {
              console.log('第三层数据结构(年级层)示例:', {
                id: res.treeList[0].children[0].children[0].id,
                deptId: res.treeList[0].children[0].children[0].deptId,
                deptName: res.treeList[0].children[0].children[0].deptName,
                subjectName: res.treeList[0].children[0].children[0].subjectName,
                subjectId: res.treeList[0].children[0].children[0].subjectId,
                teachType: (res.treeList[0].children[0].children[0] as any).teachType
              });
            }
          }
        }

        setTreeData(res.treeList);
      } else {
        console.warn('API返回了空的treeList数据');
        setTreeData([]);
      }
    } catch (err) {
      console.error('获取学科负责人数据失败:', err);
      Toast.error('获取数据失败，请重试');
      setTreeData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubjectManagerData();
  }, [semesterId]);

  // 提取第一层数据中的科目和总负责人
  const topLevelData: TopLevelData = useMemo(() => {
    // 从第一层数据中提取科目和负责人，保持原始顺序
    const subjects: string[] = [];
    const leaders: Record<string, TmbUser[]> = {};

    // 遍历第一层数据
    treeData.forEach((item) => {
      if (item.subjectName) {
        // 添加科目，保持原始顺序
        subjects.push(item.subjectName);
        // 添加该科目的负责人，确保与科目一一对应
        leaders[item.subjectName] = item.tmbUserList || [];
      }
    });

    return {
      topLevelSubjects: subjects, // 不再排序，保持原始顺序
      topLevelLeaders: leaders
    };
  }, [treeData]);

  // 处理学校数据 - 按学部分类学校数据，并提取各学段的 leaders
  const schoolData: SchoolData & {
    primaryLeaders: Record<string, TmbUser[]>;
    juniorLeaders: Record<string, TmbUser[]>;
    seniorLeaders: Record<string, TmbUser[]>;
    otherLeaders: Record<string, TmbUser[]>;
  } = useMemo(() => {
    const result = {
      primary: [] as Department[],
      junior: [] as Department[],
      senior: [] as Department[],
      other: [] as Department[],
      primaryLeaders: {} as Record<string, TmbUser[]>,
      juniorLeaders: {} as Record<string, TmbUser[]>,
      seniorLeaders: {} as Record<string, TmbUser[]>,
      otherLeaders: {} as Record<string, TmbUser[]>
    };

    // 提取接口中的第二层数据 - 学段数据
    treeData.forEach((subject) => {
      if (!subject.subjectName || !Array.isArray(subject.children)) return;

      // 遍历学段数据
      subject.children.forEach((phase) => {
        // 根据学段名称判断所属部门
        const phaseName = phase.deptName || '';
        const subjectName = subject.subjectName;

        if (DEPARTMENT_KEYWORDS.PRIMARY.some((keyword) => phaseName.includes(keyword))) {
          result.primary.push(phase);
          // 提取小学部该科目的学段负责人
          result.primaryLeaders[subjectName] = phase.tmbUserList || [];
        } else if (DEPARTMENT_KEYWORDS.JUNIOR.some((keyword) => phaseName.includes(keyword))) {
          result.junior.push(phase);
          // 提取初中部该科目的学段负责人
          result.juniorLeaders[subjectName] = phase.tmbUserList || [];
        } else if (DEPARTMENT_KEYWORDS.SENIOR.some((keyword) => phaseName.includes(keyword))) {
          result.senior.push(phase);
          // 提取高中部该科目的学段负责人
          result.seniorLeaders[subjectName] = phase.tmbUserList || [];
        } else {
          result.other.push(phase);
          // 提取其他部门该科目的学段负责人
          result.otherLeaders[subjectName] = phase.tmbUserList || [];
        }
      });
    });

    return result;
  }, [treeData]);

  return {
    treeData,
    loading,
    topLevelData,
    schoolData,
    refetchData: fetchSubjectManagerData
  };
};

// Next.js 页面组件默认导出
const UseSubjectManagerDataPage = () => {
  return null;
};
export default UseSubjectManagerDataPage;
