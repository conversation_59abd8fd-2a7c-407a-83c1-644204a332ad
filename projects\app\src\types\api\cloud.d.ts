import {
  AuditorStatusEnum,
  BizType<PERSON>num,
  DownloadRecordStatus,
  FileTypeEnum,
  MoveTypeEnum,
  PrivilegeEnum,
  SearchTypeParams,
  SharedPersonnelTypeParams,
  ShareJurisdictionEnum,
  StatisTypeEnum,
  StatusEnum
} from '@/constants/api/cloud';
import { RequestPageParams } from '..';

// 云空间
export type SpaceType = {
  id: string;
  spaceName: string;
  parentId: string;
  hasChildren?: boolean;
  privileges: PrivilegeEnum[];
  sortNo: number;
  description?: string;
  shareType: number;
  children?: SpaceType[];
  type?: MoveTypeEnum;
  folderName?: string;
};

export type GetSpaceListProps = {
  parentId: string;
  shareType?: number;
  isQuerySubdirectory?: number;
  privilege?: PrivilegeEnum;
};
export type GetSpaceViewListProps = {
  tenantId: string;
  isAdmin: 0 | 1;
  tmbId?: string;
} & GetSpaceListProps;

export interface FileInfo {
  createTime: string;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: string;
  isDeleted: number;
  updateTime: string;
}

// 云文件
export type FileType = {
  cloudLabel?: string[];
  fileType: FileTypeEnum;
  bizType?: BizTypeEnum;
  bizId?: number;
  id: string;
  parentId: string;
  fileName: string;
  fileSize?: number;
  fileKey?: string;
  tmbId?: string;
  uploader?: string;
  auditor?: string;
  privileges?: PrivilegeEnum[];
  updateTime: string;
  auditorStatus?: AuditorStatusEnum;
  auditTime?: string;
  file?: FileInfo;
  files?: FileInfo;
  type?: MoveTypeEnum;
  shareType?: number;
  rowKey?: string;
  auditRemark?: string;
  locationPath?: locationPathType[];
  auditorId?: string;
  description?: string;
  highlight?: {
    fileContent: string[];
  };
  fileParseStatus?: FileParseStatusEnum;
  onRefresh?: () => void;
};

export type GetSpaceFilePageProps = {
  parentId: string;
  shareType?: number;
  fileType?: FileTypeEnum;
  searchKey?: string;
  privilege?: PrivilegeEnum;
  sortOrder?: number;
} & RequestPageParams;

export type SpacePrimaryListProps = {
  parentId: string;
} & RequestPageParams;

export type GetSpaceFileViewPageProps = {
  tenantId: string;
  isAdmin: 0 | 1;
  tmbId?: string;
} & GetSpaceFilePageProps;

export type RemoveSpaceFileBatchProps = { list: { id: string; fileType: FileTypeEnum }[] };

export type GetFoldVersionFilePageProps = { id: string } & RequestPageParams;

export type shareObjectsType = {
  tmbId: string;
  objectType: SharedPersonnelTypeParams;
  privilege: PrivilegeEnum;
};

export type AddSpaceProps = {
  parentId: string;
  spaceName: string;
  description?: string;
  shareObjects?: shareObjectsType[];
};

export type UpdateSpaceProps = {
  id: string;
  spaceName: string;
  description?: string;
};

export type UpdateSpaceParentProps = {
  id: string;
  parentId: string;
};

export type BatchUpdateParentProps = {
  parentId: string;
  ids: string[];
  folderIds: string[];
  spaceIds: string[];
};

export type RecyclePageType = {
  bizId: number;
  bizType: number;
  createTime: string;
  deleterId: number;
  deleterName: string;
  fileName: string;
  fileType: number;
  id: string;
  isDeleted: number;
  location: string;
  operateType: number | null;
  tenantId: number;
  tmbId: number;
  updateTime: string;
  file?: FileInfo;
  type?: MoveTypeEnum;
  parentSpaceId?: string;
};

export type RecycleSubListPageType = {
  id: string;
  createTime: string;
  updateTime: string;
  deleteTime: string;
  isDeleted: boolean | null;
  tenantId: number | null;
  type: string | null;
  folderSource: string | null;
  uploadStatus: string | null;
  parentId: number | null;
  sortNo: number | null;
  folderName: string;
  tmbId: string | null;
  description: string | null;
  fileType: number;
  fileName: string;
  fileSize: number;
  status: string | null;
  isUpdateDeleted: boolean | null;
  deleterId: string | null;
  file?: FileInfo;
  parentSpaceId: string | null;
  bizType?: BizTypeEnum;
};

export type SortSpaceProps = { id: string; sortNo: number }[];

export type AuditorType = {
  id: string;
  userName: string;
  deptName: string;
  spaceId: string;
  tmbId: string;
  updateTime: string;
};

export type AddSpaceFolderProps = {
  parentId: string;
  spaceName: string;
};

export type UpdateSpaceFolderProps = {
  id: string;
  spaceName: string;
};

export type CreateTenantUploadFolderProps = {
  parentId: string;
  spaceName: string;
  isFirst: 0 | 1;
  isAuditRoot: 0 | 1;
};

export type FinishTenantUploadFolderProps = {
  id: string;
  isUpdate: 0 | 1;
  oldFolderId?: string;
};

export type CreateMyUploadFolderProps = {
  parentId: string;
  folderName: string;
  isFirst: 0 | 1;
};

export type FinishMyUploadFolderProps = {
  id: string;
  isUpdate: 0 | 1;
  oldFolderId?: string;
};

export type GetDownloadSizeProps = {
  bizType: BizTypeEnum;
  spaceIds?: string[];
  fileIds?: string[];
  tenantId: string;
  tmbId: string;
  auditStatus: 1 | 2; // 1 只下载审核通过的文件 2 下载全部文件
};

export type DownloadSizeType = {
  fileSize: string;
};

export type CreateDownloadZipProps = {
  bizType: BizTypeEnum;
  id: string;
  spaceIds?: string[];
  fileIds?: string[];
  tenantId: string;
  range: 1 | 2; // 1 批量下载时使用，2 单个文件夹下载
  version: 1 | 2; // 1 最新版本 2 历史版本
  auditStatus: 1 | 2; // 1 只下载审核通过的文件 2 下载全部文件
};

export type GetDownloadActiveCountProps = {
  bizType?: BizTypeEnum;
};

export type DownloadActiveCountType = {
  num: number;
};

export type GetDownloadRecordPageProps = {
  bizType?: BizTypeEnum;
  downloadStatus?: DownloadRecordStatus;
} & RequestPageParams;

export type DownloadRecordType = {
  id: string;
  bizType: BizTypeEnum;
  cloudFileId: string;
  cloudFolderId: string;
  cloudSpaceId: string;
  downloadStatus: DownloadRecordStatus;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: FileTypeEnum;
  createTime: string;
  updateTime: string;
  files: {
    fileName: string;
    fileUrl: string;
  };
};

export type CreateDownloadRecordProps = {
  bizType: BizTypeEnum;
  cloudFileId: string;
  downloadStatus: DownloadRecordStatus;
  fileKey: string;
  fileType: FileTypeEnum;
  tenantId: string;
};

export type UpdateDownloadRecordProps = {
  id: string;
  downloadStatus: DownloadRecordStatus;
};

export type AddMyFolderProps = {
  parentId: string;
  folderName: string;
};

export type RemoveMyFileBatchProps = { list: { id: string; fileType: FileTypeEnum }[] };

export type RenameMyFolderProps = {
  id: string;
  folderName: string;
};

export type GetMyFilePageProps = {
  parentId: string;
  fileType?: FileTypeEnum;
  sortOrder?: number;
} & RequestPageParams;

export type GetMyFlatFilePageProps = {
  searckKey?: string;
} & RequestPageParams;

export type MyFlatFileFolderType = {
  id: string;
  parentId: string;
  folderName: string;
  hasChildren: boolean;
  children: MyFlatFileFolderType[];
};

export type MyFlatFileType = { folderNames: MyFlatFileFolderType[] } & FileType;

export type UploadCloudFileProps = {
  spaceId: string;
  sourceFileIds: string[];
  isAuditRoot: 0 | 1;
};

export type BatchRecoveryParams = {
  id: string;
  parentId?: string;
};

// 请求参数类型
export interface CloudFileDetailRequest {
  bizType: number;
  fileType: number;
  id: string;
}

// 标签类型
export interface CloudLabel {
  labelId: number;
  labelName: string;
}

// 文件对象类型
export interface FileObject {
  // 根据实际的文件对象结构定义
}

// 请求参数类型
export interface CloudFileVersionRequest {
  bizType: number;
  fileType: number;
  id: string;
}

export interface LocationPathItem {
  id: number;
  name: string;
  parentId: number;
}

// 文件详情响应数据类型
export interface CloudFileDetailResponse {
  auditTime: string; // 审核时间
  auditorId: number; // 审核人Id
  auditorName: string; // 审核人名称
  auditorStatus: AuditorStatusEnum; // 审核状态
  bizType: BizTypeEnum; // 业务类型
  cloudFolderIds: number[]; // 文件夹ID数组
  cloudLabelList: CloudLabel[]; // 标签列表
  cloudSpaceIds: number[]; // 空间ID数组
  createTime: string; // 创建时间
  fileKey: string; // 文件key
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileType: FileTypeEnum; // 文件类型
  files: FileObject; // 文件对象
  id: string; // ID
  locationPath: LocationPathItem[]; // 文件位置路径
  parentId: number; // 父级文件夹ID
  tenantId: number; // 租户ID
  tmbId: number; // 上传租户人员ID
  tmbUserName: string; // 上传人
}

// 响应数据类型
export interface CloudFileVersionResponse {
  createTime: string;
  fileName: string;
  fileType: number;
  files: {
    createTime: string;
    fileJson: string;
    fileKey: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    fileUrl: string;
    id: string;
    isDeleted: number;
    updateTime: string;
  };
  folderId: number;
  id: string;
  operateType: OperateTypeEnum;
  locationPath: LocationPathItem[];
}

// 请求参数类型
export interface CloudFileShareRequest {
  fileId: string;
  searchKey?: string;
}

// 响应数据类型
export interface CloudFileShareResponse {
  createTime: string;
  deptId: number;
  deptName: string;
  fileId: number;
  id: string;
  isDeleted: number;
  objectType: ObjectTypeEnum;
  privilege: PrivilegeEnum;
  status: StatusEnum;
  tenantId: number;
  tmbId: number;
  updateTime: string;
  userName: string;
}

// 请求参数类型
export interface CloudSpaceShareRequest {
  spaceId: string;
  searchKey?: string;
}

// 响应数据类型
export interface CloudSpaceShareResponse {
  createTime: string;
  deptId: number;
  deptName: string;
  deptSpaceKey: string;
  id: string;
  isDeleted: number;
  objectType: ObjectTypeEnum;
  privilege: PrivilegeEnum;
  shareType: ShareTypeEnum;
  spaceId: number;
  status: StatusEnum;
  tenantId: number;
  tmbId: number;
  type: number;
  updateTime: string;
  userName: string;
  userSpaceKey: string;
}

// types/api/label.ts

export interface CreateLabelRequest {
  labelName: string;
}

export type LabelItem = {
  createTime: string;
  id: number;
  isDeleted: number;
  labelName: string;
  status: number;
  updateTime: string;
};

export type CreateLabelResponse = {};
export interface ListLabelsRequest {
  labelName: string;
}

export type ListLabelsResponse = LabelItem[];

export interface SetLabelRequest {
  bizType: BizTypeEnum;
  fileType: FileTypeEnum;
  id: string;
  labelIds: number[];
}

export type SetLabelResponse = {};

export interface GetSetLabelsRequest {
  bizType: BizTypeEnum;
  fileType: FileTypeEnum;
  id: number;
}

export type GetSetLabelsResponse = LabelItem[];

interface AuditRequestProps {
  auditRemark: string;
  auditorStatus: number;
  id: string;
  fileType: FileTypeEnum;
}

export type CloudNoticeListPageType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean | null;
  tenantId: number | null;
  noticeType: number;
  fileType: number;
  fileId: number;
  senderTmbId: number;
  receiverTmbId: number;
  content: string;
  auditorId: number;
  auditRemark: string;
  oldFolderId: number;
  fileName: string;
  parentId: number;
  parentName: string;
  oldFolderName: string;
  createrId: number;
  creater: string;
  operator: string;
};

export type CloudNoticeListPageParams = {
  current: number;
  size: number;
};

export type AuditCountParams = {
  type?: number;
};

export type AuditCountType = {
  num;
};

export type AuditListParams = {
  auditorStatus?: AuditorStatusEnum;
  keyword?: string;
  type: string;
};

export type AuditSubListParams = {
  auditorStatus?: AuditorStatusEnum;
  keyword?: string;
  cloudSpaceId: string;
};

export type AuditListType = {
  fileName: string;
  tmbUserName: string;
  createTime: string;
  fileSize: string;
  auditorName: string;
  auditTime: string;
  auditorStatus: AuditorStatusEnum;
};

interface CloudSpaceVOParams {
  id: string;
  fileType: number;
}

export type FolderMixBatchDeleteParams = {
  list: CloudSpaceVOParams[];
};

interface auditRequestParams {
  id: string;
  fileType: number;
}

export type DoAuditBatchParams = {
  auditRemark?: string;
  auditRequestList: auditRequestParams[];
  auditorStatus: number;
};

export type SpaceAuditorAddParams = {
  spaceIds: string[];
  auditorIds: string[];
};

export type SpaceAuditorListParams = {
  searchKey: string;
};

export type SpaceAuditorAddType = {
  deptName: string;
  spaceId: string;
  spaceIds: [];
  spaceNames: string[];
  status: number;
  tenantId: string;
  userName: string;
  updateTime: string;
  tmbId: string;
};
export type SpaceAuditorListType = {
  id: string;
  deptName: string;
  spaceId: string;
  spaceIds: [];
  spaceNames: string[];
  status: number;
  tenantId: string;
  userName: string;
  updateTime: string;
  tmbId: string;
};

export type NoticeNotReadNumType = {
  num: number;
};

interface shareObjectsParams {
  objectType: number;
  tmbId?: string;
  deptId?: string;
  privilege?: PrivilegeEnum;
}

interface ShareFilesParams {
  spaceId: string;
  fileType: number;
}

export type SpaceShareAddParams = {
  spaceId: string;
  shareObjects: shareObjectsParams[];
};

export type ShareDeleteParams = {
  id: string;
};
export type ShareUpdatePrivilegeParams = {
  id: string;
  privilege: PrivilegeEnum;
};

export type FileShareAddParams = {
  fileId: string;
  shareObjects: shareObjectsParams[];
};

export type FilesMixFolderShareParams = {
  files: ShareFilesParams[];
  shareObjects: shareObjectsParams[];
};

export type CloudRecyclePageProps = {
  bizType: number;
  current: number;
  fileType: number;
  id: string;
  parentId: number;
  searchKey: string;
  size: number;
};

export type CloudRecycleSubListPageProps = {
  bizType: number;
  current: number;
  fileType: number;
  id: string;
  parentId: number;
  searchKey: string;
  size: number;
};

export type GetUsageStatsProps = {
  bizType?: BizTypeEnum;
  statisType: StatisTypeEnum;
};

export type UpdateFileParentProps = {
  id: string;
  spaceId?: string;
  folderId?: string;
};

export type UpdateFolderParentProps = {
  id: string;
  parentId: string;
};

export interface FileSearchHistoryType {
  id: string;
  searchContent: string;
  status: number;
  tenantId: string;
  tmbId: string;
}

export interface FileGlobalListPageParams {
  searchType: SearchTypeParams;
  searchFileType?: number;
  updateTimeType?: number;
  searchKey: string;
  current?: number;
  size?: number;
  bizType?: BizTypeEnum;
}

export interface FileSearchMediaWordParams {
  fileId: string;
  searchKey: string;
}

export type locationPathType = {
  id: string;
  name: string;
  parentId: string;
  fileType?: FileTypeEnum;
  bizType: BizTypeEnum;
};

export interface SearchFileType {
  createTime: string;
  fileJson: string;
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: string;
  isDeleted: number;
  updateTime: string;
}
export interface FileGlobalListPageType {
  id: string;
  bizType: BizTypeEnum;
  fileKey: string;
  file: SearchFileType;
  fileName: string;
  fileSize: number;
  fileSuffix: string;
  fileType: FileTypeEnum;
  folderName: string;
  highlight: {
    fileContent: string[];
  };
  parentId: string;
  spaceName: string;
  uploader: string;
  updateTime: string;
  tenantId: string;
  tmbId: string;
}

export interface FileSearchMediaWordType {
  file_key: string;
  file: SearchFileType;
  file_id: string;
  file_name: string;
  file_content: string;
  highlight: {
    file_content: string[];
  };
  matchedWords: matchedWordsType[];
  words: wordsType[];
}

export type matchedWordsType = {
  end_time: number;
  start_time: number;
  word: string;
};
export type wordsType = {
  end_time: number;
  start_time: number;
  word: string;
};

export interface SpaceShareParentCloudSpaceListType {
  id: string;
  objectType: SharedPersonnelTypeParams;
  privilege: PrivilegeEnum;
  deptId: number | string;
  deptIds?: number[];
  tmbId: number | string;
  userName: string;
}

export interface CloudBatchEditPrivilegeParams {
  ids: string[];
  operateType: batchEditPrivilegeTypeEnum;
  privilege: PrivilegeEnum;
}

export interface SpaceAuditSwitchUpdateParams {
  id: string;
  status: StatusEnum;
}

export interface CloudSpaceAuditSwitchDetailType {
  id: string;
  status: StatusEnum;
}

export interface Update2DatasetParams {
  tenantDatasetId: string;
  cloudFileId: number | null;
}
export interface MergeFilePartsResponse {
  createTime: string;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: number;
  isDeleted: number;
  updateTime: string;
}

export interface UploadPartInfo {
  etag: string;
  lastModified: string;
  partNumber: number;
  requestId: string;
  responseHeaders: Record<string, unknown>;
  size: number;
  statusCode: number;
}

export interface UploadTaskInfo {
  createTime: string;
  currentPartNo: number;
  exitPartList: UploadPartInfo[];
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  finished: boolean;
  id: number;
  isDeleted: number;
  isPartUploadFinish: number;
  isSuccess: number;
  partCount: number;
  updateTime: string;
  uploadId: string;
}

export type SpaceTreeListType = {
  id: string;
  parentId: string;
  spaceName: string;
  tenantId: string;
  description: string;
  hasChildren: boolean;
  children?: SpaceTreeListType[];
};
