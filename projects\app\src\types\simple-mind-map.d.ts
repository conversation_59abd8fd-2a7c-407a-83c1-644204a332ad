declare module 'simple-mind-map' {
  export default class SimpleMindMap {
    constructor(options: any);
    setData(data: any): void;
    destroy(): void;
    on(event: string, callback: (data: any) => void): void;
    addPlugin(plugin: any): void;
  }
}

declare module 'simple-mind-map/src/plugins/Select.js' {
  const SelectPlugin: any;
  export default SelectPlugin;
}

declare module 'simple-mind-map/src/plugins/KeyboardNavigation.js' {
  const KeyboardNavigationPlugin: any;
  export default KeyboardNavigationPlugin;
}

declare module 'simple-mind-map/src/plugins/Drag.js' {
  const DragPlugin: any;
  export default DragPlugin;
}

declare module 'simple-mind-map/src/plugins/Export.js' {
  const ExportPlugin: any;
  export default ExportPlugin;
}

declare module 'simple-mind-map/src/plugins/NodeImgAdjust.js' {
  const NodeImgAdjustPlugin: any;
  export default NodeImgAdjustPlugin;
}

declare module 'simple-mind-map/src/plugins/ExportXMind.js' {
  const ExportXMindPlugin: any;
  export default ExportXMindPlugin;
}

declare module 'simple-mind-map/src/plugins/AssociativeLine.js' {
  const AssociativeLinePlugin: any;
  export default AssociativeLinePlugin;
}

declare module 'simple-mind-map/src/plugins/OuterFrame.js' {
  const NodeImgAdjustPlugin: any;
  export default NodeImgAdjustPlugin;
}
