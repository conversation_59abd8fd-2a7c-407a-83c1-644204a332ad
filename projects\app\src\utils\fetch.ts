import { getErrText } from '@/utils/string';
import { getToken } from '@/utils/auth';
import dayjs from 'dayjs';
import {
  // refer to https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web
  EventStreamContentType,
  fetchEventSource
} from '@fortaine/fetch-event-source';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@/fastgpt/global/core/workflow/runtime/constants';
import { ChatHistoryItemResType } from '@/fastgpt/global/core/chat/type';
import { StartChatFnProps } from '@/components/ChatBox/type';
import { getErrorMessage, handleErrorMessage } from './api';

type StreamFetchProps = {
  url?: string;
  data: Record<string, any>;
  onMessage: StartChatFnProps['generatingMessage'];
  abortCtrl: AbortController;
};
export type StreamResponseType = {
  responseText: string;
  reasoningText?: string;
  isInteractive?: boolean;
  [DispatchNodeResponseKeyEnum.nodeResponse]: ChatHistoryItemResType[];
};
class FatalError extends Error {}

export const streamFetch = ({
  url = '/huayun-ai/client/chat/completions',
  data,
  onMessage,
  abortCtrl
}: StreamFetchProps) =>
  new Promise<StreamResponseType>(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      abortCtrl.abort('Time out');
    }, 60000 * 5);

    // response data
    let responseText = '';
    let reasoningText = '';
    let isInteractive = false;
    let responseQueue: (
      | {
          event:
            | SseResponseEventEnum.fastAnswer
            | SseResponseEventEnum.answer
            | SseResponseEventEnum.answerDone;
          text?: string;
          reasoningText?: string;
        }
      | {
          event:
            | SseResponseEventEnum.toolCall
            | SseResponseEventEnum.toolParams
            | SseResponseEventEnum.toolResponse;
          [key: string]: any;
        }
    )[] = [];
    let errMsg = '';
    let responseData: ChatHistoryItemResType[] = [];
    let finished = false;

    const finish = () => {
      if (errMsg) {
        return failedFinish();
      }
      return resolve({
        responseText,
        responseData,
        reasoningText,
        isInteractive
      });
    };
    const failedFinish = (err?: any) => {
      finished = true;
      if (err?.msg) {
        errMsg = err?.msg;
      }
      const message =
        err || errMsg ? getErrText(err, errMsg || '响应过程出现异常~') : '对话出现异常';

      const modifiedData = getErrorMessage(message, url) || message;

      reject({
        message: modifiedData || message,
        responseText,
        reasoningText
      });
      console.log('err?.msg', err?.msg);
      if (err?.msg !== '对话数量已达上限' && err?.msg !== undefined) {
        handleErrorMessage({
          errorMessage: message,
          url,
          tenantAppId: data.tenantAppId || data.chatAppId,
          value: data.value || data.content
        });
      }
    };

    const isAnswerEvent = (event: SseResponseEventEnum) =>
      event === SseResponseEventEnum.answer || event === SseResponseEventEnum.fastAnswer;

    // animate response to make it looks smooth
    function animateResponseText() {
      if (abortCtrl.signal.aborted) {
        responseQueue.forEach((item) => {
          onMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text;
          }
        });
        return finish();
      }

      if (responseQueue.length > 0) {
        const fetchCount = Math.max(1, Math.round(responseQueue.length / 30));
        for (let i = 0; i < fetchCount; i++) {
          const item = responseQueue[i];
          onMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text;
          }
        }
        responseQueue = responseQueue.slice(fetchCount);
      }

      if (finished && responseQueue.length === 0) {
        return finish();
      }

      requestAnimationFrame(animateResponseText);
    }
    // start animation
    animateResponseText();

    try {
      // auto complete variables
      const variables = data?.variables || {};
      variables.cTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const requestData = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream, application/json, */*',
          Authorization: `${getToken()}`
        },
        signal: abortCtrl.signal,
        body: JSON.stringify({
          variables,
          detail: true,
          stream: true,
          ...data
        })
      };

      // send request
      await fetchEventSource(url, {
        ...requestData,
        async onopen(res) {
          clearTimeout(timeoutId);
          const contentType = res.headers.get('content-type');

          // Handle non-stream responses
          if (contentType?.startsWith('application/json')) {
            const json = await res.clone().json();
            if (json.code !== 200) {
              return failedFinish(json);
            }
          }

          // not stream
          if (contentType?.startsWith('text/plain')) {
            return failedFinish(await res.clone().text());
          }

          // failed stream
          if (
            !res.ok ||
            !res.headers.get('content-type')?.startsWith(EventStreamContentType) ||
            res.status !== 200
          ) {
            try {
              if (res.status == 401 || res.status == 403) {
                window.location.href = '/login';
                failedFinish();
              } else if (res.status === 406) {
                window.location.href = '/login';
                failedFinish((await res.clone().json())?.msg);
              } else {
                failedFinish(await res.clone().json());
              }
            } catch {
              const errText = await res.clone().text();
              if (!errText.startsWith('event: error')) {
                failedFinish();
              }
            }
          }
        },
        onmessage({ event, data }) {
          if (data === '[DONE]') {
            responseQueue.push({
              event: SseResponseEventEnum.answerDone
            });
            return;
          }

          // parse text to json
          const parseJson = (() => {
            try {
              return JSON.parse(data);
            } catch (error) {
              return {};
            }
          })();

          if (event === SseResponseEventEnum.answer) {
            const text: string = parseJson?.choices?.[0]?.delta?.content || '';
            reasoningText = parseJson.choices?.[0]?.delta?.reasoning_content || '';
            for (const item of text) {
              responseQueue.push({
                event,
                text: item
              });
            }
            for (const item of reasoningText) {
              responseQueue.push({
                event,
                reasoningText: item
              });
            }
            // onMessage({
            //   event,
            //   reasoningText
            // });
          } else if (event === SseResponseEventEnum.fastAnswer) {
            const text: string = parseJson?.choices?.[0]?.delta?.content || '';
            responseQueue.push({
              event,
              text
            });
          } else if (
            event === SseResponseEventEnum.toolCall ||
            event === SseResponseEventEnum.toolParams ||
            event === SseResponseEventEnum.toolResponse
          ) {
            responseQueue.push({
              event,
              ...parseJson
            });
          } else if (event === SseResponseEventEnum.flowNodeStatus) {
            onMessage({
              event,
              ...parseJson
            });
          } else if (event === SseResponseEventEnum.flowResponses && Array.isArray(parseJson)) {
            responseData = parseJson;
          } else if (event === SseResponseEventEnum.updateVariables) {
            onMessage({
              event,
              variables: parseJson
            });
          } else if (event === SseResponseEventEnum.interactive) {
            responseQueue.push({
              event,
              ...parseJson
            });
            isInteractive = true;
          } else if (event === SseResponseEventEnum.error) {
            errMsg = getErrText(parseJson, '流响应错误');
          }
        },
        onclose() {
          finished = true;
        },
        onerror(err) {
          if (err instanceof FatalError) {
            throw err;
          }
          clearTimeout(timeoutId);
          failedFinish(getErrText(err));
        },
        openWhenHidden: true
      });
    } catch (err: any) {
      clearTimeout(timeoutId);

      if (abortCtrl.signal.aborted) {
        finished = true;

        return;
      }
      console.log(err, 'fetch error');

      failedFinish(err);
    }
  });
