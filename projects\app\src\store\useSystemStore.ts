import { set } from 'lodash';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { isMobile, isPhone, isTablet, mobileBreakpoint } from '@/utils/mobile';
import { getSystemFastData } from '@/api/system';
import { FastGPTFeConfigsType } from '@/fastgpt/global/common/system/types';
import { InitDateResponse } from '@/fastgpt/global/common/api/systemRes';
import {
  EmbeddingModelItemType,
  LLMModelItemType,
  ReRankModelItemType,
  STTModelType,
  TTSModelType
} from '@/fastgpt/global/core/ai/model';
import { FastGPTDataType } from '@/types/api/system';
import { ModelTypeEnum } from '@/fastgpt/global/core/ai/constants';
import { ClientUseGuidanceDetailType } from '@/types/api/app';
import { getSystemTenantGuideValidFinish, getClientUseGuidanceDetail } from '@/api/app';
import { ClientUseGuidanceMap, ClientUseGuidanceType } from '@/constants/common';
import { getSystemConfig } from '@/api/system';
type State = {
  lastRoute: string;
  setLastRoute: (e: string) => void;
  loading: boolean;
  setLoading: (val: boolean) => null;
  screenWidth: number;
  setScreenWidth: (val: number) => void;
  isPc?: boolean;
  initIsPc(val: boolean): void;

  isMobile?: boolean;
  isPhone?: boolean;
  isTablet?: boolean;

  feConfigs: FastGPTFeConfigsType;
  systemVersion: string;
  llmModelList: LLMModelItemType[];
  datasetModelList: LLMModelItemType[];
  embeddingModelList: EmbeddingModelItemType[];
  ttsModelList: TTSModelType[];
  reRankModelList: ReRankModelItemType[];
  sttModelList: STTModelType[];

  initStaticData: (e: InitDateResponse) => void;

  fastGPTData?: FastGPTDataType;
  getFastGPTData: (reload?: boolean) => Promise<FastGPTDataType>;
  systemConfig: Record<string, string> | undefined;
  getSystemConfig: () => Promise<Record<string, string> | undefined>;
  systemGuideMap: Partial<Record<ClientUseGuidanceType, boolean>>;
  getSystemGuideMap: (
    init?: boolean,
    data?: Partial<Record<ClientUseGuidanceType, boolean>>
  ) => Promise<Partial<Record<ClientUseGuidanceType, boolean>>>;
  setSystemGuideMap: (type: ClientUseGuidanceType, value: boolean) => void;
};

export const useSystemStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        lastRoute: '/',
        setLastRoute(e) {
          set((state) => {
            state.lastRoute = e;
          });
        },
        loading: false,
        setLoading: (val: boolean) => {
          set((state) => {
            state.loading = val;
          });
          return null;
        },
        screenWidth: 600,
        setScreenWidth(val: number) {
          set((state) => {
            state.screenWidth = val;
            state.isPc = !isMobile && val >= mobileBreakpoint;
          });
        },
        isPc: undefined,
        initIsPc(val: boolean) {
          if (get().isPc !== undefined) return;

          set((state) => {
            state.isPc = !isMobile && val;
          });
        },

        isMobile,
        isPhone,
        isTablet,

        feConfigs: {},
        systemVersion: '0.0.0',

        llmModelList: [],
        datasetModelList: [],
        embeddingModelList: [],
        ttsModelList: [],
        reRankModelList: [],
        sttModelList: [],
        initStaticData(res) {
          set((state) => {
            state.feConfigs = res.feConfigs || {};
            state.systemVersion = res?.systemVersion ?? state.systemVersion;
            state.llmModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.llm) ??
              state.llmModelList;
            state.datasetModelList = state.llmModelList.filter((item) => item.datasetProcess);
            state.embeddingModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.embedding) ??
              state.embeddingModelList;
            state.ttsModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.tts) ??
              state.ttsModelList;
            state.reRankModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.rerank) ??
              state.reRankModelList;
            state.sttModelList =
              res.activeModelList?.filter((item) => item.type === ModelTypeEnum.stt) ??
              state.sttModelList;
          });
        },

        fastGPTData: undefined,
        getFastGPTData: async (reload) => {
          if (!reload && get().fastGPTData) {
            return get().fastGPTData!;
          }
          const data = await getSystemFastData();
          set((state) => {
            state.fastGPTData = data;
          });
          return data;
        },

        systemGuideMap: {},
        getSystemGuideMap: async (
          init = true,
          data?: Partial<Record<ClientUseGuidanceType, boolean>>
        ) => {
          if (Object.keys(get().systemGuideMap).length > 0 && !init) return get().systemGuideMap;
          const typeList = Object.values(ClientUseGuidanceMap);

          const res = await Promise.all(
            typeList.map((type) => getSystemTenantGuideValidFinish(type.value))
          );
          let resMap = typeList.reduce(
            (acc, type, index) => {
              acc[type.value] = res[index];
              return acc;
            },
            {} as Record<ClientUseGuidanceType, boolean>
          );
          if (data) {
            resMap = { ...resMap, ...data };
          }
          set((state) => {
            state.systemGuideMap = resMap;
          });
          return res;
        },
        setSystemGuideMap: (type: ClientUseGuidanceType, value: boolean) => {
          set((state) => {
            state.systemGuideMap[type] = value;
          });
        },

        systemConfig: undefined,
        getSystemConfig: async () => {
          if (get().systemConfig) return get().systemConfig;
          const res = await getSystemConfig();
          set((state) => {
            state.systemConfig = res;
          });
          return res;
        }
      })),
      {
        name: 'systemStore',
        partialize: () => ({})
      }
    )
  )
);
