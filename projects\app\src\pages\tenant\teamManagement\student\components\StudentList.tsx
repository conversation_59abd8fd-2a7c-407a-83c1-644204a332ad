import React, { useState, useRef, useMemo, useEffect } from 'react';
import {
  Button,
  Flex,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure
} from '@chakra-ui/react';
import { vwDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { getStudentPage, changeStudentStatus, deleteStudent } from '@/api/student';
import type { StudentPageParams, StudentPageItem } from '@/types/student';
import type { PagingData } from '@/types/index';
import MyTable from '@/components/MyTable';
import type { MyTableRef } from '@/components/MyTable/types';
import StudentView from './SchoolStructureSelect/StudentView';
import MyBox from '@/components/common/MyBox';
import styles from '../student.module.scss';
import { StudentStatusMap } from '@/constants/student';

interface StudentListProps {
  onEdit?: (id: string) => void;
  searchParams?: any;
  refreshKey?: number;
}

// API适配器函数，将StudentPageResponse转换为PagingData<StudentPageItem>
const getStudentPageAdapter = async (params: StudentPageParams): Promise<PagingData<StudentPageItem>> => {
  const response = await getStudentPage(params);
  return {
    records: response.records,
    total: response.total,
    current: response.current,
    size: response.size,
    pages: Math.ceil(response.total / response.size)
  };
};

const StudentList: React.FC<StudentListProps> = ({ onEdit, searchParams = {}, refreshKey }) => {
  const [currentDeleteId, setCurrentDeleteId] = useState<number | null>(null);
  const [currentDisableId, setCurrentDisableId] = useState<number | null>(null);
  const [currentRecoverId, setCurrentRecoverId] = useState<number | null>(null);
  const [currentGraduateId, setCurrentGraduateId] = useState<number | null>(null);
  const [viewStudent, setViewStudent] = useState<any>(null);
  
  console.log('=== STUDENT LIST RENDERED ===', { searchParams, refreshKey });
  
  const {
    isOpen: isDeleteOpen,
    onOpen: onDeleteOpen,
    onClose: onDeleteClose
  } = useDisclosure();
  
  const {
    isOpen: isDisableOpen,
    onOpen: onDisableOpen,
    onClose: onDisableClose
  } = useDisclosure();
  
  const {
    isOpen: isRecoverOpen,
    onOpen: onRecoverOpen,
    onClose: onRecoverClose
  } = useDisclosure();
  
  const {
    isOpen: isGraduateOpen,
    onOpen: onGraduateOpen,
    onClose: onGraduateClose
  } = useDisclosure();
  
  const {
    isOpen: isViewOpen,
    onOpen: onViewOpen,
    onClose: onViewClose
  } = useDisclosure();
  
  const deleteCancelRef = useRef<HTMLButtonElement>(null);
  const disableCancelRef = useRef<HTMLButtonElement>(null);
  const recoverCancelRef = useRef<HTMLButtonElement>(null);
  const graduateCancelRef = useRef<HTMLButtonElement>(null);
  const tableRef = useRef<MyTableRef<StudentPageParams, StudentPageItem>>(null);

  // 监听searchParams变化，通过ref更新表格查询参数
  useEffect(() => {
    console.log('=== STUDENT LIST USE EFFECT TRIGGERED ===', { searchParams, refreshKey });
    if (tableRef.current) {
      console.log('=== UPDATING STUDENT TABLE QUERY ===');
      tableRef.current.setQuery(searchParams);
    }
  }, [searchParams, refreshKey]);

  const handleDeleteClick = (idx: number) => {
    setCurrentDeleteId(idx);
    onDeleteOpen();
  };

  const handleDeleteConfirm = async () => {
    if (currentDeleteId !== null) {
      try {
        await deleteStudent({ id: currentDeleteId });
        Toast.success('删除成功');
        onDeleteClose();
        setCurrentDeleteId(null);
        // 刷新表格数据
        tableRef.current?.reload();
      } catch (error) {
        Toast.error('删除失败');
      }
    }
  };

  const handleDisableClick = (idx: number) => {
    setCurrentDisableId(idx);
    onDisableOpen();
  };

  const handleDisableConfirm = async () => {
    if (currentDisableId !== null) {
      try {
        await changeStudentStatus({ id: currentDisableId, status: 2 });
        Toast.success('设置失效成功');
        onDisableClose();
        setCurrentDisableId(null);
        // 刷新表格数据
        tableRef.current?.reload();
      } catch (error) {
        Toast.error('设置失效失败');
      }
    }
  };

  const handleRecoverClick = (idx: number) => {
    setCurrentRecoverId(idx);
    onRecoverOpen();
  };

  const handleRecoverConfirm = async () => {
    if (currentRecoverId !== null) {
      try {
        await changeStudentStatus({ id: currentRecoverId, status: 1 });
        Toast.success('恢复成功');
        onRecoverClose();
        setCurrentRecoverId(null);
        // 刷新表格数据
        tableRef.current?.reload();
      } catch (error) {
        Toast.error('恢复失败');
      }
    }
  };

  const handleGraduateClick = (idx: number) => {
    setCurrentGraduateId(idx);
    onGraduateOpen();
  };

  const handleGraduateConfirm = async () => {
    if (currentGraduateId !== null) {
      try {
        await changeStudentStatus({ id: currentGraduateId, status: 3 });
        Toast.success('设置毕业成功');
        onGraduateClose();
        setCurrentGraduateId(null);
        // 刷新表格数据
        tableRef.current?.reload();
      } catch (error) {
        Toast.error('设置毕业失败');
      }
    }
  };

  const handleJumpToPage = () => {
    // 跳转页面逻辑
  };

  const handleJumpPageKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  const getGenderText = (gender: number) => {
    return gender === 1 ? '男' : gender === 2 ? '女' : '未知';
  };

  const getStatusText = (status: number) => {
    return StudentStatusMap[status as keyof typeof StudentStatusMap]?.label || '未知状态';
  };

  const getStatusColor = (status: number) => {
    return StudentStatusMap[status as keyof typeof StudentStatusMap]?.color || '#999999';
  };

  const getActions = (status: number) => {
    const actions = [];
    if (status === 2) {
      actions.push('view', 'recover');
    } else if (status === 3) {
      actions.push('view');
    } else if (status === 1) {
      actions.push('edit', 'disable', 'graduate');
    }
    actions.push('delete');
    return actions;
  };

  const handleView = async (student: any) => {
    setViewStudent(student);
    onViewOpen();
  };

  const handleCloseView = () => {
    setViewStudent(null);
    onViewClose();
  };

  const columns = useMemo(() => [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: vwDims(100),
      render: (text: string) => (
        <MyBox fontSize={vwDims(14)} color="#1D2129">
          {text}
        </MyBox>
      )
    },
    {
      title: '学号',
      dataIndex: 'code',
      key: 'code',
      width: vwDims(100),
      render: (text: string) => (
        <MyBox fontSize={vwDims(14)}>
          {text}
        </MyBox>
      )
    },
    {
      title: '性别',
      dataIndex: 'sex',
      key: 'sex',
      width: vwDims(60),
      render: (sex: number) => (
        <MyBox color="#1D2129" fontSize={vwDims(14)}>
          {getGenderText(sex)}
        </MyBox>
      )
    },
    {
      title: '班级',
      dataIndex: 'gradeName',
      key: 'gradeName',
      width: vwDims(120),
      render: (gradeName: string, record: any) => {
        // 格式化班级名称，将七年级改为初一
        const formattedGrade = gradeName.replace(/七年级/g, '初一');
        return (
          <MyBox fontSize={vwDims(14)}>
            {formattedGrade} {record.clazzName}
          </MyBox>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: vwDims(80),
      render: (status: number) => (
        <Flex align="center" gap={vwDims(8)}>
          <MyBox
            w={vwDims(8)}
            h={vwDims(8)}
            borderRadius="full"
            bg={getStatusColor(status)}
          />
          <MyBox fontSize={vwDims(14)} color="#1D2129">
            {getStatusText(status)}
          </MyBox>
        </Flex>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: vwDims(180),
      render: (record: any) => {
        const actions = getActions(record.status);
        return (
          <Flex gap={vwDims(8)}>
            {actions.includes('edit') && (
              <Button
                size="sm"
                variant="outline"
                borderColor="#7D4DFF"
                color="#7D4DFF"
                bg="white"
                _hover={{ bg: "rgba(125, 77, 255, 0.1)" }}
                onClick={() => onEdit?.(record.id)}
              >
                编辑
              </Button>
            )}
            {actions.includes('view') && (
              <Button
                size="sm"
                variant="outline"
                colorScheme="blue"
                onClick={() => handleView(record)}
              >
                查看
              </Button>
            )}
            {actions.includes('recover') && (
              <Button
                size="sm"
                variant="outline"
                colorScheme="green"
                onClick={() => handleRecoverClick(record.id)}
              >
                恢复
              </Button>
            )}
            {actions.includes('disable') && (
              <Button
                size="sm"
                variant="outline"
                colorScheme="red"
                onClick={() => handleDisableClick(record.id)}
              >
                失效
              </Button>
            )}
            {actions.includes('graduate') && (
              <Button
                size="sm"
                variant="outline"
                colorScheme="blue"
                onClick={() => handleGraduateClick(record.id)}
              >
                毕业
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              colorScheme="gray"
              onClick={() => handleDeleteClick(record.id)}
            >
              删除
            </Button>
          </Flex>
        );
      }
    }
  ], [onEdit]);

  return (
    <MyBox h="100%" display="flex" flexDirection="column">
      {/* 使用 MyTable 组件 */}
      <MyTable
        ref={tableRef}
        api={getStudentPageAdapter}
        columns={columns}
        defaultQuery={searchParams}
        cacheKey={`student-list-${refreshKey}`}
        pageConfig={{
          showPaginate: true,
          defaultCurrent: 1,
          defaultSize: 10,
          showSizeChanger: true,
          showQuickJumper: true
        }}
        boxStyle={{
          px: 0,
          py: 0,
          h: '100%'
        }}
        tableStyle={{
          backgroundColor: '#FFFFFF',
          borderRadius: vwDims(8),
          height: '100%'
        }}
        sticky={{ offsetHeader: 0 }}
        scroll={{ x: vwDims(800), y: 'calc(100vh - 300px)' }}
        rowKey="id"
        scrollMode="sticky"
        className={styles['file-table']}
        headerConfig={{
          showIfEmpty: true,
          showHeader: false
        }}
        emptyConfig={{
          emptyText: '暂无学生数据',
          emptyStyle: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: vwDims(200),
            fontSize: vwDims(14),
            color: '#666666'
          }
        }}
      />

      {/* 学生详情查看模态框 */}
      {isViewOpen && viewStudent && (
        <Modal isOpen={isViewOpen} onClose={handleCloseView} size="xl">
          <ModalOverlay />
          <ModalContent>
            <ModalBody p={0}>
              <StudentView 
                isOpen={isViewOpen}
                student={viewStudent} 
                onClose={handleCloseView} 
              />
            </ModalBody>
          </ModalContent>
        </Modal>
      )}

      {/* 删除确认弹窗 */}
      <AlertDialog 
        isOpen={isDeleteOpen} 
        onClose={onDeleteClose}
        leastDestructiveRef={deleteCancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              删除学生
            </AlertDialogHeader>
            <AlertDialogBody>
              确定要删除这个学生吗？此操作不可撤销。
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={deleteCancelRef} onClick={onDeleteClose}>取消</Button>
              <Button colorScheme="red" onClick={handleDeleteConfirm} ml={3}>
                删除
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* 失效确认弹窗 */}
      <AlertDialog 
        isOpen={isDisableOpen} 
        onClose={onDisableClose}
        leastDestructiveRef={disableCancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              设置失效
            </AlertDialogHeader>
            <AlertDialogBody>
              学生设置失效后，将不参与评价，确定将学生设置为失效吗？
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={disableCancelRef} onClick={onDisableClose}>取消</Button>
              <Button colorScheme="orange" onClick={handleDisableConfirm} ml={3}>
                确定
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* 恢复确认弹窗 */}
      <AlertDialog 
        isOpen={isRecoverOpen} 
        onClose={onRecoverClose}
        leastDestructiveRef={recoverCancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              恢复学生
            </AlertDialogHeader>
            <AlertDialogBody>
              确定要恢复该学生的状态吗？
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={recoverCancelRef} onClick={onRecoverClose}>取消</Button>
              <Button colorScheme="green" onClick={handleRecoverConfirm} ml={3}>
                确定
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* 毕业确认弹窗 */}
      <AlertDialog 
        isOpen={isGraduateOpen} 
        onClose={onGraduateClose}
        leastDestructiveRef={graduateCancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              设置毕业
            </AlertDialogHeader>
            <AlertDialogBody>
              学生设置毕业状态后，将不可恢复，确定设置为毕业状态？
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={graduateCancelRef} onClick={onGraduateClose}>取消</Button>
              <Button colorScheme="purple" onClick={handleGraduateConfirm} ml={3}>
                确定
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </MyBox>
  );
};

export default StudentList;
