import { create } from 'zustand';
import { getNoticeNotReadNum, setNoticeDoRead } from '@/api/cloud';

type State = {
  tipsCount: number;
  fetchUnreadCount: () => Promise<void>;
  setTipsCount: (count: number) => void;
  markAllAsRead: () => Promise<void>;
};

export const useNotificationStore = create<State>((set, get) => ({
  tipsCount: 0,
  fetchUnreadCount: async () => {
    try {
      const count = await getNoticeNotReadNum();
      set({ tipsCount: Number(count) });
    } catch (error) {}
  },
  setTipsCount: (count: number) => {
    set({ tipsCount: count });
  },
  markAllAsRead: async () => {
    try {
      await setNoticeDoRead();
      const count = await getNoticeNotReadNum();
      set({ tipsCount: Number(count) });
    } catch (error) {}
  }
}));
