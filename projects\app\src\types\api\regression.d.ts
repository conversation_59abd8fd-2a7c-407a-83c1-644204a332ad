declare module 'regression' {
  type RegressionType = 'linear' | 'polynomial' | 'exponential';
  type DataPoint = [number, number];

  interface RegressionResult {
    points: DataPoint[];
    predict: (x: number) => [number, number];
    equation: number[];
    string: string;
    r2: number;
  }

  interface RegressionOptions {
    order?: number;
    precision?: number;
  }

  interface Regression {
    linear(data: DataPoint[], options?: RegressionOptions): RegressionResult;
    polynomial(data: DataPoint[], options?: RegressionOptions): RegressionResult;
    exponential(data: DataPoint[], options?: RegressionOptions): RegressionResult;
  }

  const regression: Regression;
  export default regression;
}
