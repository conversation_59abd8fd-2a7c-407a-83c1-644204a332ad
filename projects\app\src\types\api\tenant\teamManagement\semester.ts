import { RequestPageParams } from '@/types';

export type ClientSemesterPageType = RequestPageParams & {
  createTime: string;
  endDate: string;
  id: string;
  isCurrent: 0 | 1 | 2; // 0 表示否，1 表示是，2 表示结束
  isDeleted: number;
  startDate: string;
  tenantId: number;
  type: 1 | 2; // 1 表示第一学期，2 表示第二学期
  updateTime: string;
  year: string;
  status: number;
};

interface DictItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  parentId: string;
  code: string;
  dictKey: number;
  dictValue: string;
  sort: number;
  children?: DictItem[];
  parentName: string;
  hasChildren: boolean;
}

export type DictTree = DictItem[];

export type CreateClientSemesterParams = {
  endDate: string; // 结束日期，格式为日期时间字符串
  startDate: string; // 开始日期，格式为日期时间字符串
  type: 1 | 2; // 学期类型：1 表示第一学期，2 表示第二学期
  year: string; // 所属学年
};

export type UpdateClientSemesterParams = {
  endDate: string; // 结束日期，格式为日期时间字符串
  startDate: string; // 开始日期，格式为日期时间字符串
  id: string;
};

export type DetailClientSemester = {
  createTime: string;
  endDate: string;
  id: number;
  isCurrent: 0 | 1 | 2;
  isDeleted: number;
  startDate: string;
  tenantId: number;
  type: 1 | 2;
  updateTime: string;
  year: string;
};

export type SetCurrentClientSemesterParams = {
  id: string;
  isCurrent: 0 | 1 | 2;
};

export type Semester = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  year: string;
  type: number;
  startDate: string;
  endDate: string;
  isCurrent: number;
};

export type ClientSemesterGetDelayList = Semester[];

export type termItem = {
  label: string;
  value: number;
};
export type SemesterListOptions = ClientSemesterPageType & {
  termList: termItem[];
};

export type StartSemesterParams = {
  id: string;
  isCrossYear: 0 | 1; // 跨学期/跨学年：0-跨学期，1-跨学年
  copyTenantSubject: 0 | 1; // 学科配置: 1-复制, 0-稍后自行导入
  copySchoolManageTeacher: 0 | 1; // 教师行政工作分配：1-复制, 0-稍后自行导入
  copySchoolSubjectManager: 0 | 1; // 教师学科负责工作分配: 1-复制, 0-稍后自行导入
  copySchoolSubjectTeacher: 0 | 1; // 教师学科负责工作分配: 1-复制, 0-稍后自行导入
};
