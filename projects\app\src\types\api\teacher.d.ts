/** 教师信息分页查询接口类型定义，参考 personalCenter.d.ts 风格 */

/** 教师信息分页查询-请求参数 */
export interface TeacherPageParams {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 教师姓名 */
  name?: string;
  /** 手机号 */
  phone?: string;
  /** 状态：0-在职，1-离职 */
  status?: number;
  /** 账号 */
  account?: string;
  /** 行政职务类型ID数组 */
  schoolManageTeachTypes?: number[];
  /** 教学职务类型ID数组 */
  schoolSubjectTeachTypes?: number[];
}

/** 教师信息分页查询-单条教师信息 */
export interface TeacherPageItem {
  /** 教师ID */
  id: string | number;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 是否已删除：0-未删除，1-已删除 */
  isDeleted: number;
  /** 租户ID */
  tenantId: number;
  /** tmbId */
  tmbId: number;
  /** 姓名 */
  name: string;
  /** 性别：0-未定义，1-男，2-女 */
  gender: number;
  /** 手机号 */
  phone: string;
  /** 状态：0-在职，1-离职 */
  status: number;
  /** 租户名称 */
  tenantName: string;
  /** 账号 */
  account: string;
  /** 行政职务名称数组 */
  schoolManageTeachers: string[];
  /** 教学职务名称数组 */
  schoolSubjectTeachers: string[];
}

/** 教师信息分页查询-响应数据 */
export interface TeacherPageResponse {
  /** 教师信息列表 */
  records: TeacherPageItem[];
  /** 总条数 */
  total: number;
  /** 每页条数 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 其它分页相关字段（如有） */
  [key: string]: any;
}

/** 行政职务明细（含回显字段） */
export interface SchoolManageTeacherDetail {
  id?: string;
  teachType: number;
  deptId: number | string;
  deptName: string;
  /** 新增字段： */
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  tenantId?: number | string;
  semesterId?: number | string;
  tmbId?: number;
  parentId?: number | string;
  parentName?: string;
  teachTypeName?: string;
}

/** 教学职务明细（含回显字段） */
export interface SchoolSubjectTeacherDetail {
  id?: string;
  teachType: number;
  deptId: number | string;
  deptName: string;
  subjectId: number | string;
  /** 新增字段： */
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  tenantId?: number | string;
  semesterId?: number | string;
  tmbId?: number;
  parentId?: number | string;
  parentName?: string;
  teachTypeName?: string;
  subjectName?: string;
}

/** 创建教师-请求参数 */
export interface TeacherCreateParams {
  /** 对标账号tmbId */
  tmbId: number;
  /** 姓名 */
  name: string;
  /** 手机号 */
  phone: string | number;
  /** 性别：1-男，2-女 */
  gender: number;
  /** 行政职务明细 */
  schoolManageTeachersDetail: SchoolManageTeacherDetail[];
  /** 教学职务明细 */
  schoolSubjectTeachersDetail: SchoolSubjectTeacherDetail[];
}

/** 创建教师-响应数据（如有返回内容可补充） */
export type TeacherCreateResponse = any;

/** 账号信息（兼容旧版本，建议使用 @/types/api/tenant/account 中的 CommonAccountItem） */
export interface TeacherAccountItem {
  /** tmbId（创建老师入参用这个） */
  id: string;
  /** 用户名 */
  username: string;
  /** 账号 */
  account: string;
  /** 账号类型：0-未绑定，1-学生，2-老师 */
  type: number;
}

/** 账号列表响应（兼容旧版本，建议使用 @/types/api/tenant/account 中的 CommonAccountListResponse） */
export interface TeacherAccountListResponse {
  data: TeacherAccountItem[];
}

/** 根据账号获取教师信息-请求参数 */
export interface TeacherInfoByAccountParams {
  tmbId: number;
}

/** 根据账号获取教师信息-响应数据 */
export interface TeacherInfoByAccountResponse {
  id: string | null;
  createTime: string;
  updateTime: string;
  isDeleted: number | null;
  tenantId: number | string;
  tmbId: number;
  name: string;
  gender: number;
  phone: string;
  status: number | null;
  tenantName: string;
  account: string;
  schoolManageTeachers: string[];
  schoolSubjectTeachers: string[];
  schoolManageTeachersDetail: SchoolManageTeacherDetail[];
  schoolSubjectTeachersDetail: SchoolSubjectTeacherDetail[];
}

/** 查看教师详情-请求参数 */
export interface TeacherDetailParams {
  /** 教师ID */
  id: string | number;
}

/** 查看教师详情-响应数据 */
export interface TeacherDetailResponse {
  id: string | number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  tmbId: number;
  name: string;
  gender: number;
  phone: string;
  status: number;
  tenantName: string;
  account: string;
  schoolManageTeachers: string[];
  schoolSubjectTeachers: string[];
  schoolManageTeachersDetail: SchoolManageTeacherDetail[];
  schoolSubjectTeachersDetail: SchoolSubjectTeacherDetail[];
}

/** 编辑教师-请求参数 */
export interface TeacherUpdateParams {
  /** 教师ID */
  id: string | number;
  /** 姓名 */
  name: string;
  /** 手机号 */
  phone: string | number;
  /** 性别：1-男，2-女 */
  gender: number;
  /** tmbId */
  tmbId: number;
  /** 行政职务明细 */
  schoolManageTeachersDetail: SchoolManageTeacherDetail[];
  /** 教学职务明细 */
  schoolSubjectTeachersDetail: SchoolSubjectTeacherDetail[];
}

/** 编辑教师-响应数据（如有返回内容可补充） */
export type TeacherUpdateResponse = any;

/** 删除教师-请求参数 */
export interface TeacherDeleteParams {
  /** 教师ID */
  id: string | number;
  /** 是否同步删除账号：0-不删除，1-删除 */
  deleteAccount?: number;
}

/** 删除教师-响应数据（如有返回内容可补充） */
export type TeacherDeleteResponse = any;

/** 修改教师状态-请求参数 */
export interface TeacherUpdateStatusParams {
  /** 教师ID */
  id: string | number;
  /** 状态：0-离职老师恢复，1-在职老师失效 */
  status: number;
  /** 是否同步禁用账号：0-不禁用，1-禁用 */
  disableAccount?: number;
}

/** 修改教师状态-响应数据（如有返回内容可补充） */
export type TeacherUpdateStatusResponse = any;

/** 在职老师信息 */
export interface TeacherSimpleItem {
  id: string | number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  tmbId: number;
  name: string;
  gender: number;
  phone: string;
  status: number;
}

/** 在职老师列表响应 */
export interface TeacherSimpleListResponse {
  data: TeacherSimpleItem[];
}

/** 当前学期信息 */
export interface CurrentSemesterResponse {
  id: string | number;
  name: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

/** 学期分页请求参数 */
export interface SemesterPageParams {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  size: number;
}

/** 单个学期信息 */
export interface SemesterItem {
  id: string | number;
  name: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

/** 学期分页响应 */
export interface SemesterPageResponse {
  records: SemesterItem[];
  total: number;
  size: number;
  current: number;
  [key: string]: any;
}

/** 学科信息 */
export interface TenantSubjectItem {
  id: string | number;
  name: string;
  [key: string]: any;
}

/** 学科列表响应 */
export interface TenantSubjectListResponse {
  data: TenantSubjectItem[];
}

/** 班级树型接口-请求参数 */
export interface SchoolDeptTreeParams {
  /** 学期ID */
  semesterId: string | number;
}

/** 班级树节点 */
export interface SchoolDeptTreeItem {
  id: string | number;
  name: string;
  parentId?: string | number;
  children?: SchoolDeptTreeItem[];
  [key: string]: any;
}

/** 班级树型接口-响应数据 */
export interface SchoolDeptTreeResponse {
  data: SchoolDeptTreeItem[];
}

// 教师导入相关类型定义

// 下载教师模板-请求参数
export interface DownloadTeacherTemplateParams {
  // Body参数：空对象 {}
  [key: string]: any;
  // Header参数：授权token
  authorization?: string;
}

// 下载教师模板-响应数据
export interface DownloadTeacherTemplateResponse {
  // 文件流或下载链接
  data: Blob | string;
}

// 导入教师-请求参数
export interface ImportTeacherParams {
  file?: File; // Body参数：文件对象
  queryFile?: string; // Query参数：文件路径字符串
  authorization?: string; // Header参数：授权token
}

// 导入教师-响应数据
export interface ImportTeacherResponse {
  code: number;
  success: boolean;
  message?: string;
  msg?: string;
  data?: {
    teacherIds?: string[]; // 导入成功的教师ID列表
    valid?: number; // 有效数据数量
    msg?: string; // 数据层消息
    errMsgs?: string[]; // 错误信息数组
    errmsgs?: string[]; // 错误信息数组（小写，兼容后端）
    [key: string]: any;
  };
  // 兼容直接返回 data 的情况
  valid?: number;
  errMsgs?: string[];
  errmsgs?: string[];
  [key: string]: any;
}
