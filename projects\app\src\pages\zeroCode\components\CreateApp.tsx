import { Box, Button, Flex, Grid, Image } from '@chakra-ui/react';
import { ZeroCodeProps } from '..';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import AdvancedModal from '@/components/AppModal/advanced';
import SimpleModal from '@/components/AppModal/simple';
import ZeroSpaceTour from '@/components/CustomTour/ZeroSpaceTour';
import { useAppStore } from '@/store/useAppStore';
import { respDims } from '@/utils/chakra';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
const CreateApp = ({ currentTab, TabRender }: ZeroCodeProps) => {
  const { openOverlay } = useOverlayManager();
  const { myApps, loadMyApps } = useAppStore();

  const onAddAdvanced = () => {
    openOverlay({
      Overlay: AdvancedModal,
      props: {
        isCopy: false,
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onAddSimple = () => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: false,
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  return (
    <Box w="100%" h="100%">
      <Flex
        w="100%"
        pb={respDims(4, GLOBAL_DIMS_MIN_SCALE)}
        alignItems="center"
        justifyContent="space-between"
        borderBottom="1px solid #E0D5FF"
      >
        <TabRender />
        <Box></Box>
      </Flex>

      <Box
        pb={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
        px={respDims(0, GLOBAL_DIMS_MIN_SCALE)}
        mt={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
      >
        <Grid
          gridTemplateColumns={['repeat(1,1fr)', 'repeat(2,1fr)', 'repeat(2,1fr)', 'repeat(3,1fr)']}
          gap={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
        >
          {/* 简易应用卡片 */}
          <Flex
            p={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
            pt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
            flexDir="column"
            color="primary.500"
            position="relative"
            cursor="pointer"
            userSelect="none"
            borderRadius={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            bgColor="#fff"
            transition="all 0.3s ease-in-out"
            onClick={onAddSimple}
            _hover={{
              transform: 'translateY(-5px)',
              zIndex: '9',
              boxShadow:
                '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
              '&:hover .create-button': {
                opacity: '1'
              },
              '&:hover .app-image': {
                content: 'url(/imgs/zeroCodeSpace/hover2.png)'
              }
            }}
          >
            <Flex flex="1" flexDir="column" justifyContent="space-between">
              <Flex
                fontSize={respDims(17, GLOBAL_DIMS_MIN_SCALE)}
                color="#000"
                fontWeight="600"
                justifyContent="space-between"
                alignItems="center"
              >
                创建简易应用
                <Button
                  variant="primary"
                  className="create-button"
                  opacity="0"
                  h={respDims(36, GLOBAL_DIMS_MIN_SCALE)}
                >
                  创建应用
                </Button>
              </Flex>
              <Box
                fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                color="#909399"
                mt={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
              >
                通过填表单形式创建简单的AI应用，适合新手。
              </Box>
              <Box
                className="create-app-card"
                mt={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                bg="#F8FAFC"
                borderRadius={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
                overflow="hidden"
                position="relative"
              >
                <Image
                  src="/imgs/zeroCodeSpace/nohover2.png"
                  alt="简易应用"
                  w="100%"
                  h="auto"
                  objectFit="cover"
                  transition="all 0.3s"
                  className="app-image"
                />
              </Box>
            </Flex>
          </Flex>

          {/* 高阶应用卡片 */}
          <Flex
            p={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
            pt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
            flexDir="column"
            color="primary.500"
            position="relative"
            cursor="pointer"
            userSelect="none"
            borderRadius={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            bgColor="#fff"
            transition="all 0.3s ease-in-out"
            onClick={onAddAdvanced}
            _hover={{
              transform: 'translateY(-5px)',
              zIndex: '9',
              boxShadow:
                '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
              '&:hover .create-button': {
                opacity: '1'
              },
              '&:hover .app-image': {
                content: 'url(/imgs/zeroCodeSpace/hover.png)'
              }
            }}
          >
            <Flex flex="1" flexDir="column" justifyContent="space-between">
              <Flex
                fontSize={respDims(17, GLOBAL_DIMS_MIN_SCALE)}
                color="#000"
                fontWeight="600"
                justifyContent="space-between"
                alignItems="center"
              >
                创建高阶应用
                <Button
                  variant="primary"
                  className="create-button"
                  opacity="0"
                  h={respDims(36, GLOBAL_DIMS_MIN_SCALE)}
                >
                  创建应用
                </Button>
              </Flex>
              <Box
                fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                color="#909399"
                mt={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
              >
                通过可视化编程的方式，构建逻辑复杂的多轮对话AI 应用，推荐高级玩家使用
              </Box>
              <Box
                mt={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                bg="#F8FAFC"
                borderRadius={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
                overflow="hidden"
                position="relative"
                className="create-app-card"
              >
                <Image
                  src="/imgs/zeroCodeSpace/nohover.png"
                  alt="高阶应用"
                  w="100%"
                  h="auto"
                  objectFit="cover"
                  transition="all 0.3s"
                  className="app-image"
                />
              </Box>
            </Flex>
          </Flex>
        </Grid>
      </Box>
    </Box>
  );
};

export default CreateApp;
