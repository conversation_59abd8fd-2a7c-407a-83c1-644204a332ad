import { getRedirectUrl, getHomeComponent } from '@/utils/unthRedirect';
import DianHuaLayout from '@/components/LayoutOfficialHome/dianhua';
// 定义类型
type IndustryRedirectMap = Record<number, string>;

interface IndexProps {
  industry?: number;
  host?: string;
  error?: string;
}

export default function Index({ industry, host, error }: IndexProps) {
  const getIndustryName = (industryCode?: number) => {
    const industryNames: Record<number, string> = {
      1: '普教',
      6: '企业',
      7: '职教'
    };
    return industryNames[industryCode || 1] || '未知';
  };

  const HomeComponent = getHomeComponent(host || '');

  if (HomeComponent) {
    return HomeComponent;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">系统暂时无法访问</h1>
          <p className="text-gray-600">请稍后重试或联系管理员</p>
          {host && <p className="text-sm text-gray-400 mt-2">Host: {host}</p>}
          {error && <p className="text-sm text-gray-400 mt-2">Error: {error}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">欢迎访问AI平台</h1>
      </div>
    </div>
  );
}

// 配置常量
const REDIRECT_MAP: IndustryRedirectMap = {
  1: '/home/<USER>', // 普教
  6: '/home/<USER>', // 企业
  7: '/home/<USER>' // 职教
};
const DEFAULT_REDIRECT = '/home/<USER>';
const API_TIMEOUT = 5000; // 5秒超时

// 创建重定向结果
function createRedirectResult(destination: string) {
  return {
    redirect: {
      destination,
      permanent: false
    }
  };
}

export async function getServerSideProps(context: any) {
  const { query, req } = context;
  // 构建完整 URL
  const protocol = req.headers['x-forwarded-proto'] || 'http';
  const host = req.headers['x-forwarded-host'] || req.headers.host;

  const queryString = new URLSearchParams(query).toString();

  try {
    // if (!process.env.API_URL) {
    //   throw new Error('API_URL environment variable is not configured');
    // }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    const baseUrl = `${protocol}://${host}`;
    // const baseUrl = process.env.API_URL;

    const cacheBuster = `?_t=${Date.now()}`;
    const apiUrl = `${baseUrl}/huayun-ai/client/tenant/detail${cacheBuster}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        'X-Content-Type-Options': 'nosniff'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();

    let redirectPath = REDIRECT_MAP[data.data.industry] || DEFAULT_REDIRECT;

    redirectPath = getRedirectUrl(redirectPath, context.req.headers.host);

    // 如果重定向路径是 '/'，则不重定向，直接渲染当前页面
    if (redirectPath === '/') {
      return {
        props: {
          // 可以传递一些数据给组件
          industry: data.data.industry,
          host: host
        }
      };
    }

    return createRedirectResult(`${redirectPath}${queryString ? `?${queryString}` : ''}`);
  } catch (error) {
    // 错误默认跳 /home/<USER>
    console.error('SSR Error:', error);
    const redirectPath = getRedirectUrl(DEFAULT_REDIRECT, context.req.headers.host);

    // 如果重定向路径是 '/'，则不重定向，直接渲染当前页面
    if (redirectPath === '/') {
      return {
        props: {
          error: error,
          host: host
        }
      };
    }

    return createRedirectResult(`${redirectPath}${queryString ? `?${queryString}` : ''}`);
  }
}
