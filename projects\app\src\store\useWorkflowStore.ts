import {
  ListWorkflowProcessesParams,
  ListWorkflowsParams,
  TenantWorkflow,
  TenantWorkflowProcess
} from '@/types/api/workflow';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  listOfficialWorkflows,
  listPersonalWorkflows,
  listTenantWorkflows,
  listWorkflowProcesses
} from '@/api/workflow';
import { DataSource } from '@/constants/common';
import { PromptDataType } from '@/components/ChatBox/PromptModal';

type State = {
  selectWorkflow: TenantWorkflow | null;
  setSelectWorkflow: (workflow: TenantWorkflow | null) => void;
  selectStep: TenantWorkflowProcess | null;
  setSelectStep: (step: TenantWorkflowProcess | null) => void;
  showWorkflowInfo: boolean;
  setShowWorkflowInfo: (show: boolean) => void;
  workflows: TenantWorkflow[];
  setWorkflows: (
    source: DataSource,
    tenantAppId: string,
    init?: boolean,
    name?: string
  ) => Promise<TenantWorkflow[]>;
  steps: TenantWorkflowProcess[];
  setSteps: (tenantWorkflowId: string) => Promise<void>;
  updateWorkflowProcess: (prompt: PromptDataType) => Promise<void>;

  currentTab: DataSource | null;
  setCurrentTab: (tabType: DataSource | null) => void;
  loading: boolean;
};

export const useWorkflowStore = create<State>()(
  devtools(
    persist(
      immer((set) => ({
        selectWorkflow: null,
        setSelectWorkflow: (workflow) => {
          set((state) => {
            state.selectWorkflow = workflow;
          });
        },
        currentTab: DataSource.Offical,
        setCurrentTab: (tab) => {
          set((state) => {
            state.currentTab = tab;
          });
        },
        selectStep: null,
        setSelectStep: (step) => {
          set((state) => {
            state.selectStep = step;
          });
        },
        showWorkflowInfo: false,
        setShowWorkflowInfo: (show) => {
          set((state) => {
            state.showWorkflowInfo = show;
          });
        },
        workflows: [],
        setWorkflows: async (source, tenantAppId, init = true) => {
          set((state) => {
            state.loading = true;
            state.steps = [];
          });
          const params: ListWorkflowsParams = { tenantAppId };
          let data: TenantWorkflow[] = [];

          if (source === DataSource.Offical) {
            const [officialData, tenantData, personalData] = await Promise.all([
              listOfficialWorkflows(params),
              listTenantWorkflows(params),
              listPersonalWorkflows(params)
            ]);
            data = [...officialData, ...tenantData, ...personalData];
          } else if (source === DataSource.Tenant) {
            const [tenantData, personalData] = await Promise.all([
              listTenantWorkflows(params),
              listPersonalWorkflows(params)
            ]);
            data = [...tenantData, ...personalData];
          } else {
            data = await listPersonalWorkflows(params);
          }

          set((state) => {
            state.workflows = data;
            state.loading = false;
            if (init) {
              state.selectWorkflow = data.find((item) => item.source === state.currentTab) || null;
            }
          });
          return data;
        },
        steps: [],
        setSteps: async (tenantWorkflowId) => {
          set((state) => {
            state.loading = true;
          });
          const params: ListWorkflowProcessesParams = { id: tenantWorkflowId };
          const data = await listWorkflowProcesses(params);
          set((state) => {
            state.steps = data;
            state.loading = false;
            state.selectStep = null;
          });
        },

        updateWorkflowProcess: async (data) => {
          set((state) => {
            const steps = [...state.steps];
            const newSteps = steps.map((item, index) => {
              if (item.tenantPromptId == data.id) {
                const newStep: TenantWorkflowProcess = {
                  ...steps[index],
                  proContent: data.proContent,
                  hiddenContent: data.hiddenContent,
                  promptTitle: data.promptTitle,
                  promptDescription: data.description,
                  inputContent: data.inputContent
                };
                return newStep;
              } else {
                return item;
              }
            });
            state.steps = newSteps;
          });
        },

        loading: false
      })),
      {
        name: 'workflowStore',
        partialize: (state) => ({
          selectWorkflow: state.selectWorkflow,
          selectStep: state.selectStep,
          showWorkflowInfo: state.showWorkflowInfo,
          workflows: state.workflows,
          steps: state.steps,
          loading: state.loading
        })
      }
    )
  )
);
