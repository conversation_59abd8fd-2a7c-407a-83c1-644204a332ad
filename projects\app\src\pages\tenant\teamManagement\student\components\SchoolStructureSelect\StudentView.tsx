import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Button,
  Text,
  Image,
  Divider,
  IconButton
} from '@chakra-ui/react';
import type { StudentPageItem, StudentDetailResponse } from '@/types/student';
import SvgIcon from '@/components/SvgIcon';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';
import MyBox from '@/components/common/MyBox';

interface StudentViewProps {
  isOpen: boolean;
  onClose: () => void;
  student: StudentDetailResponse | null;
}

const GENDER_OPTIONS = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
];

const StudentView: React.FC<StudentViewProps> = ({ isOpen, onClose, student }) => {
  if (!student) return null;

  // 调试信息
  console.log('StudentView - student data:', student);
  console.log('StudentView - avatarUrl:', student.avatarUrl);

  // 性别格式化
  const getGenderText = (gender: number) => {
    if (gender === 1) return '男';
    if (gender === 2) return '女';
    return '-';
  };

  // 构建班级完整路径
  const getFullClassName = () => {
    const parts = [];
    if (student.gradeName) {
      // 格式化班级名称，将七年级改为初一
      const formattedGrade = student.gradeName.replace(/七年级/g, '初一');
      parts.push(formattedGrade);
    }
    if (student.clazzName) parts.push(student.clazzName);
    return parts.join('/') || '-';
  };

  // 构建地址
  const getFullAddress = () => {
    const parts = [];
    if (student.provinceName) parts.push(student.provinceName);
    if (student.cityName) parts.push(student.cityName);
    if (student.districtName) parts.push(student.districtName);
    return parts.join('') || '';
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <MyBox
        position="fixed"
        top="50%"
        left="50%"
        transform="translate(-50%, -50%)"
        w="684px"
        h="561px"
        bg="white"
        borderRadius="lg"
        overflow="hidden"
        zIndex={1400}
        boxShadow="xl"
        display="flex"
        flexDirection="column"
        maxH="561px"
        minH="561px"
      >
        <MyBox
          position="relative"
          pb={4}
          borderBottom="1px solid"
          borderColor="gray.200"
          px={6}
          pt={6}
        >
          <Text fontWeight="bold" fontSize="20px">
            查看信息
          </Text>
          <IconButton
            position="absolute"
            top={4}
            right={4}
            aria-label="关闭"
            icon={<Text fontSize="16px">×</Text>}
            variant="ghost"
            size="sm"
            onClick={onClose}
          />
        </MyBox>
        <MyBox p={6} overflow="auto" w="100%" flex={1} maxH="calc(561px - 80px)">
          {/* 头像区域 */}
          <Flex direction="column" align="center" mb={6}>
            <MyBox mb={2}>
              {student.avatarUrl ? (
                <Image
                  src={student.avatarUrl}
                  alt="学生照片"
                  boxSize="79px"
                  borderRadius="full"
                  objectFit="cover"
                />
              ) : (
                <SvgIcon name="user" width={79} height={79} />
              )}
            </MyBox>
          </Flex>
          <Flex gap={4} mb={3}>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 学生名称：</FormLabel>
              <MyInput 
                value={student.name} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
              />
            </FormControl>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 学号：</FormLabel>
              <MyInput 
                value={student.code} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
              />
            </FormControl>
          </Flex>
          <Flex gap={4} mb={3}>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 性别：</FormLabel>
              <MySelect 
                value={student.sex} 
                list={GENDER_OPTIONS.map((opt) => ({
                  label: opt.label,
                  value: opt.value
                }))}
                bg="#F2F3F5"
                size="sm"
                borderRadius="md"
                border="none"
              />
            </FormControl>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 出生日期：</FormLabel>
              <MyInput 
                value={student.birthday || ''} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
                placeholder="YYYY-MM-DD"
              />
            </FormControl>
          </Flex>
          <Flex gap={4} mb={3}>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 班级：</FormLabel>
              <MyInput 
                value={getFullClassName()} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
              />
            </FormControl>
            <FormControl isRequired flex={1}>
              <FormLabel fontSize="14px" mb={1}>* 入学时间：</FormLabel>
              <MyInput 
                value={student.enrollmentDate || ''} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
                placeholder="YYYY-MM-DD"
              />
            </FormControl>
          </Flex>

          <Flex gap={4} mb={3}>
            <FormControl flex={1}>
              <FormLabel fontSize="14px" mb={1}>家庭住址：</FormLabel>
              <MyInput 
                value={getFullAddress()} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
                mb={2}
              />
              <MyInput 
                value={student.address || ''} 
                isDisabled 
                bg="#F2F3F5" 
                size="sm"
                borderRadius="md"
                border="none"
                placeholder="详细地址"
              />
            </FormControl>
          </Flex>
          <FormControl>
            <FormLabel fontSize="14px" mb={1}>
              实习经历：
            </FormLabel>
            <Textarea
              value={student.experienceIntroduction || ''}
              isDisabled
              bg="#F2F3F5"
              size="sm"
              borderRadius="md"
              border="none"
              placeholder="请输入实习经历"
              minH="80px"
            />
          </FormControl>
          {/* 底部按钮 */}
          <Divider my={6} />
          <Flex justify="flex-end" gap={4}>
            <Button colorScheme="purple" onClick={onClose}>
              确认
            </Button>
          </Flex>
        </MyBox>
      </MyBox>
    </Modal>
  );
};

export default StudentView;
