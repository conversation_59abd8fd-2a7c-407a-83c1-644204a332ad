import { AppSimpleEditFormType } from '@/fastgpt/global/core/app/type';
import { SimpleAppPrompt, TenantAppKnowledgeFile } from './api/app';
import { IsStructuredPrompt } from '@/constants/api/app';
export type AppSimpleEditFormTypeMegre = {
  promptList: SimpleAppPrompt[];
  isStructuredPrompt: IsStructuredPrompt;
  filesList: TenantAppKnowledgeFile[];
} & AppSimpleEditFormType;

declare global {
  interface Window {
    // VePlayer文档 https://www.volcengine.com/docs/6469/1155423
    VePlayer: any;
    VolcenginePlayer: any;
  }
}
