import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { useToast } from '@/hooks/useToast';
import { getErrText } from '@/utils/string';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { getToken } from '@/utils/auth';
import { AppTTSConfigType } from '@/fastgpt/global/core/app/type';
import { TTSTypeEnum } from '@/fastgpt/web/core/app/constants';
import { OutLinkChatAuthProps } from '@/fastgpt/global/support/permission/chat';
const contentType = 'audio/mpeg';

export const useAudioPlay = (props?: OutLinkChatAuthProps & { ttsConfig?: AppTTSConfigType }) => {
  const { t } = useTranslation();
  const { shareId } = useRouter().query as { shareId?: string };
  const { ttsConfig, outLinkUid, teamId, teamToken } = props || {};
  const { toast } = useToast();
  const [audio, setAudio] = useState<HTMLAudioElement>();
  const audioRef = useRef<HTMLAudioElement>();
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);

  // 初始化：加载语音列表
  useEffect(() => {
    let isSubscribed = true;

    const loadVoices = () => {
      // 获取可用的语音列表
      const availableVoices = window.speechSynthesis?.getVoices?.() || [];

      if (isSubscribed) {
        setVoices(availableVoices);
      }
    };

    // 如果语音已经加载，直接设置
    const initialVoices = window.speechSynthesis?.getVoices?.() || [];
    if (initialVoices.length > 0) {
      setVoices(initialVoices);
    }

    // 监听语音加载事件
    window.speechSynthesis?.addEventListener('voiceschanged', loadVoices);

    // 清理函数
    return () => {
      isSubscribed = false;
      window.speechSynthesis?.removeEventListener('voiceschanged', loadVoices);
    };
  }, []);
  const [audioLoading, setAudioLoading] = useState(false);
  const [audioPlaying, setAudioPlaying] = useState(false);
  const audioController = useRef(new AbortController());

  // 检查是否支持指定语言
  const hasAudio = useMemo(() => {
    if (ttsConfig?.type === TTSTypeEnum.none) return false;
    if (ttsConfig?.type === TTSTypeEnum.model) return true;

    const voice = voices.find((item) => item.lang === 'zh-CN');

    return !!voice;
  }, [ttsConfig, voices]);

  const getAudioStream = useCallback(
    async (input: string) => {
      if (!input) return Promise.reject('Text is empty');

      setAudioLoading(true);
      audioController.current = new AbortController();

      const response = await fetch('/huayun-ai/client/chat/item/speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: getToken()
        },
        signal: audioController.current.signal,
        body: JSON.stringify({
          ttsConfig,
          input: input.trim(),
          shareId,
          outLinkUid,
          teamId,
          teamToken
        })
      }).finally(() => {
        setAudioLoading(false);
      });

      if (!response.body || !response.ok) {
        const data = await response.json();
        toast({
          status: 'error',
          title: getErrText(data, t('common:core.chat.Audio Speech Error'))
        });
        return Promise.reject(data);
      }
      return response.body;
    },
    [outLinkUid, shareId, t, teamId, teamToken, toast, ttsConfig]
  );
  const playAudio = async ({
    text,
    chatItemId,
    appId: appId,
    buffer
  }: {
    text: string;
    chatItemId?: string;
    appId?: string;
    buffer?: Uint8Array;
  }) =>
    new Promise<{ buffer?: Uint8Array }>(async (resolve, reject) => {
      text = text.replace(/\\n/g, '\n');
      try {
        // tts play
        if (audio && ttsConfig && ttsConfig?.type === TTSTypeEnum.model) {
          setAudioLoading(true);

          /* buffer tts */
          if (buffer) {
            playAudioBuffer({ audio, buffer });
            setAudioLoading(false);
            return resolve({ buffer });
          }

          audioController.current = new AbortController();

          /* request tts */
          const response = await fetch('/huayun-ai/client/chat/item/speech', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: getToken()
            },
            signal: audioController.current.signal,
            body: JSON.stringify({
              chatItemId,
              ttsConfig,
              input: text,
              shareId,
              appId: appId
            })
          });

          setAudioLoading(false);

          if (!response.body || !response.ok) {
            const data = await response.json();
            toast({
              status: 'error',
              title: getErrText(data, t('core.chat.Audio Speech Error'))
            });

            return reject(data);
          }

          const audioBuffer = await readAudioStream({
            audio,
            stream: response.body,
            contentType: 'audio/mpeg'
          });

          resolve({
            buffer: audioBuffer
          });
        } else {
          // window speech
          window.speechSynthesis?.cancel();
          const msg = new SpeechSynthesisUtterance(text);
          const voices = window.speechSynthesis?.getVoices?.() || []; // 获取语言包
          const voice = voices.find((item) => {
            return item.lang === 'zh-CN';
          });
          if (voice) {
            msg.onstart = () => {
              setAudioPlaying(true);
            };
            msg.onend = () => {
              setAudioPlaying(false);
              msg.onstart = null;
              msg.onend = null;
            };
            msg.voice = voice;
            window.speechSynthesis?.speak(msg);
          }
          resolve({});
        }
      } catch (error) {
        // toast({
        //   status: 'error',
        //   title: getErrText(error, t('core.chat.Audio Speech Error'))
        // });
        reject(error);
      }
      setAudioLoading(false);
    });
  const playAudioBySharing = async ({
    text,
    chatItemId,
    appId: appId,
    buffer
  }: {
    text: string;
    chatItemId?: string;
    appId?: string;
    buffer?: Uint8Array;
  }) =>
    new Promise<{ buffer?: Uint8Array }>(async (resolve, reject) => {
      text = text.replace(/\\n/g, '\n');
      try {
        // tts play
        if (audio && ttsConfig && ttsConfig?.type === TTSTypeEnum.model) {
          setAudioLoading(true);

          /* buffer tts */
          if (buffer) {
            playAudioBuffer({ audio, buffer });
            setAudioLoading(false);
            return resolve({ buffer });
          }

          audioController.current = new AbortController();

          /* request tts */
          const response = await fetch(
            '/huayun-ai/client/sharing/app/chat/huawei/cloud/audio/transcriptions',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: getToken()
              },
              signal: audioController.current.signal,
              body: JSON.stringify({
                chatItemId,
                ttsConfig,
                input: text,
                shareId,
                appId: appId
              })
            }
          );

          setAudioLoading(false);

          if (!response.body || !response.ok) {
            const data = await response.json();
            toast({
              status: 'error',
              title: getErrText(data, t('core.chat.Audio Speech Error'))
            });

            return reject(data);
          }

          const audioBuffer = await readAudioStream({
            audio,
            stream: response.body,
            contentType: 'audio/mpeg'
          });

          resolve({
            buffer: audioBuffer
          });
        } else {
          // window speech
          window.speechSynthesis?.cancel();
          const msg = new SpeechSynthesisUtterance(text);
          const voices = window.speechSynthesis?.getVoices?.() || []; // 获取语言包
          const voice = voices.find((item) => {
            return item.lang === 'zh-CN';
          });
          if (voice) {
            msg.onstart = () => {
              setAudioPlaying(true);
            };
            msg.onend = () => {
              setAudioPlaying(false);
              msg.onstart = null;
              msg.onend = null;
            };
            msg.voice = voice;
            window.speechSynthesis?.speak(msg);
          }
          resolve({});
        }
      } catch (error) {
        // toast({
        //   status: 'error',
        //   title: getErrText(error, t('core.chat.Audio Speech Error'))
        // });
        reject(error);
      }
      setAudioLoading(false);
    });
  const playWebAudio = useCallback((text: string) => {
    // window speech
    window?.speechSynthesis?.cancel();
    const msg = new SpeechSynthesisUtterance(text);
    const voices = window?.speechSynthesis?.getVoices?.() || []; // 获取语言包
    const voice = voices.find((item) => {
      return item.lang === 'zh-CN';
    });
    if (voice) {
      msg.onstart = () => {
        setAudioPlaying(true);
      };
      msg.onend = () => {
        setAudioPlaying(false);
        msg.onstart = null;
        msg.onend = null;
      };
      msg.voice = voice;
      window.speechSynthesis?.speak(msg);
    }
  }, []);

  const cancelAudio = useCallback(() => {
    if (audio) {
      audio.pause();
      audio.src = '';
    }
    window.speechSynthesis?.cancel();
    audioController.current?.abort();
    setAudioPlaying(false);
  }, [audio]);

  const playAudioByText = useCallback(
    async ({ text, buffer }: { text: string; buffer?: Uint8Array }) => {
      const playAudioBuffer = (buffer: Uint8Array) => {
        if (!audioRef.current) return;
        const audioUrl = URL.createObjectURL(new Blob([buffer], { type: 'audio/mpeg' }));

        audioRef.current.src = audioUrl;
        audioRef.current.play();
      };
      const readAudioStream = (stream: ReadableStream<Uint8Array>) => {
        if (!audioRef.current) return;

        if (!MediaSource) {
          toast({
            status: 'error',
            title: t('common:core.chat.Audio Not Support')
          });
          return;
        }

        // Create media source and play audio
        const ms = new MediaSource();
        const url = URL.createObjectURL(ms);
        audioRef.current.src = url;
        audioRef.current.play();

        let u8Arr: Uint8Array = new Uint8Array();
        return new Promise<Uint8Array>(async (resolve, reject) => {
          // Async to read data from ms
          await new Promise((resolve) => {
            ms.onsourceopen = resolve;
          });
          const sourceBuffer = ms.addSourceBuffer(contentType);

          const reader = stream.getReader();

          // read stream
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done || audioRef.current?.paused) {
                resolve(u8Arr);
                if (sourceBuffer.updating) {
                  await new Promise((resolve) => (sourceBuffer.onupdateend = resolve));
                }
                ms.endOfStream();
                return;
              }

              u8Arr = new Uint8Array([...u8Arr, ...value]);

              await new Promise((resolve) => {
                sourceBuffer.onupdateend = resolve;
                sourceBuffer.appendBuffer(value.buffer as ArrayBuffer);
              });
            }
          } catch (error) {
            reject(error);
          }
        });
      };

      return new Promise<{ buffer?: Uint8Array }>(async (resolve, reject) => {
        text = text.replace(/\\n/g, '\n');
        try {
          // stop last audio
          cancelAudio();

          // tts play
          if (audioRef.current && ttsConfig?.type === TTSTypeEnum.model) {
            /* buffer tts */
            if (buffer) {
              playAudioBuffer(buffer);
              return resolve({ buffer });
            }

            /* request tts */
            const audioBuffer = await readAudioStream(await getAudioStream(text));

            resolve({
              buffer: audioBuffer
            });
          } else {
            // window speech
            playWebAudio(text);
            resolve({});
          }
        } catch (error) {
          toast({
            status: 'error',
            title: getErrText(error, t('common:core.chat.Audio Speech Error'))
          });
          reject(error);
        }
      });
    },
    [cancelAudio, getAudioStream, playWebAudio, t, toast, ttsConfig?.type]
  );

  // listen ttsUrl update
  useEffect(() => {
    setAudio(new Audio());
  }, []);

  // listen audio status
  useEffect(() => {
    if (audio) {
      audioRef.current = audio;

      audio.onplay = () => {
        setAudioPlaying(true);
      };
      audio.onended = () => {
        setAudioPlaying(false);
      };
      audio.onerror = () => {
        setAudioPlaying(false);
      };
      audio.oncancel = () => {
        setAudioPlaying(false);
      };
    }
    const listen = () => {
      cancelAudio();
    };
    window.addEventListener('beforeunload', listen);
    return () => {
      if (audio) {
        audio.onplay = null;
        audio.onended = null;
        audio.onerror = null;
      }
      cancelAudio();
      window.removeEventListener('beforeunload', listen);
    };
  }, [audio, cancelAudio]);

  useEffect(() => {
    return () => {
      setAudio(undefined);
    };
  }, []);

  return {
    voices,
    audioPlaying,
    audioLoading,
    hasAudio,
    playAudio,
    playAudioBySharing,
    cancelAudio,
    playAudioByText
  };
};

export function readAudioStream({
  audio,
  stream,
  contentType = 'audio/mpeg'
}: {
  audio: HTMLAudioElement;
  stream: ReadableStream<Uint8Array>;
  contentType?: string;
}): Promise<Uint8Array> {
  if (typeof MediaSource === 'undefined') {
    let u8Arr: Uint8Array = new Uint8Array(0);
    const reader = stream.getReader();
    return new Promise<Uint8Array>(async (resolve, reject) => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            const blob = new Blob([u8Arr], { type: contentType });
            const audioUrl = URL.createObjectURL(blob);
            audio.src = audioUrl;
            audio.play();
            resolve(u8Arr);
            return;
          }

          const newU8Arr = new Uint8Array(u8Arr.length + value.length);
          newU8Arr.set(u8Arr);
          newU8Arr.set(value, u8Arr.length);
          u8Arr = newU8Arr;
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // Create media source and play audio
  const ms = new MediaSource();
  const url = URL.createObjectURL(ms);
  audio.src = url;
  audio.play();

  let u8Arr: Uint8Array = new Uint8Array();
  return new Promise<Uint8Array>(async (resolve, reject) => {
    // Async to read data from ms
    await new Promise((resolve) => {
      ms.onsourceopen = resolve;
    });

    const sourceBuffer = ms.addSourceBuffer(contentType);

    const reader = stream.getReader();

    // read stream
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          resolve(u8Arr);
          if (sourceBuffer.updating) {
            await new Promise((resolve) => (sourceBuffer.onupdateend = resolve));
          }
          ms.endOfStream();
          return;
        }

        u8Arr = new Uint8Array([...u8Arr, ...value]);

        await new Promise((resolve) => {
          sourceBuffer.onupdateend = resolve;
          sourceBuffer.appendBuffer(value.buffer as ArrayBuffer);
        });
      }
    } catch (error) {
      reject(error);
    }
  });
}
export function playAudioBuffer({
  audio,
  buffer
}: {
  audio: HTMLAudioElement;
  buffer: Uint8Array;
}) {
  const audioUrl = URL.createObjectURL(new Blob([buffer], { type: 'audio/mpeg' }));

  audio.src = audioUrl;
  audio.play();
}
