#nprogress .bar {
  background: '#1237b3' !important; //自定义颜色
}

@import '../../public//icon/iconfont/iconfont.css';

.textEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.textEllipsis2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.homeDescription {
  font-weight: 400;
  color: #606266;
  text-align: left;
}

.textEllipsis3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.grecaptcha-badge {
  display: none !important;
}
.textlg {
  background: linear-gradient(to bottom right, #1237b3 0%, #3370ff 40%, #4e83fd 80%, #85b1ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

span[tabindex='0'] {
  line-height: 1;
}

@keyframes zoomStopIcon {
  0% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1.2);
  }
}
