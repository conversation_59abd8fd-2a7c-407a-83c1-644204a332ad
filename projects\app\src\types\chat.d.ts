import { MessageFileType } from '@/components/ChatBox/MessageInput';

export type ExportChatType = 'md' | 'pdf' | 'html';

export type createInputGuideBody = {
  appId: string;
  textList: string[];
};
export type createInputGuideResponse = {
  insertLength: number;
};
export type updateInputGuideBody = {
  appId: string;
  dataId: string;
  text: string;
};
export type deleteInputGuideBody = { appId: string; dataIdList: string[] };
export type deleteAllInputGuideBody = { appId: string };
export type ChatInputGuideProps = PaginationProps<{
  appId: string;
  searchKey: string;
}>;
export type ChatInputGuideResponse = PaginationResponse<ChatInputGuideSchemaType>;

export type FilesParseProps = {
  files: string[];
  tenantAppId: string;
  chatId: string;
};

export type Document2MdProps = {
  fileKey: string;
};

export type ParseResultProps = {
  UploadedFileParsingResult?: {
    Files: {
      FileContent: string;
      FileName: string;
      FileType?: string;
      FileUrl?: string;
    }[];
  };

  TemplateFileParsingResult?: {
    Files: {
      FileContent: string;
      FileName: string;
      FileType?: string;
      FileUrl?: string;
    }[];
  };
};

export type FormData2ChatDataType = {
  text: string;
  images: MessageFileType[];
  files: MessageFileType[];
  ocrFileKey?: string;
  components: Component[];
};

export type ChatSearchDatasetProps = {
  messages: MessagesType[];
  spaceIds: string[];
};

export type MessagesType = {
  dataId: string;
  content: string;
  role: string;
};

export type ChatSearchDatasetResponse = {
  recalledContent: string;
  sourceName: string;
};
