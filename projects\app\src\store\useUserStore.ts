import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { UserInfoType } from '@/types/store/useUserStore';
import { UpdateUserProps } from '@/types/api/user';
import { getTokenLogin } from '@/api/auth';
import { updateUser } from '@/api/user';
import { setToken } from '@/utils/auth';
import { FeTeamPlanStatusType } from '@/fastgpt/global/support/wallet/sub/type';

type State = {
  userInfo: UserInfoType | null;
  initUserInfo: () => Promise<UserInfoType>;
  setUserInfo: (user: UserInfoType | null) => void;
  teamPlanStatus: FeTeamPlanStatusType | null;
  updateUserInfo: (user: UpdateUserProps & { avatarUrl?: string }) => Promise<void>;
};

export const useUserStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        userInfo: null,
        teamPlanStatus: null,
        async initUserInfo() {
          const res = await getTokenLogin();
          setToken(res.accessToken);
          set((state) => {
            state.userInfo = res;
          });
          return res;
        },
        setUserInfo(user: UserInfoType | null) {
          set((state) => {
            state.userInfo = user;
          });
        },
        async updateUserInfo(user) {
          const oldInfo = (get().userInfo ? { ...get().userInfo } : null) as UserInfoType | null;
          set((state) => {
            if (!state.userInfo) return;
            state.userInfo = {
              ...state.userInfo,
              ...user,
              ...(user.avatarUrl && { avatar: user.avatarUrl })
            };
          });
          try {
            await updateUser(user);
          } catch (error) {
            set((state) => {
              state.userInfo = oldInfo;
            });
            return Promise.reject(error);
          }
        }
      })),
      {
        name: 'userStore',
        partialize: (state) => ({})
      }
    )
  )
);
