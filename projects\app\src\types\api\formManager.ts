export type GetAdminAppFormPageType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean | null;
  appId: string;
  status: number;
  appName: string;
  components: any[]; // 如果有具体的组件类型，可以替换 `any` 为具体类型
  tenantAppId: string;
};

interface FormOption {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  componentId: string;
  content: string;
  sort: number;
}

interface FormComponent {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  formId: string;
  title: string;
  placeholder: string;
  type: number;
  isRequired: number;
  isMultiselect: number;
  isUploadPic: number;
  isUploadText: number;
  maxFiles: number;
  sort: number;
  isCallOcr: number;
  options: FormOption[];
}

export type FormDetail = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: string;
  status: number;
  appName: string;
  components: FormComponent[];
};

// 公共类型定义
export type Option = {
  componentId: number;
  content: string;
  createTime: string; // ISO 8601 date string
  id: number;
  isDeleted: number;
  sort: number;
  updateTime: string; // ISO 8601 date string
};

export type Component = {
  formId: number;
  id: number;
  isCallOcr: number;
  isDeleted: number;
  isMultiselect: number;
  isRequired: number;
  isUploadPic: number;
  isUploadText: number;
  maxFiles: number;
  options: Option[];
  placeholder: string;
  sort: number;
  title: string;
  type: number;
  isWideWindow: number;
  isSideBar: number;
  isTiled: number;
};

// 创建表单参数类型
export type CreateAdminAppFormParams = {
  tenantAppId: string;
  components: Component[];
};

// 更新表单参数类型
export type UpdateAdminAppFormParams = {
  // appId: string;
  tenantAppId: string;
  id: string;
  components: Component[];
};
