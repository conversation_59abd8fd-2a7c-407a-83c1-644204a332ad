import { useState, useCallback } from 'react';
import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import { editSubjectManager } from '@/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';
import { ModalState, NodeInfo, SchoolData } from '../types';
import {
  findNodeById,
  findTeachersByNodeId,
  findMatchingNode,
  extractNodeInfo,
  formatTeacherData,
  generateUniqueId
} from '../utils/dataUtils';
import { MODAL_TITLES, STORAGE_KEYS, PHASE_TYPES } from '../constants';

/**
 * 教师编辑逻辑 Hook
 */
export const useEditTeachers = (
  treeData: Department[],
  schoolData: SchoolData,
  semesterId: string,
  onRefreshData: () => void
) => {
  const [modalState, setModalState] = useState<ModalState>({
    settingId: '',
    title: '',
    deptName: '',
    selectedUsers: []
  });

  /**
   * 处理编辑教师
   */
  const handleEditTeachers = useCallback(
    (
      role: 'leader' | 'phase_leader' | 'teacher',
      id: string | null,
      grade: string,
      subject?: string,
      className?: string,
      stage?: string
    ) => {
      const subjectNode = treeData.find((item) => item.subjectName === subject);
      let nodeData, nodeId;
      if (role === 'teacher') {
        nodeData = findGradeNode(subjectNode, stage, grade);
      } else if (role === 'leader') {
        nodeData = subjectNode;
      } else {
        nodeData = findGradeNode(subjectNode, stage);
      }
      nodeId = nodeData?.deptId || '';
      console.log(subjectNode, stage, nodeData, '================= subjectNode ===============>');

      let title = '';
      let deptName = '';

      if (role === 'leader') {
        title = MODAL_TITLES.SUBJECT_LEADER;
        if (grade === PHASE_TYPES.SCHOOL) {
          deptName = `${subject}`;
        } else {
          deptName = `${grade}${subject}`;
        }
      } else if (role === 'phase_leader') {
        title = MODAL_TITLES.PHASE_LEADER;
        deptName = `${grade}${subject}`;
      } else if (role === 'teacher') {
        title = MODAL_TITLES.TEACHER;
        deptName = `${grade}${subject}`;
      }

      // 查找当前选中的用户数据
      let teacherInfo: TmbUser[] = [];
      console.log('🔍 查找教师数据:', { nodeId, nodeData, grade, subject, role });

      // 根据不同角色类型查找教师数据
      if (role === 'leader' && grade === PHASE_TYPES.SCHOOL) {
        // 总负责人：从第一层科目数据中查找
        const subjectNode = treeData.find((item) => item.subjectName === subject);
        teacherInfo = subjectNode?.tmbUserList || [];
        console.log('📋 总负责人教师数据:', teacherInfo, '来源节点:', subjectNode);
      } else if (role === 'phase_leader') {
        // 学段负责人：从第二层学段数据中查找
        const subjectNode = treeData.find((item) => item.subjectName === subject);
        if (subjectNode?.children) {
          const phaseNode = subjectNode.children.find((child) =>
            child.deptName?.includes(grade.replace('部', ''))
          );
          teacherInfo = phaseNode?.tmbUserList || [];
          console.log('📋 学段负责人教师数据:', teacherInfo, '来源节点:', phaseNode);
        }
      } else if (role === 'teacher') {
        // 年级教师：从第三层年级数据中查找
        const subjectNode = treeData.find((item) => item.subjectName === subject);
        if (subjectNode?.children) {
          for (const phaseNode of subjectNode.children) {
            if (phaseNode.children) {
              const gradeNode = phaseNode.children.find((child) => child.deptName === grade);
              if (gradeNode) {
                teacherInfo = gradeNode.tmbUserList || [];
                console.log('📋 年级教师数据:', teacherInfo, '来源节点:', gradeNode);
                break;
              }
            }
          }
        }
      } else if (nodeId) {
        teacherInfo = findTeachersByNodeId(treeData, nodeId);
        console.log('📋 通过nodeId查找到的教师:', teacherInfo);
      } else if (nodeData) {
        teacherInfo = nodeData.tmbUserList || [];
        console.log('📋 从nodeData获取的教师:', teacherInfo);
      }

      // 转换教师数据格式
      const formattedUsers = formatTeacherData(teacherInfo);
      console.log('✨ 格式化后的用户数据:', formattedUsers);

      // 设置弹窗数据
      setModalState({
        settingId: nodeId,
        title,
        deptName,
        selectedUsers: formattedUsers
      });

      // 存储节点信息
      // 确保 nodeData 不是 undefined，如果是则传入 null
      const safeNodeData = nodeData !== null && nodeData !== undefined ? nodeData : null;
      const nodeInfo = extractNodeInfo(safeNodeData, nodeId, grade, subject);
      try {
        sessionStorage.setItem(STORAGE_KEYS.CURRENT_NODE_DATA, JSON.stringify(nodeInfo));
        console.log('存储节点数据:', nodeInfo);
      } catch (e) {
        console.error('存储节点数据失败:', e);
      }

      return { openModal: true };
    },
    [treeData]
  );

  /**
   * 处理弹窗关闭
   */
  const handleModalClose = useCallback(
    async (submitted: boolean, selectedTmbIds?: number[], deptId?: string) => {
      if (submitted) {
        try {
          let targetDeptId = deptId || modalState.settingId;
          const targetTmbIds = selectedTmbIds || [];
          let teachTypeValue = 0;
          let subjectIdValue = 0;

          console.log('开始处理提交数据:', {
            初始deptId: targetDeptId,
            modalSettingId: modalState.settingId,
            modalDeptName: modalState.deptName,
            selectedTmbIds
          });

          // 从sessionStorage获取保存的节点数据
          try {
            const savedNodeData = sessionStorage.getItem(STORAGE_KEYS.CURRENT_NODE_DATA);
            if (savedNodeData) {
              const nodeData = JSON.parse(savedNodeData);
              console.log('从sessionStorage获取的节点数据:', nodeData);

              teachTypeValue = nodeData.teachType !== undefined ? nodeData.teachType : 0;
              subjectIdValue = nodeData.subjectId !== undefined ? Number(nodeData.subjectId) : 0;

              if (!targetDeptId) {
                targetDeptId = nodeData.deptId || nodeData.id || '';
              }
            }
          } catch (e) {
            console.error('读取保存的节点数据失败:', e);
          }

          // 如果仍然没有找到数据，尝试使用modalDeptName查找
          if ((teachTypeValue === 0 || subjectIdValue === 0) && modalState.deptName) {
            console.log('尝试通过modalDeptName查找数据:', modalState.deptName);

            if (modalState.deptName.includes('总负责人')) {
              const subjectName = modalState.deptName.replace('总负责人', '').trim();
              const matchingNode = treeData.find((item) => item.subjectName === subjectName);

              if (matchingNode) {
                teachTypeValue =
                  (matchingNode as any).teachType !== undefined
                    ? (matchingNode as any).teachType
                    : 0;
                subjectIdValue = matchingNode.subjectId ? Number(matchingNode.subjectId) : 0;

                if (!targetDeptId) {
                  targetDeptId = matchingNode.id || matchingNode.deptId || '';
                }
              }
            } else {
              // 年级或学段格式处理
              const parts = modalState.deptName.split(' ');
              if (parts.length >= 2) {
                const gradePart = parts[0];
                const subjectPart = parts[1];
                const foundNode = findMatchingNode(treeData, gradePart, subjectPart);

                if (foundNode) {
                  teachTypeValue =
                    (foundNode as any).teachType !== undefined ? (foundNode as any).teachType : 0;
                  subjectIdValue = foundNode.subjectId ? Number(foundNode.subjectId) : 0;

                  if (!targetDeptId) {
                    targetDeptId = foundNode.id || foundNode.deptId || '';
                  }
                }
              }
            }
          }

          // 特殊处理总负责人的情况
          if (!targetDeptId && modalState.deptName && modalState.deptName.includes('总负责人')) {
            const subjectName = modalState.deptName.replace('总负责人', '').trim();
            targetDeptId = generateUniqueId('subject-leader', subjectName);
          }

          if (!targetDeptId) {
            Toast.error('获取部门ID失败，请重试');
            return;
          }

          console.log('最终接口参数:', {
            deptId: targetDeptId,
            teachType: teachTypeValue,
            subjectId: subjectIdValue,
            semesterId: Number(semesterId),
            tmbIds: targetTmbIds
          });

          // 调用接口提交数据
          await editSubjectManager({
            deptId: targetDeptId,
            teachType: teachTypeValue,
            subjectId: subjectIdValue,
            semesterId: Number(semesterId),
            tmbIds: targetTmbIds
          });

          Toast.success('操作成功');
          onRefreshData();

          // 清除临时存储的节点数据
          try {
            sessionStorage.removeItem(STORAGE_KEYS.CURRENT_NODE_DATA);
          } catch (e) {
            console.error('清除节点数据失败:', e);
          }
        } catch (error) {
          console.error('提交数据失败:', error);
          Toast.error('操作失败，请重试');
        }
      }

      // 重置弹窗状态
      setModalState({
        settingId: '',
        title: '',
        deptName: '',
        selectedUsers: []
      });
    },
    [modalState, treeData, semesterId, onRefreshData]
  );

  return {
    modalState,
    handleEditTeachers,
    handleModalClose
  };
};

/**
 * 在嵌套的学校组织结构中查找指定学部（和年级）的节点
 * @param data - 根节点数据结构（包含children数组）
 * @param stage - 学部名称（如"小学部"，自动处理后缀）
 * @param grade - 年级名称（如"一年级"，可选参数）
 * @returns 匹配的学部节点或年级节点，未找到返回null
 */
export const findGradeNode = (
  data?: Department,
  stage?: string,
  grade?: string // 改为可选参数[1,4](@ref)
): Department | null => {
  // 规范化学部名称（移除'部'后缀）
  const normalizedStage = stage?.replace(/部$/, '');

  // 检查数据结构有效性[1,4](@ref)
  if (!data?.children || !Array.isArray(data.children)) {
    return null;
  }

  // 第一层遍历：学部节点[5](@ref)
  for (const deptNode of data.children) {
    // 匹配学部名称
    if (deptNode.deptName === normalizedStage) {
      // 未传入年级参数时直接返回学部节点[3,4](@ref)
      if (!grade) return deptNode;

      // 第二层遍历：年级节点[1](@ref)
      if (deptNode.children?.length) {
        for (const gradeNode of deptNode.children) {
          if (gradeNode.deptName === grade) {
            return gradeNode; // 返回精确匹配的年级节点
          }
        }
      }
      break; // 学部匹配但年级未找到
    }
  }
  return null;
};

// Next.js 页面组件默认导出
const UseEditTeachersPage = () => {
  return null;
};
export default UseEditTeachersPage;
