import React, { useState, useRef, useEffect } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { Box } from '@chakra-ui/react';
import { Form, Input, Button, Checkbox, InputNumber, Modal, Radio } from 'antd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';
import SideDrawer from './components/SideDrawer';
import { getAdminAppFormDetail, createAdminAppForm, updateAdminAppForm } from '@/api/formManager';
import { useToast } from '@/hooks/useToast';
import { useRouter } from 'next/router';
import { Select } from 'antd';
import OptionComponent from './components/OptionComponent';
import PageContainer from '@/components/PageContainer';

interface Option {
  id: string;
  type?: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  componentId: string;
  content: string;
  sort: number;
  title?: string;
  placeholder?: string;
  children?: Option[]; // 确保 children 是 Option[] 类型
  options?: Option[]; // 确保 options 是 Option[] 类型
}

interface Component {
  id: string | null; // 新增问题时 id 为 null
  createTime: string;
  updateTime: string;
  isDeleted: number;
  formId: string;
  title: string;
  placeholder: string;
  type: number;
  isRequired: number;
  isMultiselect: number;
  isUploadPic: number;
  isUploadText: number;
  isUploadAv: number;
  maxFiles: number;
  sort: number;
  isCallOcr: number;
  options: Option[];
  isTiled?: number; // 默认值
  isWideWindow?: number; // 默认值
  isSideBar?: number; // 默认值
  children?: Option[]; // 添加 children 属性
}

interface FormDetail {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: string;
  status: number;
  appName: string;
  components: Component[]; // 确保 components 是 Component[] 类型
}

const { Option } = Select;

const QuestionnaireForm: React.FC<{
  id: string;
  appId: string;
  tenantAppId: string;
  mode: string;
  onBack?: () => void;
}> = ({ id, appId, tenantAppId, mode: initialMode, onBack }) => {
  const { toast } = useToast();

  const { confirm } = Modal;

  const router = useRouter();

  const { TextArea } = Input;

  const [mode, setMode] = useState(initialMode);

  const [items, setItems] = useState<Component[]>([]);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const endOfListRef = useRef<HTMLDivElement | null>(null);

  const [combineValue, setCombineValue] = useState<string | null>(null); // 定义 combineValue 的类型为 string

  // 动态单选题数据
  const getSingleChoiceOptions = () => {
    return [
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '年级',
        sort: 1
      },
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '册别',
        sort: 2
      },
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '学科',
        sort: 3
      },
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '版本',
        sort: 4
      },
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '单元',
        sort: 5
      },
      {
        id: '',
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '课题',
        sort: 6
      }
    ];
  };

  const addQuestion = (type: number) => {
    const newQuestion: Component = {
      id: null, // 新增问题时 id 为 null
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      formId: '',
      title: '',
      placeholder: '',
      type,
      isRequired: 0,
      isMultiselect: 0,
      isUploadPic: 0,
      isUploadText: 0,
      isUploadAv: 0,
      maxFiles: type === 3 ? 5 : 0, // 默认参数为5
      sort: items.length + 1,
      isCallOcr: 0,
      options: type !== 5 ? [] : [], // 确保这里能获取到选项
      isTiled: type === 1 ? 0 : undefined,
      isWideWindow: type === 1 ? 0 : undefined,
      isSideBar: type === 1 ? 0 : undefined,
      children: type === 5 ? [] : undefined // 仅在 type 为 5 时添加 children，并初始化 options
    };
    setItems([...items, newQuestion]);
    setShouldScroll(true); // 添加新问题时设置滚动标志
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }

    const reorderedItems = Array.from(items);
    const [removed] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, removed);

    // 更新排序
    const updatedItems = reorderedItems.map((item, index) => ({
      ...item,
      sort: index + 1
    }));

    setItems(updatedItems);
  };

  useEffect(() => {
    if (shouldScroll) {
      const timer = setTimeout(() => {
        if (endOfListRef.current) {
          endOfListRef.current.scrollIntoView({ behavior: 'smooth' });
        }
        setShouldScroll(false); // 重置滚动标志
      }, 100); // 延迟100毫秒

      return () => clearTimeout(timer);
    }
  }, [items, shouldScroll]);

  useEffect(() => {
    if (router.query.mode) {
      setMode(router.query.mode as string);
    }
  }, [router.query.mode]);

  useEffect(() => {
    if (mode === 'view') {
      setDrawerVisible(true); // 调用预览功能
    }
  }, [mode]);

  const showConfirm = () => {
    confirm({
      title: '未保存数据',
      content: '您的更新未保存，确认放弃当前更改？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        if (onBack) {
          onBack();
        } else {
          router.back();
        }
      }
    });
  };

  const handleDeleteItem = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1); // 删除指定索引的项
    setItems(newItems); // 更新状态
  };

  const handleAddOption = (itemIndex: number) => {
    const newItems = [...items];
    newItems[itemIndex].options.push({
      id: '',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '',
      sort: newItems[itemIndex].options.length + 1
    });
    setItems(newItems);
  };

  const handleAddSelectOption = (itemIndex: number, optionIndex: number) => {
    const newItems = [...items] as any;
    newItems[itemIndex].children[optionIndex].options.push({
      id: '',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '',
      sort: newItems[itemIndex].children[optionIndex].options.length + 1
    });

    setItems(newItems);
  };

  const handleDragEndOptions = (result: any, itemIndex: number) => {
    if (!result.destination) return;

    const newItems = [...items];
    const options = Array.from(newItems[itemIndex].options);
    const [reorderedItem] = options.splice(result.source.index, 1);
    options.splice(result.destination.index, 0, reorderedItem);

    // 更新选项排序
    const updatedOptions = options.map((option, index) => ({
      ...option,
      sort: index + 1
    }));

    newItems[itemIndex].options = updatedOptions;
    setItems(newItems);
  };

  const handleOptionChange = (itemIndex: number, optionIndex: number, value: string) => {
    const newItems = [...items];
    newItems[itemIndex].options[optionIndex].content = value;
    setItems(newItems);
  };

  const handleOptionChange1 = (
    itemIndex: number,
    optionIndex: number,
    field: string,
    value: string
  ) => {
    const newItems: any = [...items];
    newItems[itemIndex].children[optionIndex][field] = value;
    setItems(newItems);
  };

  const handleSelectOptionChange = (
    itemIndex: number,
    optionIndex: number,
    optIndex: number,
    value: string
  ) => {
    const newItems = [...items] as any;
    newItems[itemIndex].children[optionIndex].options[optIndex].content = value;
    setItems(newItems);
  };

  // 删除type为5的children的项
  const handleDeleteChildren = (itemIndex: number, optionIndex: number) => {
    const newItems = [...items] as any;
    newItems[itemIndex].children.splice(optionIndex, 1);
    setItems(newItems);
  };

  // 删除type为5的children的下拉项
  const handleDeleteSelectOption = (itemIndex: number, optionIndex: number, optIndex: number) => {
    const newItems = [...items] as any;
    newItems[itemIndex].children[optionIndex].options.splice(optIndex, 1);
    setItems(newItems);
  };

  const handleDeleteOption = (itemIndex: number, optionIndex: number) => {
    const newItems = [...items];
    newItems[itemIndex].options.splice(optionIndex, 1);
    setItems(newItems);
  };

  const handleSubmit = async () => {
    const components = items as any; // 类型断言

    try {
      const result = id
        ? await updateAdminAppForm({ id, tenantAppId, components })
        : await createAdminAppForm({ tenantAppId, components });

      if (result) {
        toast({
          title: '操作成功',
          status: 'success'
        });
        setTimeout(() => {
          if (onBack) {
            onBack();
          } else {
            router.back();
          }
        }, 800);
      }
    } catch (error) {}
  };

  const componentsClass = {
    width: '198px',
    height: '44px',
    padding: '0 12px',
    marginTop: '16px',
    fontWeight: 500,
    fontSize: '14px',
    color: '#1D2129',
    background: '#F8FAFC',
    borderRadius: '8px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    border: '1px solid #F3F5FA',
    cursor: 'pointer'
  };

  const labelClass = {
    fontWeight: 500,
    display: 'bolk',
    marginBottom: '10px',
    fontSize: '14px',
    color: '#303133'
  };

  const lineClass: React.CSSProperties = {
    width: '100%',
    height: '1px',
    background: '#E5E7EB',
    position: 'absolute',
    top: '0px',
    left: '0'
  };

  useEffect(() => {
    if (id) {
      // 调用详情接口
      getAdminAppFormDetail(id as string)
        .then((response) => {
          const formData: FormDetail = response as FormDetail; // 类型转换
          // 根据接口返回的数据更新表单项
          setItems(formData.components);
        })
        .catch((error) => {
          console.error('Failed to fetch form details:', error);
        });
    }
  }, [id]);

  const handleCombineOptions = (itemIndex: number, type: string) => {
    const newItems: any = items.map((item, index) =>
      index === itemIndex ? { ...item, children: item.children || [] } : item
    );

    if (type === '1') {
      newItems[itemIndex].children.push({
        id: '',
        type: 1,
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '',
        sort: newItems[itemIndex].children.length + 1,
        options: [] // 确保 options 是 Option[] 类型
      });
    } else if (type === '4') {
      newItems[itemIndex].children.push({
        id: '',
        type: 4,
        createTime: '',
        updateTime: '',
        isDeleted: 0,
        componentId: '',
        content: '',
        sort: newItems[itemIndex].children.length + 1
      });
    }

    setItems(newItems);
  };

  return (
    <Box bgColor="#f7f9fb" h="100%">
      <SideDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        items={items as any}
        layout="narrow"
      />
      <Box
        style={{
          display: 'flex',
          height: '88vh'
        }}
      >
        <Box
          bg="#fff"
          style={{
            padding: 20,
            borderRadius: '20px',
            marginRight: '16px',
            width: '238px'
          }}
        >
          <Box fontWeight="500" fontSize="16px" color="#303133">
            组件
          </Box>
          <Box style={componentsClass} onClick={() => addQuestion(1)}>
            <Box display="flex" alignItems="center">
              <SvgIcon name="selectIcon" w="20px" h="20px" mr="10px" />
              选择题
            </Box>
            <SvgIcon name="plus" w="16px" h="16px" color="#3366FF" />
          </Box>
          <Box style={componentsClass} onClick={() => addQuestion(2)}>
            <Box display="flex" alignItems="center">
              <SvgIcon name="textIcon" w="20px" h="20px" mr="10px" />
              文本框
            </Box>
            <SvgIcon name="plus" w="16px" h="16px" color="#3366FF" />
          </Box>
          <Box style={componentsClass} onClick={() => addQuestion(3)}>
            <Box display="flex" alignItems="center">
              <SvgIcon name="uploadIcon" w="20px" h="20px" mr="10px" />
              上传文件按钮
            </Box>
            <SvgIcon name="plus" w="16px" h="16px" color="#3366FF" />
          </Box>
          <Box style={componentsClass} onClick={() => addQuestion(4)}>
            <Box display="flex" alignItems="center">
              <SvgIcon name="uploadIcon" w="20px" h="20px" mr="10px" />
              动态单选题
            </Box>
            <SvgIcon name="plus" w="16px" h="16px" color="#3366FF" />
          </Box>

          <Box style={componentsClass} onClick={() => addQuestion(5)}>
            <Box display="flex" alignItems="center">
              <SvgIcon name="uploadIcon" w="20px" h="20px" mr="10px" />
              选项组合
            </Box>
            <SvgIcon name="plus" w="16px" h="16px" color="#3366FF" />
          </Box>
        </Box>

        <Box
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="formItems">
              {(provided) => (
                <Box
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  style={{ height: '80vh', overflowY: 'auto', paddingRight: 20 }}
                >
                  <Form className={styles['my-form']} layout="vertical">
                    {items.map((item, itemIndex) => (
                      <Draggable
                        key={item.id || `new-${itemIndex}`}
                        draggableId={item.id || `new-${itemIndex}`}
                        index={itemIndex}
                      >
                        {(provided) => (
                          <Box
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={{
                              backgroundColor: '#FFF',
                              borderRadius: '20px',
                              padding: '20px',
                              marginBottom: '16px',
                              ...provided.draggableProps.style
                            }}
                          >
                            <Form.Item
                              label={
                                <Box
                                  style={{ fontWeight: 600, fontSize: '16px', color: '#303133' }}
                                >
                                  {item.type === 1
                                    ? '选择题'
                                    : item.type === 2
                                      ? '文本框'
                                      : item.type === 3
                                        ? '上传文件按钮'
                                        : item.type === 4
                                          ? '动态单选题'
                                          : item.type === 5
                                            ? '选项组合'
                                            : '单选题'}
                                </Box>
                              }
                            >
                              {item.type === 1 && (
                                <Box>
                                  <SvgIcon
                                    name="promptTrash"
                                    w="18px"
                                    h="18px"
                                    color="#F53F3F"
                                    position="absolute"
                                    right="12px"
                                    top="-36px"
                                    onClick={() => handleDeleteItem(itemIndex)}
                                  />
                                  <Box style={lineClass}></Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件标题</label>
                                    <Input
                                      placeholder="请输入组件标题"
                                      maxLength={40}
                                      value={item.title}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].title = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件提示</label>
                                    <Input
                                      placeholder="请输入组件提示"
                                      style={{ marginBottom: '10px' }}
                                      value={item.placeholder}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].placeholder = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ marginBottom: '16px' }}>
                                    <label style={labelClass}>选项</label>
                                    <DragDropContext
                                      onDragEnd={(result) =>
                                        handleDragEndOptions(result, itemIndex)
                                      }
                                    >
                                      <Droppable droppableId="options">
                                        {(provided) => (
                                          <Box {...provided.droppableProps} ref={provided.innerRef}>
                                            {item.options.map((option, optionIndex) => (
                                              <Draggable
                                                key={optionIndex}
                                                draggableId={optionIndex.toString()}
                                                index={optionIndex}
                                              >
                                                {(provided) => (
                                                  <Box
                                                    ref={provided.innerRef}
                                                    {...provided.draggableProps}
                                                    {...provided.dragHandleProps}
                                                    style={{
                                                      ...provided.draggableProps.style,
                                                      marginBottom: '10px',
                                                      display: 'flex',
                                                      alignItems: 'center'
                                                    }}
                                                  >
                                                    <SvgIcon
                                                      name="drag"
                                                      w="16px"
                                                      h="16px"
                                                      mr="12px "
                                                    />
                                                    <Input
                                                      placeholder="输入选项"
                                                      value={option.content}
                                                      onChange={(e) =>
                                                        handleOptionChange(
                                                          itemIndex,
                                                          optionIndex,
                                                          e.target.value
                                                        )
                                                      }
                                                    />
                                                    <SvgIcon
                                                      name="promptTrash"
                                                      w="16px"
                                                      h="16px"
                                                      ml="16px"
                                                      zIndex="999"
                                                      onMouseDown={(event) => {
                                                        event.stopPropagation(); // 阻止事件传播
                                                        handleDeleteOption(itemIndex, optionIndex);
                                                      }}
                                                    />
                                                  </Box>
                                                )}
                                              </Draggable>
                                            ))}
                                            {provided.placeholder}
                                            <Box ref={endOfListRef} />
                                          </Box>
                                        )}
                                      </Droppable>
                                    </DragDropContext>

                                    <Box
                                      display="flex"
                                      alignItems="center"
                                      color="#3366FF"
                                      cursor="pointer"
                                      onClick={() => handleAddOption(itemIndex)}
                                    >
                                      <SvgIcon name="plus" w="16px" h="16px" marginRight="8px" />
                                      添加选项
                                    </Box>
                                  </Box>

                                  <label style={labelClass}>配置项</label>
                                  <Box>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isRequired === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isRequired = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      必填
                                    </Checkbox>
                                    <Checkbox
                                      checked={item.isMultiselect === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isMultiselect = e.target.checked
                                          ? 1
                                          : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      支持多选
                                    </Checkbox>

                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isTiled === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isTiled = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      宽窗口平铺
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isWideWindow === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isWideWindow = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      宽窗口显示标题
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isSideBar === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isSideBar = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      侧边栏显示标题
                                    </Checkbox>
                                  </Box>
                                </Box>
                              )}

                              {item.type === 2 && (
                                <Box>
                                  <SvgIcon
                                    name="promptTrash"
                                    w="18px"
                                    h="18px"
                                    color="#F53F3F"
                                    position="absolute"
                                    right="12px"
                                    top="-36px"
                                    onClick={() => handleDeleteItem(itemIndex)}
                                  />
                                  <Box style={lineClass}></Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件标题</label>
                                    <TextArea
                                      placeholder="请输入组件标题"
                                      maxLength={40}
                                      autoSize={{ minRows: 1 }}
                                      value={item.title}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].title = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件提示</label>
                                    <TextArea
                                      placeholder="请输入组件提示"
                                      style={{ marginBottom: '10px' }}
                                      autoSize={{ minRows: 1 }}
                                      value={item.placeholder}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].placeholder = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <label style={labelClass}>配置项</label>
                                  <Box>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isRequired === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isRequired = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      必填
                                    </Checkbox>
                                  </Box>
                                </Box>
                              )}

                              {item.type === 3 && (
                                <Box>
                                  <SvgIcon
                                    name="promptTrash"
                                    w="18px"
                                    h="18px"
                                    color="#F53F3F"
                                    position="absolute"
                                    right="12px"
                                    top="-36px"
                                    onClick={() => handleDeleteItem(itemIndex)}
                                  />
                                  <Box style={lineClass}></Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件标题</label>
                                    <Input
                                      placeholder="请输入组件标题"
                                      maxLength={40}
                                      value={item.title}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].title = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件提示</label>
                                    <Input
                                      placeholder="请输入组件提示"
                                      style={{ marginBottom: '10px' }}
                                      value={item.placeholder}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].placeholder = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <label style={labelClass}>配置项</label>
                                  <Box display="flex" alignItems="center">
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isRequired === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isRequired = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      必填
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isUploadPic === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isUploadPic = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      支持上传图片
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isUploadText === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isUploadText = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      支持上传文档
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isUploadAv === 1}
                                      disabled={item.isCallOcr === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isUploadAv = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      支持上传音视频
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isCallOcr === 1}
                                      disabled={item.isUploadAv === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isCallOcr = e.target.checked ? 1 : 0;
                                        if (e.target.checked) {
                                          newItems[itemIndex].maxFiles = 1; // 设置 maxFiles 为 1
                                        }
                                        setItems(newItems);
                                      }}
                                    >
                                      上传文件作为模版
                                    </Checkbox>
                                    <Box>
                                      支持上传
                                      <InputNumber
                                        min={1}
                                        max={20}
                                        value={item.isCallOcr === 1 ? 1 : item.maxFiles} // 如果 isCallOcr 被勾选，值重置为 1
                                        style={{ margin: '0 10px' }}
                                        onChange={(value) => {
                                          const newItems = [...items];
                                          newItems[itemIndex].maxFiles = value as number;
                                          setItems(newItems);
                                        }}
                                        disabled={item.isCallOcr === 1} // 禁用 InputNumber 如果 isCallOcr 被勾选
                                      ></InputNumber>
                                      个文件
                                    </Box>
                                  </Box>
                                </Box>
                              )}

                              {item.type === 4 && (
                                <Box>
                                  <SvgIcon
                                    name="promptTrash"
                                    w="18px"
                                    h="18px"
                                    color="#F53F3F"
                                    position="absolute"
                                    right="12px"
                                    top="-36px"
                                    onClick={() => handleDeleteItem(itemIndex)}
                                  />
                                  <Box style={lineClass}></Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件标题</label>
                                    <Input
                                      placeholder="请输入组件标题"
                                      maxLength={40}
                                      value={item.title}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].title = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组件提示</label>
                                    <Input
                                      placeholder="请输入组件提示"
                                      style={{ marginBottom: '10px' }}
                                      value={item.placeholder}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].placeholder = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>选择表单字段</label>
                                    <Select
                                      placeholder="请选择表单字段"
                                      style={{ width: '400px', display: 'block' }}
                                      value={
                                        items[itemIndex].options.length > 0
                                          ? items[itemIndex].options[0].content
                                          : undefined
                                      } // 回显选中的值
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        const allOptions = getSingleChoiceOptions();
                                        const selectedOptions = allOptions.filter(
                                          (option) => option.content === e
                                        );
                                        newItems[itemIndex].options = selectedOptions; // 将匹配到的项数组赋值给 options
                                        setItems(newItems);
                                      }}
                                    >
                                      {getSingleChoiceOptions().map((option) => (
                                        <Option key={option.content} value={option.content}>
                                          {option.content}
                                        </Option>
                                      ))}
                                    </Select>
                                  </Box>

                                  <label style={labelClass}>配置项</label>
                                  <Box>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isRequired === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isRequired = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      必填
                                    </Checkbox>
                                    <Checkbox
                                      checked={item.isMultiselect === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isMultiselect = e.target.checked
                                          ? 1
                                          : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      支持多选
                                    </Checkbox>

                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isTiled === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isTiled = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      宽窗口平铺
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isWideWindow === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isWideWindow = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      宽窗口显示标题
                                    </Checkbox>
                                    <Checkbox
                                      style={{ marginRight: '10px' }}
                                      checked={item.isSideBar === 1}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].isSideBar = e.target.checked ? 1 : 0;
                                        setItems(newItems);
                                      }}
                                    >
                                      侧边栏显示标题
                                    </Checkbox>
                                  </Box>
                                </Box>
                              )}

                              {item.type === 5 && (
                                <Box>
                                  <SvgIcon
                                    name="promptTrash"
                                    w="18px"
                                    h="18px"
                                    color="#F53F3F"
                                    position="absolute"
                                    right="12px"
                                    top="-36px"
                                    onClick={() => handleDeleteItem(itemIndex)}
                                  />
                                  <Box style={lineClass}></Box>

                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <label style={labelClass}>组合标题</label>
                                    <Input
                                      placeholder="请输入组合标题"
                                      maxLength={40}
                                      value={item.title}
                                      onChange={(e) => {
                                        const newItems = [...items];
                                        newItems[itemIndex].title = e.target.value;
                                        setItems(newItems);
                                      }}
                                    />
                                  </Box>

                                  {item.children &&
                                    item.children.map((child, childIndex) => (
                                      <OptionComponent
                                        key={childIndex}
                                        option={child}
                                        optionIndex={childIndex}
                                        itemIndex={itemIndex}
                                        handleOptionChange={handleOptionChange1}
                                        handleSelectOptionChange={handleSelectOptionChange}
                                        handleDeleteSelectOption={handleDeleteSelectOption}
                                        handleDeleteChildren={handleDeleteChildren}
                                        handleDragEndOptions={handleDragEndOptions}
                                        handleAddSelectOption={handleAddSelectOption}
                                        endOfListRef={endOfListRef}
                                      />
                                    ))}
                                  <Box style={{ margin: '20px 0 10px 0' }}>
                                    <Select
                                      placeholder="请添加模块"
                                      style={{
                                        width: '188px',
                                        height: '42px',
                                        backgroundColor: '#fff',
                                        borderRadius: '8px'
                                      }}
                                      value={combineValue}
                                      onChange={(value) => {
                                        handleCombineOptions(itemIndex, value); // 处理选中的值
                                        setTimeout(() => {
                                          setCombineValue(null);
                                        }, 500);
                                      }}
                                    >
                                      <Select.Option value="1">选择题</Select.Option>
                                      <Select.Option value="4">动态单选题</Select.Option>
                                    </Select>
                                  </Box>
                                </Box>
                              )}
                            </Form.Item>
                          </Box>
                        )}
                      </Draggable>
                    ))}

                    {provided.placeholder}
                  </Form>
                </Box>
              )}
            </Droppable>
          </DragDropContext>

          <Box style={{ padding: 20, textAlign: 'right' }}>
            <Button type="default" style={{ marginRight: 8 }} onClick={showConfirm}>
              取消
            </Button>
            <Button
              type="primary"
              style={{ marginRight: 8 }}
              onClick={() => setDrawerVisible(true)}
            >
              预览
            </Button>
            <Button type="primary" onClick={() => handleSubmit()}>
              保存
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      id: context.query?.id || '',
      appId: context.query?.appId || '',
      tenantAppId: context.query?.tenantAppId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default QuestionnaireForm;
