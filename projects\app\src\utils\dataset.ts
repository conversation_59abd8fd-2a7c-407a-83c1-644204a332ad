import { getFileViewUrl } from '@/api/dataset';
import { strIsLink } from '@/utils/string';
import { getFileIcon } from './file/icon';
import { useSystemStore } from '@/store/useSystemStore';

export async function getFileAndOpen(fileId: string) {
  if (strIsLink(fileId)) {
    return window.open(fileId, '_blank');
  }
  const url = await getFileViewUrl(fileId);
  const asPath = `${location.origin}${url}`;
  window.open(asPath, '_blank');
}

export function getSourceNameIcon({
  sourceName,
  sourceId
}: {
  sourceName: string;
  sourceId?: string;
}) {
  if (strIsLink(sourceId)) {
    return 'common/linkBlue';
  }
  const fileIcon = getFileIcon(sourceName, '');
  if (fileIcon) {
    return fileIcon;
  }

  return 'file/fill/manual';
}

export const getWebLLMModel = (model?: string) => {
  const list = useSystemStore.getState().llmModelList;
  return list.find((item) => item.model === model || item.name === model) ?? list[0];
};
