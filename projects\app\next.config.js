/** @type {import('next').NextConfig} */
const { i18n } = require('./next-i18next.config');
const path = require('path');

const nextConfig = {
  i18n,
  output: 'standalone',
  reactStrictMode: process.env.NODE_ENV === 'development' ? false : true,
  compress: true,
  rewrites:
    process.env.NODE_ENV === 'development'
      ? async () => {
          return [
            {
              source: '/huayun-ai/:path*',
              destination: `${process.env.API_URL}/huayun-ai/:path*`
            },
            {
              source: '/composition-correction/:path*',
              destination: `${process.env.API_URL}/composition-correction/:path*`
            },
            {
              source: '/huayun-tool/:path*',
              destination: `${process.env.CONVERT_SERVICE_URL}/huayun-tool/:path*`
            },
            {
              source: '/huayun-mooc/:path*',
              destination: `${process.env.API_URL}/huayun-mooc/:path*`
            },
            {
              source: '/ai-resource/:path*',
              destination: `${process.env.API_URL}/ai-resource/:path*`
            },
            {
              source: '/ai-homework/:path*',
              destination: `${process.env.API_URL}/ai-homework/:path*`
            },
            {
              source: '/ai-questionnaire/:path*',
              destination: `${process.env.QUESTIONNAIRE_API_URL}/ai-questionnaire/:path*`
            },
            {
              source: '/ai-notice/:path*',
              destination: `${process.env.API_URL}/ai-notice/:path*`
            }
          ];
        }
      : undefined,
  webpack(config, { isServer }) {
    if (!isServer) {
      config.resolve = {
        ...config.resolve,
        fallback: {
          ...config.resolve.fallback,
          fs: false
        }
      };
    }
    Object.assign(config.resolve.alias, {
      '@mongodb-js/zstd': false,
      '@aws-sdk/credential-providers': false,
      snappy: false,
      aws4: false,
      'mongodb-client-encryption': false,
      kerberos: false,
      'supports-color': false,
      'bson-ext': false,
      'pg-native': false
    });
    config.module = {
      ...config.module,
      rules: config.module.rules.concat([
        {
          test: /\.svg$/i,
          issuer: /\.[jt]sx?$/,
          use: ['@svgr/webpack']
        }
      ]),
      exprContextCritical: false,
      unknownContextCritical: false
    };

    return config;
  },
  transpilePackages: ['@fastgpt/*', 'ahooks'],
  experimental: {
    proxyTimeout: 900_000,
    serverComponentsExternalPackages: [
      'mongoose',
      'pg',
      'react',
      '@chakra-ui/react',
      '@lexical/react'
    ],
    outputFileTracingRoot: path.join(__dirname, '../../')
  }
};

module.exports = nextConfig;
