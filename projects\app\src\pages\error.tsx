// ErrorPage.jsx
import React from 'react';
import { Box, Heading, Text, Button, Flex } from '@chakra-ui/react';
import { serviceSideProps } from '@/utils/i18n';
import Lottie from '@/components/Lottie';
import { respDims, rpxDim } from '@/utils/chakra';

function ErrorPage({ message }: { message: string }) {
  const query = new URLSearchParams(location.search);
  const errorMessage = message || query.get('message') || '发生错误';

  return (
    <Flex
      mt={respDims(-80)}
      direction="column"
      justifyContent="center"
      alignItems="center"
      h="100%"
    >
      <Lottie name="error" />
      <Text
        display="inline"
        fontSize="24px"
        maxWidth={rpxDim(400)}
        mt={respDims(-60)}
        mb={2}
        fontWeight="bold"
        whiteSpace="pre-wrap"
        wordBreak="break-word"
      >
        {errorMessage}
      </Text>
    </Flex>
  );
}

export async function getServerSideProps(context: any) {
  return {
    props: {
      message: context.query?.message || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default ErrorPage;
