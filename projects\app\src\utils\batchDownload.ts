import JSZip from 'jszip';

export interface DownloadFile {
  name: string;
  url: string;
  size?: number;
}

export interface BatchDownloadOptions {
  files: DownloadFile[];
  zipName?: string;
  onProgress?: (progress: number) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

/**
 * 前端批量下载文件并打包成zip
 * @param options 下载选项
 */
export const batchDownloadFiles = async (options: BatchDownloadOptions): Promise<void> => {
  const { files, zipName = 'download.zip', onProgress, onError, onComplete } = options;

  if (!files || files.length === 0) {
    onError?.(new Error('没有文件需要下载'));
    return;
  }

  try {
    const zip = new JSZip();
    let completedCount = 0;

    // 创建下载进度跟踪
    const updateProgress = () => {
      const progress = (completedCount / files.length) * 100;
      onProgress?.(progress);
    };

    // 并发下载文件
    const downloadPromises = files.map(async (file, index) => {
      try {
        // 获取文件内容
        const response = await fetch(file.url);
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status} ${response.statusText}`);
        }

        const blob = await response.blob();

        // 添加到zip
        zip.file(file.name, blob);

        completedCount++;
        updateProgress();

        return { success: true, file: file.name };
      } catch (error) {
        console.error(`下载文件失败: ${file.name}`, error);
        return { success: false, file: file.name, error };
      }
    });

    // 等待所有文件下载完成
    const results = await Promise.all(downloadPromises);

    // 检查是否有下载失败的文件
    const failedFiles = results.filter((result) => !result.success);
    if (failedFiles.length > 0) {
      console.warn('部分文件下载失败:', failedFiles);
    }

    // 生成zip文件
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: { level: 6 }
    });

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(zipBlob);
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.download = zipName;

    // 触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // 清理URL对象
    URL.revokeObjectURL(downloadUrl);

    onComplete?.();
  } catch (error) {
    console.error('批量下载失败:', error);
    onError?.(error as Error);
  }
};

/**
 * 从文件列表创建下载文件数组
 * @param fileList 文件列表
 * @returns 下载文件数组
 */
export const createDownloadFilesFromList = (fileList: any[]): DownloadFile[] => {
  return fileList.map((file) => ({
    name: file.fileName || file.name,
    url: file.fileUrl || file.url,
    size: file.fileSize || file.size
  }));
};

/**
 * 检查文件大小限制
 * @param files 文件列表
 * @param maxSize 最大大小限制（字节）
 * @returns 是否超过限制
 */
export const checkFileSizeLimit = (
  files: DownloadFile[],
  maxSize: number = 100 * 1024 * 1024
): boolean => {
  const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
  return totalSize <= maxSize;
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
