import React, { useEffect, useState } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Flex,
  Button,
  Input,
  Text,
  CloseButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody
} from '@chakra-ui/react';
import { Select } from 'antd';
import { useF<PERSON>, Controller, SubmitHandler } from 'react-hook-form';
import { createSchoolDept } from '@/api/tenant/teamManagement/administration';
import { DeptTreeResponse } from '@/types/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';

interface AddClassFormData {
  schoolSection: string; // 学段ID
  grade: string; // 年级ID
  className: string; // 班级名称
}

interface AddClassModalProps {
  isOpen: boolean;
  onClose: () => void;
  semesterId: string; // 学期ID
  treeData: DeptTreeResponse | DeptTreeResponse[] | null; // 树形数据
  onRefresh?: () => void; // 刷新父组件数据的回调
}

const AddClassModal: React.FC<AddClassModalProps> = ({
  isOpen,
  onClose,
  semesterId,
  treeData,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [schoolSectionOptions, setSchoolSectionOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [gradeOptions, setGradeOptions] = useState<{ label: string; value: string }[]>([]);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<AddClassFormData>({
    defaultValues: {
      schoolSection: '',
      grade: '',
      className: ''
    }
  });

  const watchedSchoolSection = watch('schoolSection');

  // 从树形数据中提取学段和年级选项
  useEffect(() => {
    if (!treeData) return;

    const extractOptions = (data: DeptTreeResponse | DeptTreeResponse[]) => {
      const sections: { label: string; value: string }[] = [];

      const processData = Array.isArray(data) ? data : [data];

      processData.forEach((section) => {
        // 学段级别的数据
        sections.push({
          label: section.deptName,
          value: section.id
        });
      });

      setSchoolSectionOptions(sections);
    };

    extractOptions(treeData);
  }, [treeData]);

  // 当学段改变时，更新年级选项
  useEffect(() => {
    if (!treeData) {
      setGradeOptions([]);
      return;
    }

    const processData = Array.isArray(treeData) ? treeData : [treeData];

    if (!watchedSchoolSection) {
      // 如果没有选择学段，显示所有年级
      const allGrades: { label: string; value: string }[] = [];
      processData.forEach((section) => {
        if (section.children) {
          section.children.forEach((grade) => {
            allGrades.push({
              label: `${section.deptName} - ${grade.deptName}`,
              value: grade.id
            });
          });
        }
      });
      setGradeOptions(allGrades);
    } else {
      // 如果选择了学段，只显示该学段下的年级
      const selectedSection = processData.find((section) => section.id === watchedSchoolSection);

      if (selectedSection && selectedSection.children) {
        const grades = selectedSection.children.map((grade) => ({
          label: grade.deptName,
          value: grade.id
        }));
        setGradeOptions(grades);
      } else {
        setGradeOptions([]);
      }
      setValue('grade', '');
    }
  }, [watchedSchoolSection, treeData, setValue]);

  const onSubmit: SubmitHandler<AddClassFormData> = async (data) => {
    if (!semesterId) {
      Toast.error('学期ID不能为空');
      return;
    }

    try {
      setLoading(true);
      await createSchoolDept({
        semesterId: parseInt(semesterId),
        parentId: parseInt(data.grade),
        name: data.className
      });

      Toast.success('班级创建成功');

      // 调用父组件的刷新函数
      if (onRefresh) {
        onRefresh();
      }

      handleCancel();
    } catch (error) {
      console.error('创建班级失败:', error);
      Toast.error('创建班级失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} isCentered size="lg">
      <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
      <ModalContent bg="white" borderRadius="8px" minW="600px" maxW="600px">
        <ModalBody p={0}>
          {/* 标题栏 */}
          <Flex
            justifyContent="space-between"
            alignItems="center"
            p="24px 24px 20px 24px"
            borderBottom="1px solid #F2F3F5"
          >
            <Text fontSize="18px" fontWeight="500" color="#1D2129">
              添加班级
            </Text>
            <CloseButton onClick={handleCancel} />
          </Flex>

          {/* 表单内容 */}
          <Box p="32px">
            {/* 学段选择 */}
            <FormControl isInvalid={!!errors.schoolSection} mb="24px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    学段
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="schoolSection"
                    control={control}
                    rules={{ required: '请选择学段' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        placeholder="请选择学段"
                        style={{
                          width: '100%',
                          height: '48px',
                          borderRadius: '8px'
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        suffixIcon={
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path
                              d="M3 4.5L6 7.5L9 4.5"
                              stroke="#C9CDD4"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        }
                      >
                        {schoolSectionOptions.map((option) => (
                          <Select.Option key={option.value} value={option.value}>
                            {option.label}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  />
                </Box>
              </Flex>
              {errors.schoolSection && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.schoolSection.message}
                </Text>
              )}
            </FormControl>

            {/* 年级选择 */}
            <FormControl isInvalid={!!errors.grade} mb="24px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    年级
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="grade"
                    control={control}
                    rules={{ required: '请选择年级' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        placeholder="请选择年级"
                        style={{
                          width: '100%',
                          height: '48px',
                          borderRadius: '8px'
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        suffixIcon={
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path
                              d="M3 4.5L6 7.5L9 4.5"
                              stroke="#C9CDD4"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        }
                      >
                        {gradeOptions.map((option) => (
                          <Select.Option key={option.value} value={option.value}>
                            {option.label}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  />
                </Box>
              </Flex>
              {errors.grade && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.grade.message}
                </Text>
              )}
            </FormControl>

            {/* 班级名称输入 */}
            <FormControl isInvalid={!!errors.className} mb="40px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    班级名称
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="className"
                    control={control}
                    rules={{ required: '请输入班级名称' }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="请输入班级名称"
                        height="48px"
                        border="1px solid #E5E6EB"
                        borderRadius="8px"
                        fontSize="14px"
                        bg="white"
                        _placeholder={{ color: '#C9CDD4' }}
                        _focus={{
                          borderColor: '#7C3AED',
                          boxShadow: '0 0 0 2px rgba(124, 58, 237, 0.1)'
                        }}
                        _hover={{
                          borderColor: '#B794F6'
                        }}
                      />
                    )}
                  />
                </Box>
              </Flex>
              {errors.className && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.className.message}
                </Text>
              )}
            </FormControl>

            {/* 操作按钮 */}
            <Flex justifyContent="flex-end" gap="16px">
              <Button
                variant="outline"
                borderColor="#E5E6EB"
                color="#6B7280"
                bg="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                _hover={{
                  borderColor: '#D1D5DB',
                  bg: '#F9FAFB'
                }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                bg="#7C3AED"
                color="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                isLoading={loading}
                loadingText="保存中..."
                _hover={{
                  bg: '#6D28D9'
                }}
                _active={{
                  bg: '#5B21B6'
                }}
                onClick={handleSubmit(onSubmit)}
              >
                保存
              </Button>
            </Flex>
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default AddClassModal;
