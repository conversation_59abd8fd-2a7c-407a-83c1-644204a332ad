import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { Box, Button, Tab, <PERSON>b<PERSON>ist, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import { useState } from 'react';
import HomeworkScope from './components/HomeworkScope';
import HomeworkContent from './components/HomeworkContent';
import HomeworkRule from './components/HomeworkRule';
import { getHomeTypeDetail, getHomeworkDetail } from '@/api/homeworkDetail';
import { useSearchParams } from 'next/navigation';
import { useHomeworkStore } from '@/store/useHomework';
import { useQuery } from '@tanstack/react-query';
import router from 'next/router';

function HomeworkRequirements() {
  const [tabIndex, setTabIndex] = useState<number>(0);
  const { homeworkDetail } = useHomeworkStore();

  const handleTabsChange = (index: number) => {
    setTabIndex(index);
  };

  const handleEdit = () => {
    const { id, taskType } = homeworkDetail;
    // 跳转到编辑页面
    router.push(`/homeworkManagement/assignmentRelease?editId=${id}&type=${taskType - 1}`);
  };

  return (
    <Box display={'flex'} justifyContent={'center'} padding={`${vwDims(40)} ${vwDims(0)}`}>
      <Box w={vwDims(918)}>
        <Tabs isLazy onChange={handleTabsChange} index={tabIndex}>
          <TabList>
            <Tab
              _selected={{ color: '#1d2129', fontWeight: '600', fontSize: vwDims(18) }}
              fontSize={vwDims(16)}
              fontWeight={'500'}
              color={'#86909C'}
              display={'flex'}
              flexDirection={'column'}
            >
              <Box position={'relative'}>
                作业范围
                {tabIndex === 0 && (
                  <SvgIcon
                    name="homeworkDetailTabs"
                    w={vwDims(84)}
                    h={vwDims(8)}
                    position={'absolute'}
                    bottom={-1.5}
                    left={0}
                  />
                )}
              </Box>
            </Tab>
            <Tab
              _selected={{ color: '#1d2129', fontWeight: '600', fontSize: vwDims(18) }}
              fontSize={vwDims(16)}
              fontWeight={'500'}
              color={'#86909C'}
            >
              <Box position={'relative'}>
                作业内容
                {tabIndex === 1 && (
                  <SvgIcon
                    name="homeworkDetailTabs"
                    w={vwDims(84)}
                    h={vwDims(8)}
                    position={'absolute'}
                    bottom={-1.5}
                    left={0}
                  />
                )}
              </Box>
            </Tab>
            <Tab
              _selected={{ color: '#1d2129', fontWeight: '600', fontSize: vwDims(18) }}
              fontSize={vwDims(16)}
              fontWeight={'500'}
              color={'#86909C'}
            >
              <Box position={'relative'}>
                作业规则
                {tabIndex === 2 && (
                  <SvgIcon
                    name="homeworkDetailTabs"
                    w={vwDims(84)}
                    h={vwDims(8)}
                    position={'absolute'}
                    bottom={-1.5}
                    left={0}
                  />
                )}
              </Box>
            </Tab>
            <Button
              w={vwDims(120)}
              h={vwDims(40)}
              background={'#fff'}
              color={'#7D4DFF'}
              border={'1px solid #7D4DFF'}
              _hover={{}}
              marginLeft={'auto'}
              marginBottom={vwDims(10)}
              onClick={handleEdit}
            >
              <SvgIcon name="editHomework" w={vwDims(14)} h={vwDims(14)} mr={vwDims(8)} />
              编辑
            </Button>
          </TabList>

          <TabPanels>
            <TabPanel padding={0}>
              <HomeworkScope />
            </TabPanel>
            <TabPanel padding={0}>
              <HomeworkContent />
            </TabPanel>
            <TabPanel padding={0}>
              <HomeworkRule />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </Box>
  );
}

export default HomeworkRequirements;
