import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { FileType } from '@/types/api/cloud';
import { getUsageStats } from '@/api/cloud';
import { BizTypeEnum, StatisTypeEnum } from '@/constants/api/cloud';

export type UsageStatsType = {
  total: number;
  used: number;
};

type State = {
  selectFile: FileType | null;
  setSelectFile: (file: FileType | null) => void;

  showFileInfo: boolean;
  setShowFileInfo: (show: boolean) => void;

  usageStats?: UsageStatsType;
  loadUsageStats: (force?: boolean) => Promise<UsageStatsType>;
};

export const useCloudStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        selectFile: null,
        setSelectFile: (file) => {
          set((state) => {
            state.selectFile = file;
          });
        },

        showFileInfo: false,
        setShowFileInfo: (show) => {
          set((state) => {
            state.showFileInfo = show;
          });
        },

        usageStats: undefined,
        loadUsageStats: (force?: boolean) => {
          if (!force && get().usageStats) {
            return Promise.resolve(get().usageStats!);
          }
          return getUsageStats({
            statisType: StatisTypeEnum.Personal
          }).then((res) => {
            const total = 100 * 1024 * 1024 * 1024;
            const stats = {
              total,
              used: Math.min(res, total)
            };
            set((state) => {
              state.usageStats = stats;
            });
            return stats;
          });
        }
      })),
      {
        name: 'cloudStore',
        partialize: (state) => ({
          selectFile: state.selectFile,
          showFileInfo: state.showFileInfo
        })
      }
    )
  )
);
