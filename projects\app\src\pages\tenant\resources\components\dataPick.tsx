import React from 'react';
import { DatePicker } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { RangePickerProps } from 'antd/es/date-picker';
import 'dayjs/locale/zh-cn';
import { rpxDim } from '@/utils/chakra';
const { RangePicker } = DatePicker;

interface DataPickProps {
  value?: [Dayjs, Dayjs];
  onChange?: (dates: [Dayjs, Dayjs] | null, period?: Object) => void;
}

interface PeriodOption {
  period: string;
  desc: string;
}

const DataPick: React.FC<DataPickProps> = ({ value, onChange }) => {
  const getDateRangeByPeriod = (period: string): [Dayjs, Dayjs] => {
    const today = dayjs();
    switch (period) {
      case 'yesterday':
        const yesterday = today.subtract(1, 'd');
        return [yesterday, yesterday];
      case 'before_yesterday':
        const beforeYesterday = today.subtract(2, 'd');
        return [beforeYesterday, beforeYesterday];
      case 'this_week':
        return [today.startOf('week'), today];
      case 'last_week':
        const lastWeekStart = today.subtract(1, 'week').startOf('week');
        const lastWeekEnd = lastWeekStart.endOf('week');
        return [lastWeekStart, lastWeekEnd];
      case 'this_month':
        return [today.startOf('month'), today];
      case 'last_month':
        const lastMonthStart = today.subtract(1, 'month').startOf('month');
        const lastMonthEnd = lastMonthStart.endOf('month');
        return [lastMonthStart, lastMonthEnd];
      case 'last_seven_days':
        return [today.subtract(7, 'd'), today];
      case 'last_thirty_days':
        return [today.subtract(30, 'd'), today];
      default:
        return [today, today];
    }
  };

  // 移除默认范围，让组件初始状态为空
  // const defaultRange = getDateRangeByPeriod('last_seven_days');

  const periodOptions: PeriodOption[] = [
    { period: 'yesterday', desc: '昨日' },
    { period: 'before_yesterday', desc: '前日' },
    { period: 'this_week', desc: '本周' },
    { period: 'last_week', desc: '上周' },
    { period: 'this_month', desc: '本月' },
    { period: 'last_month', desc: '上月' },
    { period: 'last_seven_days', desc: '最近7天' },
    { period: 'last_thirty_days', desc: '最近30日' }
  ];

  const presets = periodOptions.map(({ period, desc }) => ({
    label: desc,
    value: getDateRangeByPeriod(period),
    period
  }));

  const handleChange: RangePickerProps['onChange'] = (dates) => {
    if (!dates || dates[0] === null || dates[1] === null) {
      onChange && onChange(null);
      return;
    }

    const selectedPreset = presets.find(
      (preset) => preset.value[0].isSame(dates[0], 'day') && preset.value[1].isSame(dates[1], 'day')
    );
    console.log('selectedPreset', selectedPreset);
    onChange && onChange([dates[0], dates[1]], selectedPreset?.period);
  };

  return (
    <RangePicker
      value={value || undefined}
      onChange={handleChange}
      presets={presets}
      allowClear={true}
      placeholder={['开始日期', '结束日期']}
      style={{
        width: rpxDim(240),
        height: rpxDim(40),
        borderRadius: '8px',
        background: 'rgba(0,0,0,0.03)',
        border: 'none'
      }}
      format="YYYY-MM-DD"
    />
  );
};

export default DataPick;
