import React from 'react';
import {
  <PERSON>dal,
  <PERSON>dalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Button,
  Text,
  Image,
  Divider,
  IconButton
} from '@chakra-ui/react';
import type { StudentPageItem, StudentDetailResponse } from '@/types/student';
import SvgIcon from '@/components/SvgIcon';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';
import MyBox from '@/components/common/MyBox';

// Next.js 页面组件默认导出
const StudentViewPage = () => {
  return null;
};
export default StudentViewPage;
