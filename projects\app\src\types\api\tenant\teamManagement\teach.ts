import { RequestPageParams } from '@/types';

export type TmbUser = {
  tmbId: string;
  userName: string;
  userId: string;
};

export type ClientSchoolDeptManageTreeParams = {
  semesterId: string;
  teacherName: string;
};

export type DepartmentNode = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  deptId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: number;
  sort: number;
  tmbIds: string;
  semesterId: string;
  tmbUserList: TmbUser[];
  children: DepartmentNode[];
  subjectId: string;
  subjectName: string;
};

export type ClientSchoolDeptManageTreeType = {
  tmbIds: string[];
  treeList: DepartmentNode[];
};
export type ClientSchoolDeptManageValid = {
  isExisted: number;
};
export type ClientSchoolDeptManageBindParams = {
  id: string;
  tmbIds: string;
};

export type ClientSchoolDeptManageCopyParams = {
  sourceSemesterId: string;
  targetSemesterId: string;
};
export type ClientSchoolDeptSubjectManageTreeParams = {
  semesterId: string;
  teacherName: string;
};

export type User = {
  tmbId: string;
  userId: string;
  userName: string;
};

export type Department = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  deptId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: number;
  sort: number;
  subjectId: string;
  tmbIds: string;
  semesterId: string;
  tmbUserList: User[];
  subjectName: string;
  children: Department[];
};

export type ClientSchoolDeptSubjectManageTreeType = {
  tmbIds: string[];
  treeList: Department[];
};

export type ClientSchoolDeptSubjectManageBindParams = {
  id: string;
  tmbIds: string;
};
