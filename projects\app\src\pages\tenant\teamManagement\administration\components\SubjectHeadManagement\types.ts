import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';

// 组件 Props 接口
export interface SubjectHeadManagementProps {
  onEditTeachers: (
    role: 'leader' | 'phase_leader' | 'teacher',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string
  ) => void;
  refreshList: () => void;
  semesterData: {
    year: string;
    type: 1 | 2;
  };
  highlightedTmbIds: number[];
  semesterId: string;
}

// 表格记录类型
export interface RecordType {
  className: string;
  isGrade: boolean;
  isDepartment?: boolean;
  isSchool?: boolean;
}

// 年级数据类型
export interface Grade {
  [subjectName: string]: Department;
}

// 表格行数据类型
export interface TableRowData {
  key: string;
  gradeName: string;
  isLeader?: boolean; // 是否为学段负责人行
  schoolData?: Department; // 学校数据，用于学段负责人行
}

// 负责人表格行数据类型
export interface LeadershipRowData {
  key: string;
  role: string;
}

// 年级科目管理数据结构
export interface SchoolData {
  // 按学部分组的数据
  primary: Department[];
  junior: Department[];
  senior: Department[];
  other: Department[];
}

// 悬停单元格状态
export interface HoveredCellState {
  id: string;
  users: TmbUser[];
}

// 弹窗状态
export interface ModalState {
  settingId: string;
  title: string;
  deptName: string;
  selectedUsers: { name: string; id: string; tmbId?: string }[];
}

// 节点数据信息
export interface NodeInfo {
  id: string;
  deptId: string;
  teachType: number;
  subjectId: number;
  subject?: string;
  isSubjectLeader: boolean;
  isPhaseLeader: boolean;
  grade: string;
  title: string;
}

// 顶级数据结构
export interface TopLevelData {
  topLevelSubjects: string[];
  topLevelLeaders: Record<string, TmbUser[]>;
}

// Next.js 页面组件默认导出
const TypesPage = () => {
  return null;
};
export default TypesPage;
