import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { Box, Text, VStack } from '@chakra-ui/react';
import { useHomeworkStore } from '@/store/useHomework';
import QuestionItem, {
  QuestionItemProps,
  QuestionOption
} from '@/pages/homeworkManagement/assignmentRelease/intelligentHomework/components/QuestionItem';
import HomeworkMaterials from './HomeworkMaterials';
import { useMemo } from 'react';
import type { ContentList, QuestionList, MaterialFileList } from '@/types/api/homeworkDetail';

/**
 * 智慧作业和自定义作业
 * 在作业类型为智慧作业和自定义作业时，调用此组件
 *
 */
function SmartAndCustom() {
  const { homeworkDetail } = useHomeworkStore();

  // 将API数据转换为QuestionItem组件所需的格式
  const transformQuestionData = (questionList: QuestionList[]): QuestionItemProps[] => {
    return questionList
      .map((item, index) => {
        const question = item.question;
        if (!question) return null;

        // 解析选项JSON字符串
        let options: QuestionOption[] = [];
        try {
          const parsedOptions = JSON.parse(question.options || '[]');
          options = parsedOptions.map((opt: any, idx: number) => ({
            label: String.fromCharCode(65 + idx), // A, B, C, D...
            content: typeof opt === 'string' ? opt : opt.content || '',
            image: opt.image || undefined
          }));
        } catch (error) {
          console.warn('解析题目选项失败:', error);
        }

        // 解析答案JSON字符串
        let correctAnswer = '';
        try {
          const parsedAnswer = JSON.parse(question.answer || '[]');
          if (Array.isArray(parsedAnswer) && parsedAnswer.length > 0) {
            correctAnswer = parsedAnswer[0];
          }
        } catch (error) {
          correctAnswer = question.answer || '';
        }

        return {
          id: item.id || String(question.id),
          questionNumber: item.questionOrder || index + 1,
          questionText: question.stem || '',
          questionImage: undefined, // 题目图片需要从stem中解析或单独字段
          options,
          correctAnswer,
          mainStem: question.mainStem || question.stem || question.originInfo || '题库',
          sourceTag: question.attributionToName || undefined,
          date: new Date(question.createTime || Date.now()).toLocaleDateString('zh-CN'),
          difficulty: question.difficultyName || '中等',
          knowledgePoint: question.knowledgePointNames?.join('、') || '',
          questionType: question.questionTypeName || '选择题',
          score: item.score || 0,
          tags: question.knowledgePointNames || [],
          homeworkType: 1 as const, // 智慧作业/自定义作业
          // 添加解析相关字段
          analysis: question.analysis || '', // 解析内容
          answer: question.answer || '', // 答案原始数据
          knowledgePointNames: question.knowledgePointNames || [] // 考点名称列表
        };
      })
      .filter(Boolean) as QuestionItemProps[];
  };

  // 获取所有作文辅助材料
  const materialFiles = useMemo(() => {
    if (!homeworkDetail?.contentList) return [];

    const materials: MaterialFileList[] = [];
    homeworkDetail.contentList.forEach((content: ContentList) => {
      if (content.materialFileList) {
        materials.push(...content.materialFileList);
      }
    });

    return materials;
  }, [homeworkDetail]);

  // 处理所有内容列表中的题目
  const allQuestions = useMemo(() => {
    if (!homeworkDetail?.contentList) return [];

    let questionNumber = 1;
    const questions: QuestionItemProps[] = [];

    homeworkDetail.contentList.forEach((content: ContentList) => {
      if (content.layoutList) {
        content.layoutList.forEach((layout) => {
          if (layout.questionList) {
            const transformedQuestions = transformQuestionData(layout.questionList);
            // 重新编号
            transformedQuestions.forEach((q) => {
              q.questionNumber = questionNumber++;
              questions.push(q);
            });
          }
        });
      }
    });

    return questions;
  }, [homeworkDetail]);

  // 计算题目统计信息
  const questionStats = useMemo(() => {
    const totalQuestions = allQuestions.length;
    const totalScore = allQuestions.reduce((sum, q) => sum + q.score, 0);
    return { totalQuestions, totalScore };
  }, [allQuestions]);

  return (
    <Box pt={vwDims(40)}>
      {/* 作业辅助材料 */}
      <HomeworkMaterials materialFiles={materialFiles} />

      {/* 作业题目 */}
      {materialFiles.length > 0 && <Box mt={vwDims(50)} />}
      <Box>
        <Text
          fontSize={vwDims(20)}
          fontWeight="500"
          color="#000"
          mb={vwDims(32)}
          display="flex"
          alignItems="center"
          fontFamily="PingFang SC"
          fontStyle="normal"
          lineHeight={vwDims(22)}
        >
          <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
          作业题目：{questionStats.totalQuestions}道题，合计{questionStats.totalScore}分
        </Text>

        <Box>
          {allQuestions.length > 0 ? (
            <VStack spacing={vwDims(16)} align="stretch">
              {allQuestions.map((question) => (
                <QuestionItem
                  key={question.id}
                  {...question}
                  homeworkType={4} // 智慧作业/自定义作业模式，显示完整功能
                />
              ))}
            </VStack>
          ) : (
            <Box textAlign="center" py={vwDims(40)} color="#86909C">
              <Text fontSize={vwDims(14)}>暂无题目数据</Text>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
}

export default SmartAndCustom;
