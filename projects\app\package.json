{"name": "app", "version": "1.0.0", "private": false, "scripts": {"dev": "next dev -p 8088", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build", "start": "next start", "lint": "next lint", "check": "npx tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.3.4", "@chakra-ui/anatomy": "^2.2.1", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@chakra-ui/styled-system": "^2.9.1", "@chakra-ui/system": "^2.6.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortaine/fetch-event-source": "^3.0.6", "@lexical/react": "0.12.6", "@lexical/selection": "^0.17.0", "@lexical/text": "0.12.6", "@lexical/utils": "0.12.6", "@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "^4.24.10", "@types/pdfjs-dist": "^2.10.378", "@types/react-beautiful-dnd": "^13.1.8", "ahooks": "3.7.11", "antd": "5.13.3", "axios": "^1.7.2", "browser-image-compression": "^2.0.2", "classnames": "^2.5.1", "click-to-react-component": "^1.1.2", "cron-parser": "^4.9.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "docx": "^9.0.3", "docxtemplater": "^3.52.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-resize-detector": "^1.2.4", "file-saver": "^2.0.5", "formidable": "^2.1.1", "framer-motion": "^9.1.7", "html-react-parser": "^5.2.5", "html-to-docx": "^1.8.0", "hyperdown": "^2.4.29", "i18next": "^22.5.1", "immer": "^9.0.19", "jschardet": "^3.0.0", "jsonwebtoken": "^9.0.2", "jsxgraph": "^1.10.1", "jszip": "^3.10.1", "katex": "^0.16.22", "lexical": "0.12.6", "lodash": "^4.17.21", "mammoth": "^1.8.0", "marked": "^15.0.0", "markmap-lib": "^0.17.0", "markmap-view": "^0.17.0", "mermaid": "^11.9.0", "mobile-detect": "^1.4.5", "nanoid": "^4.0.2", "next": "13.5.2", "next-i18next": "^13.3.0", "nprogress": "^0.2.0", "openai": "4.28.0", "papaparse": "^5.4.1", "pdfjs-dist": "^2.16.105", "pizzip": "^3.1.7", "qrcode": "^1.5.3", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.7.1", "react-dom": "18.2.0", "react-hook-form": "^7.43.1", "react-i18next": "^12.3.1", "react-infinite-scroll-component": "^6.1.0", "react-latex-next": "^3.0.0", "react-lottie": "^1.2.4", "react-markdown": "^8.0.7", "react-pdf": "^9.2.1", "react-stack-grid": "^0.7.1", "react-syntax-highlighter": "^15.5.0", "react-ueditor-wrap": "^1.0.8", "reactflow": "^11.7.4", "regression": "^2.0.1", "rehype-external-links": "^3.0.0", "rehype-katex": "^6.0.2", "rehype-raw": "^5.1.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sanitize-html": "^2.13.0", "sass": "^1.58.3", "simple-mind-map": "0.13.1-fix.2", "slick-carousel": "^1.8.1", "timezones-list": "^3.0.3", "update": "^0.7.4", "use-context-selector": "^2.0.0", "use-debounce": "^10.0.4", "use-immer": "^0.11.0", "xh-htmlword": "^1.2.1", "xml2js": "^0.6.2", "zustand": "^4.3.5"}, "devDependencies": {"@svgr/webpack": "^6.5.1", "@types/crypto-js": "^4.2.2", "@types/element-resize-detector": "^1.1.6", "@types/file-saver": "^2.0.7", "@types/formidable": "^2.0.5", "@types/html-docx-js": "^0.3.4", "@types/js-cookie": "^3.0.3", "@types/jsonwebtoken": "^9.0.3", "@types/jszip": "^3.4.1", "@types/lodash": "^4.14.191", "@types/node": "^20.8.5", "@types/nprogress": "^0.2.0", "@types/papaparse": "^5.3.14", "@types/qrcode": "^1.5.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-lottie": "^1.2.10", "@types/react-stack-grid": "^0.7.7", "@types/react-syntax-highlighter": "^15.5.6", "@types/request-ip": "^0.0.37", "@types/xml2js": "^0.4.14", "eruda": "^3.4.1", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "html-docx-js": "^0.3.1", "typescript": "5.5.3"}}