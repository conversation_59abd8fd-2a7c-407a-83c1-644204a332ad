import crypto from 'crypto';
import CryptoJS from 'crypto-js';
import { Toast } from './ui/toast';

/* check string is a web link */
export function strIsLink(str?: string) {
  if (!str) return false;
  if (/^((http|https)?:\/\/|www\.|\/)[^\s/$.?#].[^\s]*$/i.test(str)) return true;
  return false;
}

/* hash string */
export const hashStr = (str: string) => {
  return crypto.createHash('sha256').update(str).digest('hex');
};

export const replaceSensitiveLink = (text: string) => {
  const urlRegex = /(?<=https?:\/\/)[^\s]+/g;
  return text.replace(urlRegex, 'xxx');
};

export const getErrText = (err: any, def = '') => {
  const msg: string = typeof err === 'string' ? err : err?.message || def || '';
  return replaceSensitiveLink(msg);
};

export const maskPhoneNumber = (phone: string) => {
  const firstPart = phone && phone.slice(0, 3);
  const middlePart = '****';
  const lastPart = phone && phone.slice(7);

  return `${firstPart}${middlePart}${lastPart}`;
};

export const encodeBase64 = (str: string) => {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str));
};

export const decodeBase64 = (str: string) => {
  return CryptoJS.enc.Base64.parse(str).toString(CryptoJS.enc.Utf8);
};
