// types.ts
/**
 * ，维度其实用到id,indactorName,sort就行，
 *  项目的话用到的是id,indactorName,sort,projectType,subjectIds,scoreRate,
 */
import { PagingData, RequestPageParams } from '@/types';
import {
  EvaluatorType,
  EvaluateeType,
  MatchType,
  PeriodType,
  RuleStatus,
  Term,
  EvaluateType,
  HasSubIndicator,
  NeedSignature,
  UseLogo,
  ScoreInputType,
  ProjectType,
  EducationStage,
  IndicatorType,
  Status,
  EvaluatorTypeInRule,
  DimenTypeStatus,
  ScoreLevelStatus,
  BizType
} from '@/constants/api/tenant/evaluate/rule';
import { Dayjs } from 'dayjs';
import { DepartmentTree } from '../teamManagement/student';
import { Department } from '../teamManagement/teach';
import { FileInfo, FileType } from '../../cloud';

// 评价规则基本类型
export interface EvaluationRule {
  createTime?: string;
  endTime?: string;
  evaluateeIds?: string[];
  evaluateeNum?: number;
  evaluateeType?: EvaluateeType;
  evaluatorIds?: string[];
  evaluatorNum?: number;
  evaluatorType?: EvaluatorType;
  id?: string;
  indacatorIds?: string[];
  isDeleted?: number;
  matchType?: MatchType;
  periodType?: PeriodType;
  ruleName: string;
  ruleStatus?: RuleStatus;
  semesterId?: string;
  startTime?: string;
  tenantId?: string;
  term?: Term;
  updateTime?: string;
  year?: string;
  evaluateeIdsCoustom?: string[];
  copyId: string; //前端使用
}

// 新增评价规则参数
export type AddEvaluationRuleParams = EvaluationRule;

// 删除评价规则参数
export interface DeleteEvaluationRuleParams {
  id: string;
}
export interface CopyEvaluationRuleParams {
  id: string;
}

// 评价规则详情参数
export type EvaluationRuleDetailParams = DeleteEvaluationRuleParams;

// 评价指标类型
export interface Indicator {
  children?: Indicator[];
  createTime?: string;
  dimenTypeId?: string;
  evaluateType?: EvaluateType;
  hasChildren?: boolean;
  id: string;
  indactorName?: string;
  isDeleted?: number;
  isHasSub?: HasSubIndicator;
  isNeedSign?: NeedSignature;
  isUseLogo?: UseLogo;
  parentId?: string;
  projectType?: ProjectType;
  score?: number;
  scoreInputType?: ScoreInputType;
  scoreLevel?: number;
  scoreLevelId?: string;
  scoreMax?: number;
  scoreMin?: number;
  scoreRate?: number;
  sortNo?: number;
  stage?: EducationStage;
  status?: Status;
  tenantId?: string;
  type?: IndicatorType;
  updateTime?: string;
  iconFileKey?: string;
  iconFile?: FileInfo;
}

// 修改评价规则详情接口
export interface EvaluationRuleDetail extends EvaluationRule {
  evaluatees?: EvaluaRuleEvaluatee[];
  evaluators?: EvaluaRuleEvaluator[];
  indacators?: EvaluaIndactorVO[];
  indactorNum?: number;
}

// 获取教师列表参数
export interface GetTeachersParams {
  deptId?: string;
  deptIds?: number[];
  searchKey?: string;

  tmbId?: string;
}

// 教师信息
export interface TeacherType {
  accessKey?: string;
  avatar?: string;
  avatarFile?: any; // 可以根据实际情况定义具体类型
  createTime?: string;
  deptId?: string;
  deptName?: string;
  dingUserId?: string;
  email?: string;
  id: string;
  isDefault?: number;
  isDeleted?: number;
  phone?: string;
  qywxUserId?: string;
  roleId?: string;
  roleName?: string;
  roleType?: number;
  status?: Status;
  tenantId?: string;
  updateTime?: string;
  userId?: string;
  username: string;
}

// 评价规则列表请求参数
export interface EvaluationRuleListParams extends RequestPageParams {
  ascs?: string;
  descs?: string;
  searchKey?: string;
  tenantId?: string;
}

// 评价规则被评价人
export interface EvaluaRuleEvaluatee {
  clazzId?: string;
  createTime?: string;
  id?: string;
  isDeleted?: number;
  ruleId?: string;
  tenantId?: string;
  updateTime?: string;
}

// 评价规则评价人
export interface EvaluaRuleEvaluator {
  createTime?: string;
  evaluatorId?: string;
  evaluatorType?: EvaluatorTypeInRule;
  id?: string;
  isDeleted?: number;
  ruleId?: string;
  tenantId?: string;
  updateTime?: string;
}

// src/types/api/tenant/evaluate/dimenType.ts

export interface DimenType {
  id?: string;
  name: string;
  status?: DimenTypeStatus;
  tenantId?: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
}

export interface AddDimenTypeParams extends Omit<DimenType, 'id'> {}

export interface DeleteDimenTypeParams {
  id: string;
}

export interface ListDimenTypeParams {
  id?: string;
}

export type UpdateDimenTypeParams = DimenType;

export interface EvaluaDimension {
  id?: string;
  indactorName: string;
  sortNo?: number;
  dimenTypeId?: string;
}

// 评价项目
export interface EvaluaProject {
  id?: string;
  indactorName: string;
  indactorNum?: number;
  sortNo?: number;
  isHasSub?: HasSubIndicator;
  parentId?: string;
  projectType?: ProjectType;
  subjectIds?: string[];
  scoreRate?: number;
  subjects: SubjectType[];
  clazzId?: string;
}

export interface SubjectType {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  indactorId: string;
  subjectId: string;
  subjectName: string;
}

// 评价指标
export interface EvaluaIndactor {
  id?: string;
  indactorName?: string;
  sortNo?: number;
  tenantId?: string;
  type: IndicatorType.EvaluationIndicator;
  createTime?: string;
  dimenTypeId?: string;
  evaluateType?: EvaluateType;
  isDeleted?: number;
  isHasSub?: HasSubIndicator;
  isNeedSign?: NeedSignature;
  isUseLogo?: UseLogo;
  parentId?: string;
  projectType?: ProjectType;
  score?: number;
  scoreInputType?: ScoreInputType;
  scoreLevel?: string;
  scoreLevelId?: string;
  scoreMax?: number;
  scoreMin?: number;
  scoreRate?: number;
  stage?: EducationStage[] | string;
  status?: Status;
  subjectIds?: number[];
  updateTime?: string;
  iconFileKey?: string;
  iconFile?: iconFileParams;
}

type iconFileParams = {
  fileUrl: string;
};

export interface EvaluaIndicatorQueryReq {
  ascs?: string;
  current?: number;
  descs?: string;
  dimenTypeId?: string;
  searchKey?: string;
  size?: number;
  parentId?: string;
  type?: IndicatorType;
}

export interface EvaluaIndactorVO extends EvaluaIndactor {
  children?: EvaluaIndactorVO[];
  hasChildren?: boolean;
  parent?: EvaluaIndactorVO;
}

export interface EvaluaIndactorSortParams {
  id: string;
  sortNo: number;
}

// 评价等级相关接口
export interface EvaluaScoreLevel {
  id?: string;
  name: string;
  status?: ScoreLevelStatus;
  tenantId?: number;
  createTime?: string;
  updateTime?: string;
  bindCount?: number;
  isDeleted?: number;
  details?: EvaluaScoreLevelValue[];
}

// types.ts

export interface EvaluaScoreLevelValue {
  id?: string;
  name: string;
  scoreLevelId: string;
  scoreMax?: number;
  scoreMin?: number;
  sortNo?: number;
  status: number;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  tenantId?: number;
  levelName?: string;
  levelStatus?: string;
}

export interface EvaluaScoreLevelValueCreateParams {
  name: string;
  scoreLevelId: string;
  scoreMax?: number;
  scoreMin?: number;
  status: number;
  sortNo?: number;
}

export interface EvaluaScoreLevelValueUpdateParams {
  id?: string;
  name: string;
  scoreLevelId: string;
  scoreMax?: number;
  scoreMin?: number;
  status: number;
  sortNo?: number;
}

export type BatchUpdateParams = {
  createReqs: EvaluaScoreLevelValueCreateParams[];
  updateReqs: EvaluaScoreLevelValueUpdateParams[];
};

export interface TenantEvaluateIconAddParams {
  fileKey: string;
}

export interface IconListType {
  fileKey: string;
  id: string;
  file: iconFileParams;
}

export interface EvaluaIndactorParams {
  id: string;
  status: number;
}

export interface GetIndactorTreeByProjectParams {
  evaluateType?: number;
  gradeIds?: string[];
  id?: string;
  parentId?: string;
  parentIds?: string[];
  searchKey?: string;
  stage?: number;
  stages?: number[];
  subjectId?: string;
  status?: Status;
  tenantId?: string;
  type?: number;
}

export interface EvaluaIndactorDetailParams {
  id: string;
}

export interface HomeworkEvaluateDeleteParams {
  id: string;
}

export interface HomeworkClassEvaluateDeleteParams {
  id: string;
}

export interface EvaluaViewListNewParams extends RequestPageParams {
  clazzId?: string;
  evaluateeId?: string;
  searchKey?: string;
  evaluatorId?: string;
  term?: number;
  year?: string;
  projectId?: string;
}

export type Indactor = {
  id: string;
  indactorName: string;
  parent?: Indactor;
};
export interface EvaluaViewListNewList {
  clazzId: string;
  evaluateType: EvaluateType;
  evaluatorId: string;
  gradeId: string;
  id: string;
  indactorId: string;
  indactorName: string;
  remark: string;
  score: number;
  scoreLevelId: string;
  scoreLevelValue: string;
  scoreLevelValueId: string;
  studentId: string;
  studentName: string;
  updateTime: string;
  indactor: Indactor;
  evaluatorName: string;
  scoreLevel: string;
  bizType: BizType;
  parentId: string;
  subjectName: string;
  children?: EvaluaIndactorVO[];
}

export type DeptTeacherType = Department & {
  teachers: TeacherType[];
  children: DeptTeacherType[];
};
