import React, { memo, useCallback, useState, useRef } from 'react';
import { <PERSON>, Flex, Button as <PERSON><PERSON><PERSON><PERSON>on, Image } from '@chakra-ui/react';
import type { TableProps } from 'antd';

import PageContainer from '@/components/PageContainer';
import {
  deleteScene,
  deleteSubScene,
  getSceneList,
  getSubSceneList,
  sortScene,
  sortSubScene,
  sortApp,
  getAppLabelPage
} from '@/api/tenant/scene';
import { message, But<PERSON>, Card } from 'antd';
import { respDims } from '@/utils/chakra';
import SceneAdd, { SceneAddRef } from './components/SceneAdd';
import ImportAppScene, { ImportAppSceneRef } from './components/ImportAppScene';
import SubSceneAdd, { SubSceneAddRef } from './components/SubSceneAdd';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import MyTable from '@/components/MyTable';
import { arrayMove } from '@dnd-kit/sortable';
import { SceneSortParams, SubSceneSortParams } from '@/types/api/tenant/scene';
import { MyTableRef } from '@/components/MyTable/types';
import { getMyAppPage, removeApp } from '@/api/tenant/app';
import { serviceSideProps } from '@/utils/i18n';
import { useRouter } from 'next/router';
import { useLoading } from '@/hooks/useLoading';
import { AddIcon } from '@chakra-ui/icons';
import { getListFromPage } from '@/utils/api';

const Scene = ({ tenantId }: { tenantId: string }) => {
  const router = useRouter();
  const { industry } = router.query;
  const [selectedScene, setSelectedScene] = useState('');
  const [selectedSubScene, setSelectedSubScene] = useState('');
  const sceneAddRef = useRef<SceneAddRef>(null);
  const importAppSceneRef = useRef<ImportAppSceneRef>(null);
  const subSceneAddRef = useRef<SubSceneAddRef>(null);

  const sceneTableRef = useRef<MyTableRef>(null);
  const subSceneTableRef = useRef<MyTableRef>(null);
  const appTableRef = useRef<MyTableRef>(null);
  const { Loading } = useLoading();
  const [loading, setLoading] = useState(false);
  const handleDragSortEnd = useCallback(async (event: any, newDataSource: any[]) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      let params: SceneSortParams = {
        param: newDataSource.map((item: any, index) => {
          return {
            id: item.id,
            sort: index
          };
        })
      };
      try {
        setLoading(true);
        await sortScene(params);
        message.success('排序更新成功');
        sceneTableRef.current?.reload();
      } catch (error) {
        message.error('排序更新失败');
      }
      setLoading(false);
    }
  }, []);

  const handleSubDragSortEnd = useCallback(async (event: any, newDataSource: any[]) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      let params: SubSceneSortParams = {
        param: newDataSource.map((item: any, index) => {
          return {
            id: item.id,
            sort: index
          };
        })
      };
      try {
        setLoading(true);
        await sortSubScene(params);
        message.success('排序更新成功');
        subSceneTableRef.current?.reload();
      } catch (error) {
        message.error('排序更新失败');
      }
      setLoading(false);
    }
  }, []);

  const fetchSubSceneList = (params: any) =>
    params.tenantSceneId ? getSubSceneList(params) : Promise.resolve([]);

  const fetchAppList = (params: any) =>
    params.tenantLabelId ? getListFromPage(getAppLabelPage, params) : Promise.resolve([]);

  const onAddScene = (type: 'edit' | 'add', record = null) => {
    if (sceneAddRef.current) {
      sceneAddRef.current.openModal({
        formStatus: type,
        records: record as any
      });
    }
  };

  const onAddSubScene = (type: 'edit' | 'add', record = null) => {
    if (subSceneAddRef.current) {
      subSceneAddRef.current.openModal({
        formStatus: type,
        records: record as any
      });
    }
  };

  const onImportAppScene = () => {
    if (importAppSceneRef.current) {
      importAppSceneRef.current.openModal({
        formStatus: 'add'
      });
    }
  };

  const onDeleteScene = (record: any) => {
    MessageBox.confirm({
      title: '删除场景',
      content: '是否删除该场景？',
      onOk: async () => {
        // 在这里添加删除场景的逻辑
        await deleteScene({
          id: record.id
        });
        Toast.success('场景删除成功');
        // 重新加载场景列表
        if (record.id === selectedScene) {
          setSelectedScene('');
        }
        sceneTableRef.current?.reload();
      }
    });
  };

  const onDeleteSubScene = async (record: any) => {
    MessageBox.confirm({
      title: '删除子场景',
      content: '是否删除该子场景？',
      onOk: async () => {
        // 在这里添加删除子场景的逻辑
        await deleteSubScene({
          id: record.id
        });
        Toast.success('子场景删除成功');
        // 重新加载子场景列表
        subSceneTableRef.current?.reload();
      }
    });
  };

  const onDeleteApp = (record: any) => {
    MessageBox.confirm({
      title: '移除应用',
      content: '是否移除该应用？',
      onOk: async () => {
        // 在这里添加移除应用的逻辑，例如调用API移除记录
        await removeApp({
          tenantLabelId: selectedSubScene,
          tenantAppId: record.id,
          tenantSceneId: selectedScene
        });
        Toast.success('应用移除成功');
        // 重新加载应用列表
        appTableRef.current?.reload();
      }
    });
  };

  const columnsScene: TableProps<any>['columns'] = [
    {
      title: '姓名',
      dataIndex: 'name',
      className: 'drag-visible'
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: (text: any, record: any) => (
        <Flex alignItems="center" justifyContent="flex-end">
          <Box
            color="#0052D9"
            fontSize={respDims(14, 12)}
            cursor="pointer"
            mr={respDims(8, 4)}
            onClick={(event) => {
              event.stopPropagation();
              onAddScene('edit', record);
            }}
          >
            编辑
          </Box>
          <Box
            color="#F6685D"
            fontSize={respDims(14, 12)}
            cursor="pointer"
            mr={respDims(8, 4)}
            onClick={(event) => {
              event.stopPropagation();
              onDeleteScene(record);
            }}
          >
            删除
          </Box>
        </Flex>
      )
    }
  ];

  const columnsSubScene: TableProps<any>['columns'] = [
    {
      title: '姓名',
      dataIndex: 'name',
      className: 'drag-visible'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (text: any, record: any) => (
        <Flex alignItems="center" justifyContent="flex-end">
          <Box
            color="#0052D9"
            fontSize={respDims(14, 12)}
            cursor="pointer"
            mr={respDims(8, 4)}
            onClick={(event) => {
              event.stopPropagation();
              onAddSubScene('edit', record);
            }}
          >
            编辑
          </Box>
          <Box
            color="#F6685D"
            fontSize={respDims(14, 12)}
            cursor="pointer"
            mr={respDims(8, 4)}
            onClick={(event) => {
              event.stopPropagation();
              onDeleteSubScene(record);
            }}
          >
            删除
          </Box>
        </Flex>
      )
    }
  ];

  const handleAppDragSortEnd = useCallback(async (event: any, newDataSource: any[]) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      let params = {
        param: newDataSource.map((item: any, index) => {
          return {
            id: item.tenantAppLabelId,
            sort: index
          };
        })
      };
      try {
        setLoading(true);
        // 假设有一个 sortApp 方法来处理应用的排序
        await sortApp(params);
        message.success('排序更新成功');
        appTableRef.current?.reload();
      } catch (error) {
        message.error('排序更新失败');
      }
      setLoading(false);
    }
  }, []);

  const columnsApp: TableProps<any>['columns'] = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      className: 'drag-visible',
      render: (value) => <Box ml={respDims(10)}> {value} </Box>
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (text: any, record: any) => (
        <Flex alignItems="center" justifyContent="flex-end">
          <Box
            color="#F6685D"
            fontSize={respDims(14, 12)}
            cursor="pointer"
            mr={respDims(8, 4)}
            onClick={(event) => {
              event.stopPropagation();
              onDeleteApp(record);
            }}
          >
            移除
          </Box>
        </Flex>
      )
    }
  ];

  return (
    <PageContainer>
      <Loading loading={loading}></Loading>
      <Flex padding={respDims(24)} h="100%" w="100%">
        <Flex justifyContent="space-around" h="100%" w="100%">
          <Flex flexDirection="column" h="100%" w="32%">
            <Box mb={respDims(10)} fontSize={respDims(18, 14)} fontWeight="600" color="#1D2129">
              场景
            </Box>
            <Flex
              bg="#f7f9fb"
              borderRadius="8px"
              flex="1"
              h="0"
              p={respDims(16)}
              flexDirection="column"
            >
              <Box
                flex="1"
                overflow="auto"
                css={{
                  '& .selected-row': {
                    background: '#eff5fe!important'
                  }
                }}
              >
                <MyTable
                  ref={sceneTableRef}
                  columns={columnsScene}
                  boxStyle={{
                    px: '0',
                    py: '0',
                    borderRadius: '12px',
                    css: {
                      '& .ant-table': {
                        borderRadius: '12px 12px 12px 12px!important',
                        overflow: 'hidden!important'
                      },
                      '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                        borderBottom: 'none'
                      }
                    }
                  }}
                  size="small"
                  headerConfig={{
                    showHeader: false
                  }}
                  showHeader={false}
                  pageConfig={{
                    showPaginate: false
                  }}
                  emptyConfig={{
                    EmptyPicComponent: () => (
                      <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
                    )
                  }}
                  api={getSceneList}
                  dragConfig={{
                    enabled: true,
                    rowKey: 'id',
                    onDragEnd: handleDragSortEnd
                  }}
                  rowClassName={(record: any) =>
                    record.id === selectedScene ? 'selected-row' : ''
                  }
                  onRow={(record: any) => ({
                    onClick: () => {
                      setSelectedScene(record.id);
                      setSelectedSubScene(''); // Reset sub-scene selection
                    }
                  })}
                />
              </Box>
              <Flex justifyContent="flex-end">
                <Box w="100%" pt={respDims(20)}>
                  <ChakraButton
                    bg="#fff"
                    color="#4E5969"
                    w="100%"
                    leftIcon={<AddIcon />}
                    onClick={() => onAddScene('add')}
                  >
                    新增场景
                  </ChakraButton>
                </Box>
              </Flex>
            </Flex>
          </Flex>
          <Flex flexDirection="column" h="100%" w="32%">
            <Box mb={respDims(10)} fontSize={respDims(18, 14)} fontWeight="600" color="#1D2129">
              子场景
            </Box>
            <Flex
              bg="#f7f9fb"
              borderRadius="8px"
              flex="1"
              h="0"
              p={respDims(16)}
              flexDirection="column"
            >
              <Box
                flex="1"
                overflow="auto"
                css={{
                  '& .selected-row': {
                    background: '#eff5fe!important'
                  }
                }}
              >
                <MyTable
                  ref={subSceneTableRef}
                  columns={columnsSubScene}
                  api={fetchSubSceneList}
                  defaultQuery={{ tenantSceneId: selectedScene }}
                  boxStyle={{
                    px: '0',
                    py: '0'
                  }}
                  size="small"
                  showHeader={false}
                  headerConfig={{
                    showHeader: false
                  }}
                  pageConfig={{
                    showPaginate: false
                  }}
                  emptyConfig={{
                    EmptyPicComponent: () => (
                      <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
                    ),
                    emptyText: (!selectedScene && '请选中一个场景') || ''
                  }}
                  dragConfig={{
                    enabled: true,
                    rowKey: 'id',
                    onDragEnd: handleSubDragSortEnd
                  }}
                  rowClassName={(record) => (record.id === selectedSubScene ? 'selected-row' : '')}
                  onRow={(record) => ({
                    onClick: () => {
                      setSelectedSubScene(record.id);
                    }
                  })}
                />
              </Box>
              <Flex justifyContent="flex-end">
                <Box pt={respDims(20)} w="100%">
                  {selectedScene && (
                    <ChakraButton
                      bg="#fff"
                      color="#4E5969"
                      w="100%"
                      leftIcon={<AddIcon />}
                      onClick={() => onAddSubScene('add')}
                    >
                      新增子场景
                    </ChakraButton>
                  )}
                </Box>
              </Flex>
            </Flex>
          </Flex>
          <Flex flexDirection="column" h="100%" w="32%">
            <Box mb={respDims(10)} fontSize={respDims(18, 14)} fontWeight="600" color="#1D2129">
              应用
            </Box>
            <Flex
              bg="#f7f9fb"
              borderRadius="8px"
              flex="1"
              h="0"
              p={respDims(16)}
              flexDirection="column"
            >
              <Box flex="1" overflow="auto">
                <MyTable
                  columns={columnsApp}
                  boxStyle={{
                    px: '0',
                    py: '0'
                  }}
                  size="small"
                  showHeader={false}
                  headerConfig={{
                    showHeader: false
                  }}
                  pageConfig={{
                    showPaginate: false
                  }}
                  emptyConfig={{
                    EmptyPicComponent: () => (
                      <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
                    ),
                    emptyText: (!selectedSubScene && '请选中一个子场景') || ''
                  }}
                  api={fetchAppList}
                  defaultQuery={{ tenantLabelId: selectedSubScene }}
                  ref={appTableRef}
                  dragConfig={{
                    enabled: true,
                    rowKey: 'id',
                    onDragEnd: handleAppDragSortEnd
                  }}
                />
              </Box>
              <Flex justifyContent="flex-end">
                <Box pt={respDims(20)} w="100%">
                  {selectedSubScene && (
                    <ChakraButton
                      bg="#fff"
                      color="#4E5969"
                      w="100%"
                      leftIcon={<AddIcon />}
                      onClick={() => onImportAppScene()}
                    >
                      导入应用
                    </ChakraButton>
                  )}
                </Box>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
      <SceneAdd ref={sceneAddRef} onSuccess={() => sceneTableRef.current?.reload()} />
      <SubSceneAdd
        ref={subSceneAddRef}
        sceneId={selectedScene}
        onSuccess={() => subSceneTableRef.current?.reload()}
      />
      <ImportAppScene
        ref={importAppSceneRef}
        selectedSubScene={selectedSubScene}
        selectedScene={selectedScene}
        onSuccess={() => appTableRef.current?.reload()}
      />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(Scene);
