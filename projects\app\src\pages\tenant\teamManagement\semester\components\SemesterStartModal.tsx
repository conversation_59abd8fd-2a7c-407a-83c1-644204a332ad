import React, { useState, useEffect } from 'react';
import { Box, Flex, Text, Divider, Button } from '@chakra-ui/react';
import { Radio, Checkbox, Space } from 'antd';
import MyModal from '@/components/MyModal';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

interface SemesterStartModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (config: SemesterStartConfig) => void;
  isLoading?: boolean;
  isSameYear: boolean; // 是否与当前进行中的学期属于同一学年
  semesterId: string; // 学期ID
}

export interface SemesterStartConfig {
  copySubject: boolean; // 复制学科
  administrativeWorkType: 'copy' | 'import'; // 行政工作分配
  subjectResponsibleType: 'copy' | 'import'; // 学科负责工作分配
  teachingAssignmentType: 'copy' | 'import'; // 任课工作分配
}

const SemesterStartModal: React.FC<SemesterStartModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
  isSameYear,
  semesterId
}) => {
  // 初始化配置状态
  const [config, setConfig] = useState<SemesterStartConfig>({
    copySubject: false,
    administrativeWorkType: 'copy',
    subjectResponsibleType: 'copy',
    teachingAssignmentType: 'copy'
  });

  // 重置配置项
  useEffect(() => {
    if (isOpen) {
      setConfig({
        copySubject: false,
        administrativeWorkType: 'copy',
        subjectResponsibleType: 'copy',
        teachingAssignmentType: 'copy'
      });
    }
  }, [isOpen]);

  // 处理学科复选框变化
  const handleSubjectChange = (e: CheckboxChangeEvent) => {
    setConfig({ ...config, copySubject: e.target.checked });
  };

  // 处理行政工作分配单选框变化
  const handleAdministrativeChange = (e: any) => {
    setConfig({ ...config, administrativeWorkType: e.target.value });
  };

  // 处理学科负责工作分配单选框变化
  const handleSubjectResponsibleChange = (e: any) => {
    setConfig({ ...config, subjectResponsibleType: e.target.value });
  };

  // 处理任课工作分配单选框变化
  const handleTeachingAssignmentChange = (e: any) => {
    setConfig({ ...config, teachingAssignmentType: e.target.value });
  };

  // 确认开始学期 - 只收集配置信息，不调用API
  const handleConfirm = () => {
    // 直接将配置传递给父组件，由父组件处理API调用
    onConfirm(config);
    onClose();
  };

  return (
    <MyModal isOpen={isOpen} title="开始新学期" onClose={onClose} isCentered>
      <Box p="24px">
        <Text mb="24px" color="rgba(0, 0, 0, 0.60)">
          开始新学期后，将以下方设置为基础初始化新学期数据。
        </Text>

        {/* 学科配置 */}
        <Flex alignItems="center" height="30px">
          <Box>学科配置：</Box>
          <Checkbox checked={config.copySubject} onChange={handleSubjectChange}>
            复制自上一学期
          </Checkbox>
        </Flex>

        <Divider my="10px" />

        {/* 行政工作分配 */}
        <Flex alignItems="center" height="30px" mb="10px">
          <Box>行政工作分配：</Box>
          <Space direction="horizontal">
            <Radio
              value="copy"
              checked={config.administrativeWorkType === 'copy'}
              onChange={handleAdministrativeChange}
            >
              {isSameYear ? '复制自上一学期' : '跟随学生年级变化'}
            </Radio>
            <Radio
              value="import"
              checked={config.administrativeWorkType === 'import'}
              onChange={handleAdministrativeChange}
            >
              稍后自行导入
            </Radio>
          </Space>
        </Flex>

        {/* 学科负责工作分配 */}
        <Flex alignItems="center" height="30px" mb="10px">
          <Box>学科负责工作分配：</Box>
          <Space direction="horizontal">
            <Radio
              value="copy"
              checked={config.subjectResponsibleType === 'copy'}
              onChange={handleSubjectResponsibleChange}
            >
              {isSameYear ? '复制自上一学期' : '跟随学生年级变化'}
            </Radio>
            <Radio
              value="import"
              checked={config.subjectResponsibleType === 'import'}
              onChange={handleSubjectResponsibleChange}
            >
              稍后自行导入
            </Radio>
          </Space>
        </Flex>

        {/* 教师任课工作分配 */}
        <Flex alignItems="center" height="30px" mb="10px">
          <Box>教师任课工作分配：</Box>
          <Space direction="horizontal">
            <Radio
              value="copy"
              checked={config.teachingAssignmentType === 'copy'}
              onChange={handleTeachingAssignmentChange}
            >
              {isSameYear ? '复制自上一学期' : '跟随学生年级变化'}
            </Radio>
            <Radio
              value="import"
              checked={config.teachingAssignmentType === 'import'}
              onChange={handleTeachingAssignmentChange}
            >
              稍后自行导入
            </Radio>
          </Space>
        </Flex>

        {/* 按钮区域 */}
        <Flex justifyContent="flex-end" mt="24px">
          <Button variant="outline" mr="16px" onClick={onClose}>
            取消
          </Button>
          <Button colorScheme="blue" isLoading={isLoading} onClick={handleConfirm}>
            确认开始
          </Button>
        </Flex>
      </Box>
    </MyModal>
  );
};

export default SemesterStartModal;
