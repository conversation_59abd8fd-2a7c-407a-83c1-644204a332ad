import { ICompositionFiles } from '@/types/api/ai/correction/task';

export interface AppNewCorrectionProps {
  studentName?: string;
  index?: number;
  uploadType?: 'replace' | 'create';
  studentListIndex: number;
  parseResult?: string;
  parseStatus?: 'parsing' | 'in-progress' | 'completed';
}

export interface BatchList {
  url: string;
  type: 'img' | 'pdf';
  name: string;
  fileName: string;
  fileKey: string;
  source?: number; // 1: 本地上传, 2: 数据空间
}

export interface BatchUploadFile {
  url: string;
  type: 'img' | 'pdf';
  fileName: string;
  fileKey: string;
  source?: number; // 1: 本地上传, 2: 数据空间
}

export interface studentListType {
  id?: number;
  studentId?: string;
  title: string;
  content?: any;
  type?: 'img' | 'pdf' | 'text';
  parseResult?: string;
  parseStatus?: 'parsing' | 'in-progress' | 'completed'; // ‘解析中’ | ‘正在进行’ | ‘已完成’；
  compositionFiles?: ICompositionFiles[];
  contents?: any;
}

export interface FileItem {
  files: ICompositionFiles[];
  text: string;
  type: 'img' | 'pdf';
  id?: number;
  contents?: any;
}
