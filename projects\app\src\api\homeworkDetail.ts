import { POST } from '@/utils/request';
import type { AxiosResponse } from 'axios';
import type {
  HomeworkDetailResponse,
  HomeworkTypeDetailResponse,
  HomeworkSubmissionStatsResponse,
  HomeworkSubmissionCompletionStatsResponse,
  HomeworkSubmissionClassComparisonResponse,
  HomeworkGradingStatsResponse,
  HomeworkGradingAnswerStatsResponse,
  HomeworkGradingClassComparisonResponse,
  StudentSubmissionPageRequest,
  StudentSubmissionPageResponse,
  LearningAnalysisRequest,
  LearningAnalysisResponse,
  QuestionCompletionStatsResponse,
  ExcellentStudentResponse,
  ErrorDistributionResponse,
  LevelAvgScoreResponse,
  StudentSubmitRecordRequest,
  StudentSubmitRecordResponse,
  IndividualAnalysisRequest,
  IndividualAnalysisResponse,
  StudentErrorReviseStatsRequest,
  StudentErrorReviseStatsResponse,
  StudentErrorWrongStatsRequest,
  StudentErrorWrongStatsResponse,
  StudentErrorPageRequest,
  StudentErrorPageResponse,
  BatchOperationRequest,
  StudentErrorQuestionStatsRequest,
  StudentErrorQuestionStatsResponse,
  StudentReviseSubmitListRequest,
  StudentReviseSubmitRecord,
  WordCloudRequest,
  WordCloudResponse,
  RejectRevisionRequest
} from '@/types/api/homeworkDetail';
import { PagingData } from '@/types';

// 更新为实际的API服务地址
export const BASE_URL = '/ai-homework';

/**
 * 获取作业详情
 * @param id 作业ID TODO: id=24 测试数据
 * @returns 作业详情数据
 */
export const getHomeworkDetail = (id: number) => {
  return POST<HomeworkDetailResponse>(
    `/teacher/homework/detail`,
    { id },
    {
      baseURL: BASE_URL
    }
  );
};

/**
 * 获取作业类型详情
 * @param id 作业类型ID
 * @returns 作业类型详情
 */
export const getHomeTypeDetail = (id: number) => {
  return POST<HomeworkTypeDetailResponse>(
    `/teacher/homework-type/detail`,
    { id },
    { baseURL: BASE_URL }
  );
};

/**
 * 获取作业提交概况-提交情况
 */
export function getHomeworkSubmissionStats({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId: number;
}) {
  return POST<HomeworkSubmissionStatsResponse>(
    `/teacher/homework/submission-overview/submission-stats`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 获取作业提交概况-完成情况
 * @param homeworkId 作业ID
 * @param clazzId 班级ID
 * @returns 完成情况统计数据
 */
export function getHomeworkSubmissionCompletionStats({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId: number;
}) {
  return POST<HomeworkSubmissionCompletionStatsResponse>(
    `/teacher/homework/submission-overview/completion-stats`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 获取作业提交概况-班级对比
 * @param homeworkId 作业ID
 * @param clazzId 班级ID (可选)
 * @returns 班级对比数据
 */
export function getHomeworkSubmissionClassComparison({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId?: number;
}) {
  return POST<HomeworkSubmissionClassComparisonResponse[]>(
    `/teacher/homework/submission-overview/class-comparison`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 获取作业批改概况-批改进度
 * @param homeworkId 作业ID
 * @param clazzId 班级ID
 * @returns 批改进度统计数据
 */
export function getHomeworkGradingStats({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId: number;
}) {
  return POST<HomeworkGradingStatsResponse>(
    `/teacher/homework/grading-overview/grading-stats`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 获取作业批改概况-答题情况
 * @param homeworkId 作业ID
 * @param clazzId 班级ID
 * @returns 答题情况统计数据
 */
export function getHomeworkGradingAnswerStats({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId: number;
}) {
  return POST<HomeworkGradingAnswerStatsResponse>(
    `/teacher/homework/grading-overview/answer-stats`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 获取作业批改概况-班级对比
 * @param homeworkId 作业ID
 * @param clazzId 班级ID
 * @returns 班级对比数据
 */
export function getHomeworkGradingClassComparison({
  homeworkId,
  clazzId
}: {
  homeworkId: number;
  clazzId?: number;
}) {
  return POST<HomeworkGradingClassComparisonResponse>(
    `/teacher/homework/grading-overview/class-comparison`,
    { homeworkId, clazzId },
    { baseURL: BASE_URL }
  );
}

/**
 * 分页查询学生提交与批改列表
 * @param params 查询参数
 * @returns 学生提交与批改列表分页数据
 */
export function getStudentSubmissionPage(params: Partial<StudentSubmissionPageRequest>) {
  return POST<StudentSubmissionPageResponse>(`/teacher/homework/student-submission/page`, params, {
    baseURL: BASE_URL
  });
}

export function exportCorrectionResults({
  studentHomeworkIds,
  noToast = false
}: {
  studentHomeworkIds: number[];
  noToast?: boolean;
}): Promise<AxiosResponse> {
  return POST<AxiosResponse>(
    `/teacher/homework/exportCorrectionResults`,
    { studentHomeworkIds },
    {
      baseURL: BASE_URL,
      responseType: 'blob',
      noToast
    }
  );
}

/**
 * 批量确认
 * @param submitIds 学生作业提交ID列表
 * @returns 确认结果
 */
export function batchConfirm({ submitIds }: { submitIds: number[] }) {
  return POST<boolean>(
    `/teacher/homework/student-submission/batch-confirm`,
    { submitIds },
    {
      baseURL: BASE_URL
    }
  );
}

// ==================== 学情分析相关接口 ====================

/**
 * 学情分析-作业完成情况-提交情况/提交时间/完成时长
 * @param params 查询参数
 * @returns 提交情况统计数据
 */
export function getLearningAnalysisSubmitStats(params: LearningAnalysisRequest) {
  return POST<LearningAnalysisResponse>(
    `/teacher/homework/student-submission/submit-stats`,
    params,
    {
      baseURL: BASE_URL
    }
  );
}

/**
 * 学情分析-作业完成情况-题目平均完成率/错题占比
 * @param params 查询参数
 * @returns 题目完成率统计数据
 */
export function getLearningAnalysisQuestionCompletionStats(params: LearningAnalysisRequest) {
  return POST<QuestionCompletionStatsResponse>(
    `/teacher/homework/student-submission/question-completion-stats`,
    params,
    { baseURL: BASE_URL }
  );
}

/**
 * 学情分析-作业质量分析-前10优秀作业
 * @param params 查询参数
 * @returns 优秀学生作业数据
 */
export function getLearningAnalysisExcellentStudent(params: LearningAnalysisRequest) {
  return POST<ExcellentStudentResponse>(
    `/teacher/homework/student-submission/excellent-student`,
    params,
    { baseURL: BASE_URL }
  );
}

/**
 * 学情分析-作业质量分析-知识点/题型错题分布
 * @param params 查询参数
 * @returns 错题分布数据
 */
export function getLearningAnalysisErrorDistribution(params: LearningAnalysisRequest) {
  return POST<ErrorDistributionResponse>(
    `/teacher/homework/student-submission/error-distribution`,
    params,
    { baseURL: BASE_URL }
  );
}

/**
 * 学情分析-作业质量分析-班级平均分（分层作业）
 * @param params 查询参数
 * @returns 班级平均分数据
 */
export function getLearningAnalysisLevelAvgScore(params: LearningAnalysisRequest) {
  return POST<LevelAvgScoreResponse>(
    `/teacher/homework/student-submission/level-avg-score`,
    params,
    { baseURL: BASE_URL }
  );
}

/**
 * 获取学生提交记录列表
 * @param params 查询参数
 * @returns 学生提交记录列表
 */
export function getStudentSubmitRecord(params: StudentSubmitRecordRequest) {
  return POST<StudentSubmitRecordResponse>(`/teacher/homework/studentSubmit`, params, {
    baseURL: BASE_URL
  });
}

/**
 * 学情分析-个体分析
 * @param params 查询参数
 * @returns 个体分析数据
 */
export function getIndividualAnalysis(params: IndividualAnalysisRequest) {
  return POST<IndividualAnalysisResponse>(`/teacher/homework/score`, params, { baseURL: BASE_URL });
}

/**
 * 获取作业题目列表-词云
 * @param params 查询参数
 * @returns 题目列表
 */
export function getHomeworkQuestionList(params: {
  /** 作业ID 测试数据74 */
  homeworkId: number;
  clazzId?: number;
  studentId?: number;
}) {
  return POST<string[]>(`/client/chat/homework/questionList`, params, { baseURL: '/huayun-ai' });
}

// ==================== 学生错题相关接口 ====================

/**
 * 学生错题-错题概况-订正情况
 * 功能描述: 获取学生错题的订正情况统计
 * 入参: homeworkId - 作业ID, clazzId - 班级ID, level - 层级
 * 返回参数: 订正情况统计数据
 * URL地址: /teacher/homework/reviseStats
 * 请求方式: POST
 */
export const getStudentErrorReviseStats = (params: StudentErrorReviseStatsRequest) =>
  POST<StudentErrorReviseStatsResponse>('/teacher/homework/reviseStats', params, {
    baseURL: BASE_URL
  });

/**
 * 学生错题-错题概况-错题占比
 * 功能描述: 获取学生错题的错题占比统计
 * 入参: homeworkId - 作业ID, clazzId - 班级ID, level - 层级
 * 返回参数: 错题占比统计数据
 * URL地址: /teacher/homework/wrongStats
 * 请求方式: POST
 */
export const getStudentErrorWrongStats = (params: StudentErrorWrongStatsRequest) =>
  POST<StudentErrorWrongStatsResponse>('/teacher/homework/wrongStats', params, {
    baseURL: BASE_URL
  });

/**
 * 学生错题-错题概况-知识点/题型错题分布
 * 功能描述: 获取学生错题的知识点和题型错题分布统计
 * 入参: homeworkId - 作业ID, clazzId - 班级ID, level - 层级
 * 返回参数: 知识点/题型错题分布数据
 * URL地址: /teacher/homework/student-submission/error-distribution
 * 请求方式: POST
 */
export const getStudentErrorDistribution = (params: LearningAnalysisRequest) =>
  POST<ErrorDistributionResponse>(
    '/teacher/homework/student-submission/error-distribution',
    params,
    { baseURL: BASE_URL }
  );

/**
 * 学生错题-分页查询学生错题列表
 * 功能描述: 分页查询学生错题列表信息
 * 入参: 分页参数和查询条件
 * 返回参数: 学生错题列表分页数据
 * URL地址: /teacher/homework/student-error/page
 * 请求方式: POST
 */
export const getStudentErrorPage = (params: StudentErrorPageRequest) =>
  POST<PagingData<StudentErrorPageResponse>>('/teacher/homework/student-error/page', params, {
    baseURL: BASE_URL
  });

/**
 * 批量打回订正
 * 功能描述: 批量打回学生错题订正
 * 入参: submitIds - 学生作业提交ID列表, addErrorRule - 归纳错题规则, rejectReason - 打回原因
 * 返回参数: 操作结果
 * URL地址: /teacher/homework/student-error/batch-reject
 * 请求方式: POST
 */
export const batchRejectStudentError = (params: RejectRevisionRequest) =>
  POST<boolean>('/teacher/homework/student-error/batch-reject', params, { baseURL: BASE_URL });

/**
 * 批量归纳至错题本
 * 功能描述: 批量将学生错题归纳至错题本
 * 入参: submitIds - 学生作业提交ID列表
 * 返回参数: 操作结果
 * URL地址: /teacher/homework/student-error/batch-add-to-error
 * 请求方式: POST
 */
export const batchAddToErrorBook = (params: BatchOperationRequest) =>
  POST<boolean>('/teacher/homework/student-error/batch-add-to-error', params, {
    baseURL: BASE_URL
  });

/**
 * 学生错题-学生错题概况
 * 功能描述: 获取学生错题概况统计信息
 * 入参: homeworkId - 作业ID, studentId - 学生ID
 * 返回参数: 学生错题概况数据
 * URL地址: /teacher/homework/student-error/questionStats
 * 请求方式: POST
 */
export const getStudentErrorQuestionStats = (params: StudentErrorQuestionStatsRequest) =>
  POST<StudentErrorQuestionStatsResponse>('/teacher/homework/student-error/questionStats', params, {
    baseURL: BASE_URL
  });

/**
 * 学生错题-获取学生订正提交列表
 * 功能描述: 获取学生订正提交的详细列表信息
 * 入参: id - 学生作业ID
 * 返回参数: 学生订正提交记录
 * URL地址: /teacher/homework/student-error/submitList
 * 请求方式: POST
 */
export const getStudentReviseSubmitList = (params: StudentReviseSubmitListRequest) =>
  POST<StudentReviseSubmitRecord>('/teacher/homework/student-error/submitList', params, {
    baseURL: BASE_URL
  });

/**
 * 获取词云接口
 * @param params
 * @returns
 */
export const getWordCloud = async (params: WordCloudRequest) => {
  const result = await POST<any>('/api/chat/commonBusiness', params, { baseURL: '/huayun-tool' });
  return JSON?.parse((result as any).choices[0].message.content).data as WordCloudResponse[];
};

/**
 * 批量催交
 * 功能描述: 批量催交学生作业
 * 入参: submitIds - 学生作业提交ID列表
 * 返回参数: 操作结果
 * URL地址: /teacher/homework/student-submission/batch-reminder
 * 请求方式: POST
 */
export const batchUrgeStudentSubmit = (params: { ids: string[] }) =>
  POST<boolean>('/teacher/homework/student-submission/batch-reminder', params, {
    baseURL: BASE_URL
  });
