import React from 'react';
import { Box } from '@chakra-ui/react';
import { Empty } from 'antd';
import SvgIcon from '@/components/SvgIcon';

interface EmptyStateProps {
  semesterData: {
    year: string;
    type: 1 | 2;
  };
}

/**
 * 空状态组件
 */
const EmptyState: React.FC<EmptyStateProps> = ({ semesterData }) => {
  return (
    <Box w="390px" height="100%" margin="auto" pt="10%" textAlign="center">
      <SvgIcon name="empty" w="100px" h="100px" cursor="pointer" />
      {semesterData.year && semesterData.type ? (
        <Box fontSize="16px" color="#303133" textAlign="left" mt={4} w="390px">
          {`当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂无学科负责人管理数据。`}
        </Box>
      ) : (
        <Empty description="请选择学期后进行操作。" />
      )}
    </Box>
  );
};

export default EmptyState;
