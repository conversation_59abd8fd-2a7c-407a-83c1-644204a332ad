//行政管理部分
export type AdministrationUpdateParams = {
  deptId: string; // 班级ID
  teachType: number;
  semesterId: number;
  tmbIds: number[];
};

export type ClientSchoolDeptManageTreeType = {
  // tmbIds: string[];
  treeList: DepartmentNode[];
};
export type DepartmentNode = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  deptId: string;
  parentId: string;
  deptName: string;
  subDeptType: number;
  tmbIds: string[];
  semesterId: string;
  tmbUserList: TmbUser[];
  children: DepartmentNode[];
};
export type TmbUser = {
  tmbId: string;
  userName: string;
  userId: string;
  avatar: string;
};

// 教师信息接口
export interface TeacherInfo {
  createTime: string;
  gender: number;
  id: string;
  isDeleted: number;
  name: string;
  phone: string;
  status: number;
  tenantId: number;
  tmbId: number;
  updateTime: string;
}

// 教师列表分页数据
export interface TeacherListResponse {
  code: number;
  data: TeacherInfo[];
  msg: string;
  success: number;
}

export interface DeptTreeResponse {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: number;
  sort: number;
  lastUpgradeTime: string;
  lastSnapTime: string;
  graduateStatus: number;
  yearId: number;
  studentNum: number;
  teacherNames: string;
  parentName: string;
  yearName: string;
  schoolName: string;
  teachers: any[];
  children: DeptTreeResponse[];
  parentValue: null;
  childrenCount: number;
}

//教学组织学科负责人
export type Department = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  deptId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: number;
  sort: number;
  subjectId: string;
  tmbIds: string;
  semesterId: string;
  tmbUserList: User[];
  subjectName: string;
  children: Department[];
  teachType?: number; // 添加可选的teachType字段
};

export type SubjectTeacherTreeTypeResponse = {
  tmbIds: string[];
  treeList: Department[];
};
export type User = {
  tmbId: string;
  userId: string;
  userName: string;
};

// 班级详情接口
export interface ClassDetailResponse {
  id: number;
  name: string;
  stageId: number;
  stageName: string;
  gradeId: number;
  gradeName: string;
  yearId: number;
}

// 班级任课老师用户信息
export interface ClassTeacherUser {
  tmbId: string;
  userId: string;
  userName: string;
  avatar: string;
}

// 班级任课老师信息
export interface ClassTeacherInfo {
  semesterId: number;
  year: string;
  type: number;
  deptId: number;
  teachType: number;
  subjectId: number;
  teachTypeName: string;
  subjectName: string;
  tmbUserList: ClassTeacherUser[];
}

// 班级任课老师响应类型
export type ClassTeachersResponse = ClassTeacherInfo[];

// 单个教师配置项
export interface TeacherConfigItem {
  deptId: number;
  teachType: number;
  semesterId: number;
  tmbIds: number[];
  subjectId?: number; // 任课老师需要科目ID
}

// 批量更新教师入参
export interface UpdateTeacherBatchParams {
  list: TeacherConfigItem[];
}

// 班级教师管理用户信息
export interface ClazzTeacherUser {
  tmbId: string;
  userId: string;
  userName: string;
  avatar: string;
}

// 班级教师管理响应项
export interface ClazzTeacherRespItem {
  semesterId: number;
  year: string;
  type: number;
  deptId: number;
  teachType: number;
  subjectId: number | null;
  teachTypeName: string;
  subjectName: string;
  tmbUserList: ClazzTeacherUser[];
}

// 班级教师管理响应
export interface ClazzTeacherManageResp {
  deptId: number;
  clazzTeacherRespList: ClazzTeacherRespItem[];
}

// 班级教师管理列表响应类型
export type ClazzTeacherManageListResponse = ClazzTeacherManageResp[];

// 学生信息接口
export interface StudentInfo {
  id: string;
  name: string;
  code: string;
  gender?: number;
  phone?: string;
  status?: number;
}

// 学生分页查询参数（不在指定班级）
export interface StudentPageExcludeClazzParams {
  clazzId: string;
  semesterId: string;
  name?: string;
  code?: string;
  current: string;
  size: string;
}

// 学生分页查询参数（指定班级）
export interface StudentPageByClazzParams {
  current: number;
  size: number;
  clazzId: number;
  name?: string;
  code?: string;
}

// 批量调入学生参数
export interface BatchChangeClazzParams {
  clazzId: string;
  semesterId: string;
  studentIds: number[];
}

// 调出学生参数
export interface ChangeClazzParams {
  id: string;
  stageId: number;
  gradeId: number;
  clazzId: number;
}

// 学生分页响应
export interface StudentPageResponse {
  records: StudentInfo[];
  total: number;
  size: number;
  current: number;
  pages: number;
}
