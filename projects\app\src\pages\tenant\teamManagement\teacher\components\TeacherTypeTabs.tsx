import {
  Box,
  Flex,
  Select,
  Button,
  FormControl,
  FormLabel,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody
} from '@chakra-ui/react';
import MyInput from '@/components/MyInput';
import MySelect from '@/components/MySelect';
import MyModal from '@/components/MyModal';
import { vwDims } from '@/utils/chakra';
import { useState, useEffect } from 'react';
import TagTextarea from '@/components/common/Textarea/TagTextarea';
import TeachersAdd from './TeachersAdd';
import { getTenantSubjectList, getTeacherPage } from '@/api/teacher';
import type { TenantSubjectItem, TeacherPageParams } from '@/types/api/teacher';
import { Tag, TagLabel, TagCloseButton, List, ListItem, useOutsideClick } from '@chakra-ui/react';
import React, { useRef } from 'react';
import type { TeacherAccountItem } from '@/types/api/teacher';
import {
  SCHOOL_MANAGE_TEACHER_TYPE_MAP,
  SCHOOL_SUBJECT_TEACHER_TYPE_MAP
} from '@/constants/teacher';
import { extractSubjectListFromResponse } from '@/utils/subjectUtils';


const STATUS_OPTIONS = [
  { label: '在职', value: 0 },
  { label: '离职', value: 1 }
];

const ADMIN_ROLE_OPTIONS = Object.entries(SCHOOL_MANAGE_TEACHER_TYPE_MAP).map(([value, label]) => ({
  label,
  value: Number(value)
}));
const TEACH_ROLE_OPTIONS = Object.entries(SCHOOL_SUBJECT_TEACHER_TYPE_MAP).map(
  ([value, label]) => ({ label, value: Number(value) })
);

interface TeacherTypeTabsProps {
  onSearch?: (params: any) => void;
  onAddTeacher?: () => void;
  accountList: TeacherAccountItem[];
  addModalOpen: boolean;
  onAddModalClose: () => void;
}

// 自定义标签式多选下拉组件
interface TagSelectOption {
  label: string;
  value: number;
}
interface TagSelectProps {
  options: TagSelectOption[];
  value: number[];
  onChange: (vals: number[]) => void;
  placeholder?: string;
  label?: string;
  width?: string;
  bg?: string;
  height?: string;
  minHeight?: string;
}
const TagSelect: React.FC<TagSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  label,
  width = '140px',
  bg,
  height,
  minHeight
}) => {
  const [inputValue, setInputValue] = React.useState('');
  const [dropdownOpen, setDropdownOpen] = React.useState(false);
  const ref = useRef<HTMLDivElement>(null);
  useOutsideClick({ ref, handler: () => setDropdownOpen(false) });
  const filteredOptions = options.filter(
    (opt: TagSelectOption) =>
      !value.includes(opt.value) && (!inputValue || opt.label.includes(inputValue))
  );
  const handleSelect = (val: number) => {
    onChange([...value, val]);
    setInputValue('');
    setDropdownOpen(false);
  };
  const handleRemove = (val: number) => {
    onChange(value.filter((v: number) => v !== val));
  };
  return (
    <FormControl w={width} position="relative">
      {label && (
        <FormLabel fontSize={vwDims(14)} mb={vwDims(4)}>
          {label}
        </FormLabel>
      )}
      <Box
        borderWidth="1px"
        borderRadius="md"
        p={vwDims(4)}
        minH={vwDims(36)}
        bg={bg || '#fff'}
        onClick={() => setDropdownOpen(true)}
        cursor="text"
        ref={ref}
        w={value.length === 0 ? `${vwDims(65)}px` : 'auto'}
        h={value.length === 0 ? vwDims(40) : 'auto'}
      >
        <Flex wrap="wrap" align="center" gap={vwDims(4)}>
          {value.map((val: number) => {
            const opt = options.find((o: TagSelectOption) => o.value === val);
            return (
              <Tag key={val} borderRadius="full" variant="solid" colorScheme="blue" size="sm">
                <TagLabel fontSize={vwDims(12)}>{opt?.label}</TagLabel>
                <TagCloseButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(val);
                  }}
                />
              </Tag>
            );
          })}
          <MyInput
            variant="unstyled"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={value.length === 0 ? placeholder : ''}
            width="auto"
            minW={vwDims(80)}
            fontSize={vwDims(20)}
            onFocus={() => setDropdownOpen(true)}
            onBlur={() => setDropdownOpen(false)}
            _focus={{ boxShadow: 'none' }}
            display="flex"
            alignItems="center"
            height="100%"
            _placeholder={{ fontSize: vwDims(14), color: '#4E5969' }}
          />
        </Flex>
      </Box>
      {dropdownOpen && filteredOptions.length > 0 && (
        <Box
          position="absolute"
          zIndex={10}
          bg="white"
          borderWidth="1px"
          borderRadius="md"
          mt={vwDims(4)}
          w="100%"
          maxH={vwDims(180)}
          overflowY="auto"
          boxShadow="md"
        >
          <List>
            {filteredOptions.map((opt: TagSelectOption) => (
              <ListItem
                key={opt.value}
                px={vwDims(12)}
                py={vwDims(8)}
                cursor="pointer"
                fontSize={vwDims(14)}
                _hover={{ bg: 'gray.100' }}
                onMouseDown={() => handleSelect(opt.value)}
              >
                {opt.label}
              </ListItem>
            ))}
          </List>
        </Box>
      )}
    </FormControl>
  );
};

const TeacherTypeTabs = ({
  onSearch,
  onAddTeacher,
  accountList,
  addModalOpen,
  onAddModalClose
}: TeacherTypeTabsProps) => {
  const [form, setForm] = useState({
    name: '',
    account: '',
    phone: '',
    status: '',
    adminRoles: [] as number[],
    teachRoles: [] as number[]
  });
  const [subjectList, setSubjectList] = useState<TenantSubjectItem[]>([]);

  useEffect(() => {
    getTenantSubjectList().then((res) => {
      const list = extractSubjectListFromResponse(res);
      setSubjectList(list);
    });
  }, []);

  const handleChange = (key: string, value: any) => {
    console.log(`表单字段 ${key} 发生变化:`, value);
    setForm((prev) => {
      const newForm = { ...prev, [key]: value };
      console.log('更新后的表单数据:', newForm);
      return newForm;
    });
  };

  const handleSearch = () => {
    console.log('搜索按钮被点击，当前表单数据:', form);
    const searchParams = {
      name: form.name,
      account: form.account,
      phone: form.phone,
      status: form.status === '' ? undefined : Number(form.status),
      schoolManageTeachTypes: form.adminRoles,
      schoolSubjectTeachTypes: form.teachRoles
    };
    console.log('准备发送搜索参数:', searchParams);
    if (onSearch) {
      onSearch(searchParams);
    }
  };

  const handleReset = () => {
    console.log('重置按钮被点击');
    setForm({
      name: '',
      account: '',
      phone: '',
      status: '',
      adminRoles: [],
      teachRoles: []
    });
    if (onSearch) {
      console.log('发送空搜索参数进行重置');
      onSearch({});
    }
  };



  return (
    <Box p={vwDims(24)} h={vwDims(150)}>
      {/* 第一行 */}
      <Flex align="center" gap={vwDims(8)} left={vwDims(46)} position="absolute" top={vwDims(84)}>
        {/* 教师姓名 */}
        <Flex align="center" w={vwDims(260)} gap={vwDims(8)}>
          <Text
            w={vwDims(70)} // 统一宽度
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            教师姓名：
          </Text>
          <MyInput
            value={form.name}
            onChange={(e) => handleChange('name', e.target.value)}
            size="sm"
            placeholder="请输入"
            w={vwDims(160)}
            h={vwDims(40)}
            borderRadius={vwDims(8)}
            px={vwDims(12)}
            py={vwDims(4)}
            bg="#F2F3F5"
            border="none"
          />
        </Flex>

        {/* 账号 */}
        <Flex align="center" w={vwDims(260)} gap={vwDims(8)}>
          <Text
            w={vwDims(70)}
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            账号：
          </Text>
          <MyInput
            value={form.account}
            onChange={(e) => handleChange('account', e.target.value)}
            size="sm"
            placeholder="请输入"
            w={vwDims(160)}
            h={vwDims(40)}
            borderRadius={vwDims(8)}
            px={vwDims(12)}
            py={vwDims(4)}
            bg="#F2F3F5"
            border="none"
          />
        </Flex>

        {/* 手机号 */}
        <Flex align="center" w={vwDims(260)} gap={vwDims(8)}>
          <Text
            w={vwDims(70)}
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            手机号：
          </Text>
          <MyInput
            value={form.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            size="sm"
            placeholder="请输入"
            w={vwDims(160)}
            h={vwDims(40)}
            borderRadius={vwDims(8)}
            px={vwDims(12)}
            py={vwDims(4)}
            bg="#F2F3F5"
            border="none"
          />
        </Flex>

        {/* 行政职务 */}
        <Flex align="center" w={vwDims(280)} h={vwDims(40)} gap={vwDims(8)}>
          <Text
            w={vwDims(70)}
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            行政职务：
          </Text>
          <TagSelect
            options={ADMIN_ROLE_OPTIONS}
            value={form.adminRoles}
            onChange={(vals) => handleChange('adminRoles', vals)}
            placeholder="请选择职务"
            bg="#F2F3F5"
          />
        </Flex>

        {/* 教学职务 */}
        <Flex align="center" w={vwDims(320)} gap={vwDims(8)}>
          <Text
            w={vwDims(70)}
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            教学职务：
          </Text>
          <TagSelect
            options={TEACH_ROLE_OPTIONS}
            value={form.teachRoles}
            onChange={(vals) => handleChange('teachRoles', vals)}
            placeholder="请选择职务"
            bg="#F2F3F5"
          />
        </Flex>
      </Flex>

      {/* 状态label和下拉框一行 */}
      <Flex
        align="center"
        gap={vwDims(32)}
        w={vwDims(549)}
        h={vwDims(40)}
        position="absolute"
        top={vwDims(150)}
        // left={vwDims(256)}
      >
        {/* 状态下拉框 */}
        <Flex
          align="center"
          w={vwDims(600)}
          h={vwDims(40)}
          position="absolute"
          top={vwDims(0)}
          left={vwDims(47)}
        >
          <Text
            w={vwDims(70)} // 统一宽度
            h={vwDims(22)}
            fontFamily="PingFang SC"
            fontWeight={500}
            fontSize={vwDims(14)}
            lineHeight={vwDims(22)}
            color="#1D2129"
          >
            状态：
          </Text>
          <MySelect
            value={form.status}
            onchange={(value) => handleChange('status', value)}
            placeholder="全部"
            list={[
              { label: '在职', value: '0', alias: '在职' },
              { label: '离职', value: '1', alias: '离职' }
            ]}
            size="sm"
            bg="#F2F3F5"
            h={vwDims(32)}
            color="#4E5969"
            w={vwDims(520)}
            border="none"
            borderRadius={vwDims(8)}
          />
        </Flex>
      </Flex>
      <Box
        w={vwDims(190)}
        h={vwDims(40)}
        position="absolute"
        top={vwDims(150)}
        right="0"
        display="flex"
        alignItems="center"
        gap={vwDims(8)}
      >
        <Button size="sm" h={vwDims(32)} px={vwDims(16)} onClick={handleSearch}>
          搜索
        </Button>
        <Button size="sm" h={vwDims(32)} px={vwDims(16)} onClick={handleReset} variant="outline">
          重置
        </Button>
      </Box>
      <Box flex={1} />
      <Button
        colorScheme="purple"
        size="lg"
        w={vwDims(130)}
        h={vwDims(32)}
        position="absolute"
        top={vwDims(34)}
        left={vwDims(1421)}
        opacity={1}
        borderRadius={vwDims(8)}
        gap={vwDims(8)}
        pt={vwDims(7)}
        pr={vwDims(20)}
        pb={vwDims(7)}
        pl={vwDims(20)}
        bg="var(--purple-500, #7D4DFF)"
        onClick={onAddTeacher}
      >
        + 添加教师
      </Button>

      {/* 添加教师弹窗 */}
      <MyModal isOpen={addModalOpen} onClose={onAddModalClose} w="xl">
        <TeachersAdd
          isOpen={addModalOpen}
          onClose={onAddModalClose}
          onSuccess={() => {
            onAddModalClose();
            if (typeof onSearch === 'function') {
              onSearch({}); // 触发父级刷新
            }
          }}
        />
      </MyModal>


    </Box>
  );
};

export default TeacherTypeTabs;
