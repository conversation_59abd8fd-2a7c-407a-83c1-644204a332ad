import React, { useEffect, useState } from 'react';
import { FormControl, FormLabel, Flex, Button, ModalBody, useToast } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import MyBox from '@/components/common/MyBox';
import { Toast } from '@/utils/ui/toast';
import SchoolStructureSelect from '../SchoolStructureSelect/SchoolStructureSelect';
import { changeStudentClazz, getSchoolDeptTree } from '@/api/student';
import { ChangeClazzClientStudentParams } from '@/types/api/tenant/teamManagement/student';
import { useTenantStore } from '@/store/useTenantStore';

interface FormData {
  schoolStructure: {
    stageId: string;
    gradeId: string;
    clazzId: string;
  };
}

interface ChangeClassModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  studentId: string;
  studentInfo: any;
}

interface TreeSelectDataItem {
  value: string;
  title: string;
  children?: TreeSelectDataItem[];
  isLeaf: boolean;
}

const ChangeClassModal: React.FC<ChangeClassModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  studentId,
  studentInfo
}) => {
  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<FormData>();
  const { industryAlias } = useTenantStore();
  const [schoolStructure, setSchoolStructure] = useState<TreeSelectDataItem[]>([]);

  useEffect(() => {
    const fetchSchoolStructure = async () => {
      try {
        const res = await getSchoolDeptTree();
        const convertedData = convertToTreeSelectData(res);
        setSchoolStructure(convertedData);
      } catch (error) {
        console.error(`获取${industryAlias}结构失败:`, error);
        Toast.error(`获取${industryAlias}结构失败，请重试`);
      }
    };

    fetchSchoolStructure();
  }, []);

  const convertToTreeSelectData = (data: any[]): TreeSelectDataItem[] => {
    return data.map((item) => {
      // 格式化班级名称，将七年级改为初一
      const formattedTitle = (item.deptName || '').replace(/七年级/g, '初一');
      return {
        value: item.id,
        title: formattedTitle,
        children: item.children ? convertToTreeSelectData(item.children) : undefined,
        isLeaf: !item.children || item.children.length === 0
      };
    });
  };

  const onSubmit = async (data: FormData) => {
    try {
      if (
        data.schoolStructure.stageId &&
        data.schoolStructure.gradeId &&
        data.schoolStructure.clazzId
      ) {
        await changeStudentClazz({
          id: String(studentId),
          stageId: String(data.schoolStructure.stageId),
          gradeId: String(data.schoolStructure.gradeId),
          clazzId: String(data.schoolStructure.clazzId)
        });
        Toast.success('调班成功');
        onClose();
        onSuccess();
      } else {
        Toast.error('请选择完整的班级信息');
      }
    } catch (error) {
      console.error('调班失败:', error);
      Toast.error('调班失败，请重试');
    }
  };

  return (
    <MyModal isOpen={isOpen} title={'调班'} isCentered>
      <ModalBody>
        <MyBox p="20px">
          <FormControl mb="14px">
            <Flex alignItems="center">
              <FormLabel color="#4E5969" fontSize="14px" mb="0" width="80px">
                学生姓名：
              </FormLabel>
              <MyBox>{studentInfo.name}</MyBox>
            </Flex>
          </FormControl>

          <FormControl mb="14px">
            <Flex alignItems="center">
              <FormLabel color="#4E5969" fontSize="14px" mb="0" width="80px">
                原年级：
              </FormLabel>
              <MyBox>{(studentInfo.gradeName || '').replace(/七年级/g, '初一')}</MyBox>
            </Flex>
          </FormControl>

          <FormControl mb="14px">
            <Flex alignItems="center">
              <FormLabel color="#4E5969" fontSize="14px" mb="0" width="80px">
                原班级：
              </FormLabel>
              <MyBox>{studentInfo.clazzName}</MyBox>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.schoolStructure}>
            <Flex alignItems="baseline" whiteSpace="nowrap">
              <FormLabel color="#4E5969" fontSize="14px" width="80px">
                <MyBox
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  调班：
                </MyBox>
              </FormLabel>
              <Flex flexDir="column" flex={1}>
                <SchoolStructureSelect
                  control={control}
                  name="schoolStructure"
                  treeData={schoolStructure}
                  placeholder="请选择学段/年级/班级"
                  mode="edit"
                />
                {errors.schoolStructure && (
                  <MyBox color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.schoolStructure.message}
                  </MyBox>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <Flex justifyContent="end" mt="20px">
            <Button variant={'grayBase'} onClick={() => onClose()}>
              取消
            </Button>
            <Button colorScheme="purple" onClick={handleSubmit(onSubmit)} ml={3}>
              确认
            </Button>
          </Flex>
        </MyBox>
      </ModalBody>
    </MyModal>
  );
};

export default ChangeClassModal;
