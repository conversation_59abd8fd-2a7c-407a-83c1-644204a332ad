import { HasPersonalAccountEnum } from '@/constants/api/aiPlatform';

export interface AiAccountType {
  categoryName: string;
  clientUserThirdPartyAccountResponses: UserThirdPartyAccountResponseType[];
}

export interface UserThirdPartyAccountResponseType {
  platformName: string;
  intro: string;
  link: string;
  logo: string;
  sort: string;
  instructions: string;
  videoUrl: string;
  accounts: AccountsType[];
  password: string;
  instructions: string;
  hasPersonalAccount: HasPersonalAccountEnum;
}

export interface AccountsType {
  account: string;
  password: string;
}
