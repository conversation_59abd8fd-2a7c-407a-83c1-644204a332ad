import React, { useState, useEffect } from 'react';
import { Box, Flex, Text, Button, Avatar } from '@chakra-ui/react';
import { ClockCircleOutlined } from '@ant-design/icons';
import { Select, Table, Tag, Input, DatePicker, Pagination } from 'antd';
import dayjs from 'dayjs';
import styled from '@emotion/styled';
import { useDebounce } from 'ahooks';
import { useQuery } from '@tanstack/react-query';
import { getStudentSubmissionPage } from '@/api/homeworkDetail';
import { StudentSubmissionPageResponse, HomeworkDetailResponse } from '@/types/api/homeworkDetail';
import { vwDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import ClassTabs from '../../ClassTabs';
import {
  getStudentActionButtons,
  getBatchActionButtons,
  getStatusConfig,
  getStatusName,
  getCorrectMethodName,
  getSubmitMethodName
} from '@/utils/homeworkButtonUtils';
import { useHomeworkStore } from '@/store/useHomework';
import { useStudentActions } from '../hooks/useStudentActions';

const { RangePicker } = DatePicker;

// 样式化的表格组件
const StyledTable = styled(Table)`
  .ant-table-thead > tr > th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    color: #262626;
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }
`;

interface StudentSubmissionDetailsProps {
  homeworkId: string | null;
}

function StudentSubmissionDetails({ homeworkId }: StudentSubmissionDetailsProps) {
  // 获取作业详情数据
  const { homeworkDetail } = useHomeworkStore();

  // 筛选条件状态
  const [filterSearchKey, setFilterSearchKey] = useState<string>(''); // 搜索关键字（学生学号、姓名）
  const debouncedSearchKey = useDebounce(filterSearchKey, { wait: 500 }); // 防抖处理，500ms延迟
  const [filterLevel, setFilterLevel] = useState<number | null>(null); // 分层
  const [filterStatus, setFilterStatus] = useState<number | null>(null); // 状态
  const [submitStartTime, setSubmitStartTime] = useState<string>(''); // 提交开始日期
  const [submitEndTime, setSubmitEndTime] = useState<string>(''); // 提交结束日期
  const [confirmStartTime, setConfirmStartTime] = useState<string>(''); // 批改完成开始日期
  const [confirmEndTime, setConfirmEndTime] = useState<string>(''); // 批改完成结束日期

  // 使用学生操作 hook
  const {
    handleStudentAction,
    handleBatchUrge,
    handleBatchReModifyWithConfirm,
    handleExportCorrectionResult,
    handleBatchConfirm
  } = useStudentActions();

  // 表格处选择班级
  const [selectedClass, setSelectedClass] = useState<any>(null); // null表示"全部"
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 学生提交数据
  const [studentSubmissionData, setStudentSubmissionData] =
    useState<StudentSubmissionPageResponse | null>(null);

  // 监听筛选条件变化，清空选择状态
  useEffect(() => {
    setSelectedRowKeys([]);
  }, [
    selectedClass,
    debouncedSearchKey,
    filterLevel,
    filterStatus,
    submitStartTime,
    submitEndTime,
    confirmStartTime,
    confirmEndTime,
    currentPage
  ]);

  // 获取学生提交与批改列表数据
  useQuery({
    queryKey: [
      'studentSubmission',
      selectedClass,
      homeworkId,
      currentPage,
      pageSize,
      debouncedSearchKey,
      filterLevel,
      filterStatus,
      submitStartTime,
      submitEndTime,
      confirmStartTime,
      confirmEndTime
    ],
    queryFn: () =>
      getStudentSubmissionPage({
        clazzId: selectedClass,
        current: currentPage,
        size: pageSize,
        homeworkId: homeworkId ? parseInt(homeworkId) : 0,
        // 添加筛选条件
        searchKey: debouncedSearchKey || undefined, // 防抖后的搜索关键字（学生学号、姓名）
        status: filterStatus, // 状态筛选
        level: filterLevel || undefined, // 学生作业层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
        submitStartTime: submitStartTime || undefined, // 提交开始日期
        submitEndTime: submitEndTime || undefined, // 提交结束日期
        confirmStartTime: confirmStartTime || undefined, // 批改完成开始日期
        confirmEndTime: confirmEndTime || undefined // 批改完成结束日期
      }),
    enabled: !!homeworkId,
    onSuccess: (res) => {
      console.log('studentSubmission', res);
      setStudentSubmissionData(res);
      // 当数据更新时，清空之前的选择状态，避免跨页选择问题
      setSelectedRowKeys([]);
    }
  });

  // 批量催交
  const handleBatchRemind = () => {
    handleBatchUrge(selectedRowKeys);
  };

  // 渐变文本样式
  const gradientTextStyle = {
    background: 'linear-gradient(115deg, #268BFF 16.58%, #682CFF 51.05%, #9A0BFF 84.21%)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent'
  };

  // 操作按钮样式
  const getActionButtonStyle = (type: 'primary' | 'secondary' | 'correction') => {
    const baseStyle = {
      fontSize: `${vwDims(14)} !important`,
      fontWeight: '500 !important',
      height: `${vwDims(32)} !important`,
      padding: `0 ${vwDims(16)} !important`,
      borderRadius: '8px !important'
    };

    switch (type) {
      case 'primary':
        return {
          ...baseStyle,
          border: '1px solid #7D4DFF !important',
          color: '#7D4DFF !important',
          background: 'transparent !important'
        };
      case 'secondary':
        return {
          ...baseStyle,
          border: '1px solid #E5E7EB !important',
          color: '#606266 !important',
          background: 'transparent !important'
        };
      case 'correction':
        return {
          ...baseStyle,
          border: '1px solid transparent !important',
          background:
            'linear-gradient(white, white) padding-box, linear-gradient(115deg, #268BFF 16.58%, #682CFF 51.05%, #9A0BFF 84.21%) border-box !important'
        };
      default:
        return baseStyle;
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '学生',
      key: 'student',
      width: 120,
      render: (_: any, record: any) => (
        <Flex alignItems="center" gap={vwDims(8)}>
          <SvgIcon name="columnsStudentIcon" w={vwDims(33)} h={vwDims(33)} />
          <Box>
            <Text fontSize={vwDims(14)} fontWeight="500" color="#303133">
              {record.studentName}
            </Text>
            <Text fontSize={vwDims(12)} color="#909399">
              {record.studentId}
            </Text>
          </Box>
        </Flex>
      )
    },
    {
      title: '班级',
      width: 100,
      render: (_: any, record: any) => (
        <Box
          display={'inline-block'}
          fontSize={vwDims(14)}
          color="#606266"
          height={vwDims(27)}
          lineHeight={vwDims(27)}
          background={'#F2F3F5'}
          padding={`${vwDims(0)} ${vwDims(8)}`}
          borderRadius={'2px'}
        >
          {record.gradeName}
          {record.className}
        </Box>
      )
    },
    {
      title: '得分/满分',
      dataIndex: 'scoreInfo',
      key: 'scoreInfo',
      width: 120,
      render: (_: string, record: any) => {
        if (!record.score && record.score !== 0) {
          return (
            <Text fontSize={vwDims(14)} color="#909399">
              -
            </Text>
          );
        }
        return (
          <Box display="flex" alignItems="center" gap={vwDims(10)}>
            {record?.isRevise === 1 && (
              <Text
                display="flex"
                width={vwDims(50)}
                padding={`${vwDims(1)} ${vwDims(8)}`}
                justifyContent="center"
                alignItems="center"
                gap={vwDims(4)}
                borderRadius="4px"
                background="#ECF2FF"
                color={'#1F7BFF'}
                fontSize={vwDims(14)}
              >
                订正
              </Text>
            )}
            <Text fontSize={vwDims(14)} color="#303133" fontWeight="500">
              {record.score}/{record.totalScore || 100}分
            </Text>
          </Box>
        );
      }
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_: any, record: any) => {
        const status = getStatusConfig(record.status);
        if (!status) return <span>-</span>;

        return (
          <Box
            display="inline-flex"
            padding={`${vwDims(1)} ${vwDims(8)}`}
            justifyContent="center"
            alignItems="center"
            borderRadius="50px"
            background={status.background}
            fontSize={vwDims(14)}
            fontWeight="500"
            width="fit-content"
          >
            {status?.isGradient ? (
              <Box sx={gradientTextStyle}>
                <span style={{ marginRight: '4px' }}>•</span>
                {status.text}
              </Box>
            ) : (
              <Box color={status.color}>
                <span style={{ marginRight: '4px' }}>•</span>
                {status.text}
              </Box>
            )}
          </Box>
        );
      }
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      width: 140,
      render: (text: string) => (
        <Text fontSize={vwDims(14)} color="#606266">
          {text || '-'}
        </Text>
      )
    },
    {
      title: '批改完成时间',
      dataIndex: 'correctionTime',
      key: 'correctionTime',
      width: 140,
      render: (text: string) => (
        <Text fontSize={vwDims(14)} color="#606266">
          {text || '-'}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: any) => {
        if (!homeworkDetail) return null;

        const buttonsConfig = getStudentActionButtons(
          record.status || 0,
          homeworkDetail.correctMethod,
          homeworkDetail.submitMethod
        );

        const renderButton = (key: string, config: any) => {
          if (!config.show) return null;

          return (
            <Button
              key={key}
              variant={'outline'}
              sx={getActionButtonStyle(config.type)}
              onClick={() => handleStudentAction(config.action, record)}
            >
              {config.type === 'correction' ? (
                <Box sx={gradientTextStyle}>{config.text}</Box>
              ) : (
                config.text
              )}
            </Button>
          );
        };

        return (
          <Flex gap={vwDims(8)} flexWrap="wrap">
            {renderButton('view', buttonsConfig.view)}
            {renderButton('urge', buttonsConfig.urge)}
            {renderButton('helpRecord', buttonsConfig.helpRecord)}
            {renderButton('manualCorrect', buttonsConfig.manualCorrect)}
            {renderButton('aiCorrect', buttonsConfig.aiCorrect)}
            {renderButton('confirm', buttonsConfig.confirm)}
            {renderButton('modify', buttonsConfig.modify)}
            {renderButton('reCorrect', buttonsConfig.reCorrect)}
          </Flex>
        );
      }
    }
  ];

  return (
    <Box>
      <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
        <Box mb={vwDims(13)} display={'flex'} alignItems={'center'}>
          <Box fontSize={vwDims(18)} fontWeight={500} color={'#1D2129'}>
            学生提交与批改详情
          </Box>
        </Box>
        {/* 操作按钮区域 */}
        <Box mb={vwDims(20)}>
          <Box display="flex" justifyContent="flex-end" gap={vwDims(12)}>
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              onClick={() => handleBatchReModifyWithConfirm(selectedRowKeys)}
              sx={gradientTextStyle}
            >
              <SvgIcon name="homeworkEditIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              批量重新修改
            </Button>
            <Button
              variant={'outline'}
              fontWeight={500}
              fontSize={vwDims(14)}
              border={'1px solid #7D4DFF'}
              borderRadius={'8px'}
              color={'#7D4DFF'}
              onClick={() => handleExportCorrectionResult(selectedRowKeys)}
            >
              <SvgIcon name="homeworkExportIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              导出批改结果
            </Button>
            <Button colorScheme={'#7D4DFF'} onClick={() => handleBatchConfirm(selectedRowKeys)}>
              <SvgIcon name="homeworkConfirmIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              批量确认
            </Button>
            <Button colorScheme={'#7D4DFF'} onClick={handleBatchRemind}>
              <SvgIcon name="homeworkRemindIcon" w={vwDims(12)} h={vwDims(12)} mr={vwDims(8)} />
              批量催交
            </Button>
          </Box>
        </Box>
      </Box>

      {/* 学生提交与批改详情 */}
      <Box
        h={vwDims(567)}
        borderRadius={'10px'}
        border={'1px solid #e7e7e7'}
        padding={`${vwDims(20)} ${vwDims(16)} ${vwDims(20)}`}
      >
        <Box>
          {/* 班级选择标签 */}
          <ClassTabs
            value={selectedClass}
            onChange={(value) => {
              setSelectedClass(value);
              console.log('Selected class:', value);
            }}
          />
        </Box>

        <Box>
          {/* 筛选条件行 */}
          <Flex
            mb={vwDims(16)}
            alignItems="center"
            gap={vwDims(16)}
            color={'#1D2129'}
            sx={{
              '.ant-input-affix-wrapper': {
                border: 'none !important',
                borderRadius: '8px !important',
                background: '#F2F3F5 !important',
                padding: `0px ${vwDims(11)} !important`
              },
              'input::placeholder': {
                fontSize: `${vwDims(14)} !important`,
                color: '#86909C'
              },
              '.ant-select-selector': {
                border: 'none !important',
                borderRadius: '8px !important',
                background: '#F2F3F5 !important',
                fontSize: `${vwDims(14)} !important`
              },
              '.ant-select-selection-placeholder': {
                color: '#1D2129 !important',
                fontSize: `${vwDims(14)} !important`
              },
              '.ant-picker-outlined': {
                border: 'none !important',
                borderRadius: '8px !important',
                background: '#F2F3F5 !important',
                padding: `0px ${vwDims(11)} !important`
              }
            }}
          >
            <Flex alignItems="center" gap={vwDims(5)}>
              <Text fontSize={vwDims(14)} fontWeight={500}>
                学生：
              </Text>
              <Input
                placeholder="请输入学生学号、学生姓名"
                style={{ width: vwDims(215), height: vwDims(32) }}
                allowClear
                value={filterSearchKey}
                onChange={(e) => setFilterSearchKey(e.target.value)}
              />
            </Flex>

            <Flex alignItems="center" gap={vwDims(5)}>
              <Text fontSize={vwDims(14)} fontWeight={500}>
                状态：
              </Text>
              <Select
                value={filterStatus}
                style={{ width: vwDims(96), height: vwDims(32) }}
                placeholder="全部状态"
                onChange={(value) => setFilterStatus(value)}
                options={[
                  { value: null, label: '全部状态' },
                  { value: 0, label: '待提交' },
                  { value: 1, label: '未提交' },
                  { value: 2, label: '待批改' },
                  { value: 3, label: 'AI批改中' },
                  { value: 4, label: '待确认' },
                  { value: 5, label: '已完成' },
                  { value: 6, label: '待订正' },
                  { value: 7, label: '批改失败' }
                ]}
              />
            </Flex>

            <Flex alignItems="center" gap={vwDims(5)}>
              <Text fontSize={vwDims(14)} fontWeight={500}>
                提交时间：
              </Text>
              <RangePicker
                showTime={{ format: 'HH:mm' }}
                format="MM月DD日 HH:mm"
                placeholder={['开始时间', '结束时间']}
                style={{ width: vwDims(240), height: vwDims(32) }}
                value={[
                  submitStartTime ? dayjs(submitStartTime) : null,
                  submitEndTime ? dayjs(submitEndTime) : null
                ]}
                onChange={(dates) => {
                  if (dates) {
                    setSubmitStartTime(dates[0] ? dates[0].format('YYYY-MM-DD HH:mm:ss') : '');
                    setSubmitEndTime(dates[1] ? dates[1].format('YYYY-MM-DD HH:mm:ss') : '');
                  } else {
                    setSubmitStartTime('');
                    setSubmitEndTime('');
                  }
                }}
                suffixIcon={
                  <ClockCircleOutlined style={{ color: '#4E5969', fontSize: vwDims(14) }} />
                }
              />
            </Flex>

            <Flex alignItems="center" gap={vwDims(5)}>
              <Text fontSize={vwDims(14)} fontWeight={500}>
                批改完成时间：
              </Text>
              <RangePicker
                showTime={{ format: 'HH:mm' }}
                format="MM月DD日 HH:mm"
                placeholder={['开始时间', '结束时间']}
                style={{ width: vwDims(240), height: vwDims(32) }}
                value={[
                  confirmStartTime ? dayjs(confirmStartTime) : null,
                  confirmEndTime ? dayjs(confirmEndTime) : null
                ]}
                onChange={(dates) => {
                  if (dates) {
                    setConfirmStartTime(dates[0] ? dates[0].format('YYYY-MM-DD HH:mm:ss') : '');
                    setConfirmEndTime(dates[1] ? dates[1].format('YYYY-MM-DD HH:mm:ss') : '');
                  } else {
                    setConfirmStartTime('');
                    setConfirmEndTime('');
                  }
                }}
                suffixIcon={
                  <ClockCircleOutlined style={{ color: '#4E5969', fontSize: vwDims(14) }} />
                }
              />
            </Flex>
          </Flex>

          {/* 表格 */}
          <StyledTable
            rowKey={(record) => record.id || record.studentId} // 添加唯一的rowKey
            rowSelection={{
              selectedRowKeys,
              onChange: (newSelectedRowKeys: React.Key[]) => {
                // 修改参数名避免冲突
                console.log('Table selection changed:', newSelectedRowKeys);
                setSelectedRowKeys(newSelectedRowKeys);
              },
              getCheckboxProps: (record) => ({
                disabled: false,
                name: record.studentName
              })
            }}
            pagination={false}
            dataSource={studentSubmissionData?.records || []}
            columns={columns}
            scroll={{ y: vwDims(384) }}
          />
        </Box>
      </Box>

      {/* 分页组件 */}
      <Box>
        <Flex
          justifyContent={'flex-end'}
          mt={vwDims(24)}
          sx={{
            '.ant-select-selector': {
              border: 'none !important',
              background: '#F2F3F5 !important',
              borderRadius: '2px !important'
            },
            '.ant-pagination-options-quick-jumper input': {
              border: 'none !important',
              background: '#F2F3F5 !important',
              borderRadius: '2px !important'
            }
          }}
        >
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={studentSubmissionData?.total || 0}
            showSizeChanger
            showQuickJumper
            pageSizeOptions={['10', '20', '50', '100']}
            onChange={(page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            }}
            onShowSizeChange={(_, size) => {
              setPageSize(size);
              setCurrentPage(1); // 改变每页条数时回到第一页
            }}
          />
        </Flex>
      </Box>
    </Box>
  );
}

export default StudentSubmissionDetails;
