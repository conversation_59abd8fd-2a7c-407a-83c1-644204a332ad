import React from 'react';
import { Box } from '@chakra-ui/react';
import { Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import { vwDims } from '@/utils/chakra';
import { Department, TmbUser } from '@/types/api/tenant/teamManagement/teach';
import { TableRowData, HoveredCellState } from '../types';
import { TABLE_CONFIG } from '../constants';
import TeacherCell from './TeacherCell';
import { findGradeNode } from '../hooks/useEditTeachers';
import { hasStageData, hasGradeData } from '../utils/dataUtils';

interface GradeTableProps {
  schools: Department[];
  title: string;
  subjects: string[];
  leaders: Record<string, TmbUser[]>; // 添加 leaders 参数
  treeData: Department[];
  hoveredCell: HoveredCellState | null;
  highlightedTmbIds: number[];
  onCellHover: (cellData: HoveredCellState | null) => void;
  onEditTeacher: (
    nodeId: string | null,
    grade: string,
    subject: string,
    role: 'leader' | 'phase_leader' | 'teacher',
    stage?: string
  ) => void;
}

/**
 * 年级表格组件
 */
const GradeTable: React.FC<GradeTableProps> = ({
  schools,
  title,
  subjects,
  leaders,
  treeData, // 注意：此参数现在不再使用，直接使用 schoolData 中的字段
  hoveredCell,
  highlightedTmbIds,
  onCellHover,
  onEditTeacher
}) => {
  if (!schools || schools.length === 0) {
    return null;
  }

  // 从学校数据中提取年级数据
  const gradeMap: Record<string, Department> = {};
  schools.forEach((school) => {
    // 处理第二层的children，即年级数据
    school.children?.forEach((grade) => {
      if (grade.deptName) {
        gradeMap[grade.deptName] = grade;
      }
    });
  });

  const grades = Object.keys(gradeMap);
  if (grades.length === 0) {
    return null;
  }

  // 创建表格数据
  const tableData: TableRowData[] = [];

  // 1. 添加学段负责人行 - 来自接口第二层的数据
  // 如果有多个学校，取第一个作为学段负责人数据
  // if (schools.length > 0) {
  //   const school = schools[0];
  tableData.push({
    key: `${title}-leader`,
    gradeName: '学段负责人',
    isLeader: true
    // schoolData: school
  });
  // }

  // 2. 添加年级数据行
  grades.sort().forEach((grade) => {
    tableData.push({
      key: grade,
      gradeName: grade
    });
  });

  // 创建表格列定义
  const columns: ColumnType<TableRowData>[] = [
    {
      title: <div style={{ textAlign: 'center' }}>年级/学科</div>,
      dataIndex: 'gradeName',
      key: 'gradeName',
      width: vwDims(TABLE_CONFIG.FIXED_COLUMN_WIDTH),
      fixed: 'left',
      align: 'center' as const,
      onCell: () => ({
        style: {
          // backgroundColor: record.isLeader ? '#f0f8ff' : '#f9f9f9'
        }
      }),
      render: (gradeName) => (
        <Box display="flex" alignItems="center" justifyContent="center">
          {gradeName}
        </Box>
      )
    },
    // 使用不排序的科目列表，与总负责人表格保持一致
    ...subjects.map((subject) => ({
      title: <div style={{ textAlign: 'center' }}>{subject}</div>,
      dataIndex: subject,
      key: subject,
      width: TABLE_CONFIG.COLUMN_WIDTH,
      align: 'center' as const,
      onCell: (record: TableRowData) => {
        let cellId = '';
        let teacherInfo: any[] = [];
        let nodeId: string | null = null;
        let role: 'leader' | 'phase_leader' | 'teacher' = 'teacher';
        let canEdit = true;

        const subjectNode = treeData.find((item) => item.subjectName === subject);
        const grade = record.gradeName;
        const gradeData = findGradeNode(subjectNode, title, grade);

        if (record.isLeader) {
          // 学段负责人：检查该学科在该学段是否存在数据
          teacherInfo = leaders[subject] || [];
          nodeId = subjectNode ? subjectNode.deptId : null;
          cellId = `${nodeId}-${subject}`;
          role = 'phase_leader';
          canEdit = hasStageData(treeData, subject, title);
        } else {
          // 年级教师：检查该学科在该学段的该年级是否存在数据
          teacherInfo = gradeData?.tmbUserList || [];
          cellId = `${gradeData?.deptId || ''}-${subject}`;
          nodeId = gradeData?.deptId || null;
          role = 'teacher';
          canEdit = hasGradeData(treeData, subject, title, grade);
        }

        return {
          onMouseEnter: canEdit ? () => onCellHover({ id: cellId, users: teacherInfo }) : undefined,
          onMouseLeave: canEdit ? () => onCellHover(null) : undefined,
          onClick: canEdit
            ? () => {
                onEditTeacher(nodeId, grade, subject, role, title);
              }
            : undefined,
          style: { cursor: canEdit ? 'pointer' : 'default' }
        };
      },
      render: (_: unknown, record: TableRowData) => {
        let cellId = '';
        let teacherInfo: any[] = [];
        let nodeId: string | null = null;
        let canEdit = true;

        const subjectNode = treeData.find((item) => item.subjectName === subject);
        const grade = record.gradeName;
        const gradeData = findGradeNode(subjectNode, title, grade);

        if (record.isLeader) {
          // 学段负责人：检查该学科在该学段是否存在数据
          teacherInfo = leaders[subject] || [];
          nodeId = subjectNode ? subjectNode.deptId : null;
          cellId = `${nodeId}-${subject}`;
          canEdit = hasStageData(treeData, subject, title);
        } else {
          // 年级教师：检查该学科在该学段的该年级是否存在数据
          teacherInfo = gradeData?.tmbUserList || [];
          cellId = `${gradeData?.deptId || ''}-${subject}`;
          nodeId = gradeData?.deptId || null;
          canEdit = hasGradeData(treeData, subject, title, grade);
        }

        return (
          <TeacherCell
            cellId={cellId}
            teacherInfo={teacherInfo}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            canEdit={canEdit}
          />
        );
      }
    }))
  ];

  return (
    <Table
      title={() => title}
      columns={columns}
      dataSource={tableData}
      pagination={false}
      bordered
      scroll={{ x: 'max-content' }}
      rowKey="key"
      style={{ marginBottom: 16 }}
    />
  );
};

export default GradeTable;
