import { TaskCorrectionStatus } from '@/constants/ai/correction';
import { FileMetaType } from '../../file';

export type TaskInfoType = {
  id: string;
  status: TaskCorrectionStatus;
  createTime: string;
  grade: string;
  gradeId: number;
  gradeName: string;
  isDeleted: number;
  standardContent: string;
  standardFile: FileMetaType;
  standardFileKey: string;
  subject: string;
  subjectId: number;
  subjectName: string;
  tenantId: number;
  titleContent: string;
  titleFile: FileMetaType;
  titleFileKey: string;
  tmbId: number;
  updateTime: string;
};

export interface CompositionData {
  id?: number;
  gradeId?: number | '';
  gradeName?: string;
  standardContent?: string;
  subjectId?: number | '';
  standardFileKey?: string;
  subjectName?: string;
  titleContent?: string;
  titleFileKey?: string;
  titleFileUrl?: string;
  titleFileSize?: string;
  titleFileName?: string;
  standardFileUrl?: string;
  standardFileSize?: string;
  standardFileName?: string;
  standardFileKey?: string;
  standardFileSize?: string;
  standardFileName?: string;
  standardFileUrl?: string;
  titleInputContent?: string;
  standardInputContent?: string;
  title?: string;
  standard?: string;
}

export interface LatestComposition {
  createTime: string;
  grade: string;
  gradeId: number;
  id: number;
  isDeleted: number;
  standardContent: string;
  standardFileKey: string;
  status: number;
  subject: string;
  subjectId: number;
  tenantId: number;
  titleContent: string;
  titleFileKey: string;
  tmbId: number;
  titleFileUrl: string;
  titleFileSize: string;
  titleFileName: string;
  standardFileUrl: string;
  standardFileSize: string;
  standardFileName: string;
  standardFileKey: string;
  standardFileSize: string;
  standardFileName: string;
  standardFileUrl: string;
  updateTime: string;
  titleFile: FileMetaType;
  standardFile: FileMetaType;
  temporaryStorage: string;
}
export interface StudentListType {
  name: string;
  id?: string;
  studentName: string;
  sort?: number;
}

export interface CreateStudentListType {
  name: string;
  compositionRuleId: string;
  sort?: number;
  // studentId: string;
}

export interface UpdateStudentListType {
  id: string;
  name: string;
  sort?: number;
}

export interface ParsedFileType {
  fileKey: string;
  parseResult: string;
}

export interface CreateCompositionInfoType {
  compositionRuleId: number;
  content: string;
  fileKeys: string;
  inputMethod: number;
  score: number;
  status: number;
  studentId?: number;
  studentName: string;
}

export interface TempListItem {
  compositionFiles: ICompositionFiles[];
  compositionRuleId: number;
  content: string;
  createTime: string;
  fileKeys: string;
  id: number;
  inputMethod: number;
  isDeleted: number;
  score: number;
  status: number;
  studentId: number;
  studentName: string;
  tenantId: number;
  tmbId: number;
  updateTime: string;
}

export interface ICompositionFiles {
  createTime?: string;
  fileContent?: string;
  fileKey: string;
  fileKeys?: string;
  fileName: string;
  fileParseStatus?: number;
  fileSize?: number;
  fileSuffix?: string;
  fileUrl: string;
  id?: number;
  isDeleted?: number;
  status?: number;
  tenantId?: string;
  tmbId?: string;
  updateTime?: string;
  compositionId?: number;
}

export interface UpdateCompositionInfoType {
  id?: number;
  content?: string;
  fileKeys?: string;
  studentId?: number;
  studentName?: string;
  score?: string;
}

export interface CorrectionResultType {
  compositionContent: string;
  compositionFiles: ICompositionFiles[];
  compositionId: number;
  compositionRuleId: number;
  correctionResultFileKey: string;
  createTime: string;
  errorAndSuggestion: string;
  fullScore: number;
  grade: string;
  id: number;
  isDeleted: number;
  originalHighlights: string;
  overallComment: string;
  paragraphSuggestions: string;
  polishedText: string;
  score: number;
  scoreText: string;
  subject: string;
  tenantId: number;
  tmbId: number;
  updateTime: string;
  vocabSuggest: string;
}

export interface PageListType {
  gradeName: string;
  subjectName: string;
  compositionCount: number;
  createTime: string;
  grade: string;
  gradeId: number;
  id: number;
  isDeleted: number;
  standardContent: string;
  standardFileKey: string;
  status: number;
  subject: string;
  subjectId: number;
  tenantId: number;
  titleContent: string;
  titleFileKey: string;
  tmbId: number;
  updateTime: string;
  standardFile?: FileMetaType;
  titleFile?: FileMetaType;
}

export interface PageListResponseType {
  records: PageListType[];
  searchCount: boolean;
  size: number;
  total: number;
}

export interface FileKeys {
  fileKeys: string;
}

export interface Student {
  name: string;
}

export interface InfoList {
  content?: string;
  students: Student;
  filesList?: FileKeys[];
}

export interface BatchParams {
  compositionRuleId: string;
  infoList: InfoList[];
}

export interface File {
  id: string | null;
  createTime: string;
  updateTime: string;
  isDeleted: number | null;
  tenantId: string | null;
  tmbId: string | null;
  fileName: string;
  fileKey: string;
  fileUrl: string;
  fileSuffix: string;
  fileSize: number | null;
  fileContent: string;
  status: number;
  fileParseStatus: number | null;
}

export interface StudentInfo {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number | null;
  tenantId: string;
  tmbId: string;
  name: string;
  sort: number | null;
}

export interface InfoList {
  students: StudentInfo;
  filesList?: File[];
}

export interface BatchCreateAndUploadResponse
  extends Array<{
    infoList: {
      content?: string;
      students: {
        id: string;
        createTime: string;
        updateTime: string;
        isDeleted: number | null;
        tenantId: string;
        tmbId: string;
        name: string;
        sort: number | null;
      };
      filesList?: {
        id: string | null;
        createTime: string;
        updateTime: string;
        isDeleted: number | null;
        tenantId: string | null;
        tmbId: string | null;
        fileName: string;
        fileKey: string;
        fileUrl: string;
        fileSuffix: string;
        fileSize: number | null;
        fileContent: string;
        status: number;
        fileParseStatus: number | null;
      }[];
    }[];
  }> {}
