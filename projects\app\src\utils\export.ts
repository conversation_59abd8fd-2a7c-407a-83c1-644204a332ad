/**
 * 将HTML中的LaTeX公式图片转换为LaTeX文本格式
 * @param html HTML字符串
 * @returns 转换后的HTML字符串
 */
export const convertLatexImgToFormula = (html: string): string => {
  console.log(html, 'html');

  // 创建一个临时的DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // 查找所有img标签
  const images = tempDiv.getElementsByTagName('img');

  // 从后向前遍历，这样替换时不会影响数组长度
  for (let i = images.length - 1; i >= 0; i--) {
    const img = images[i];
    let formula = '';

    // 首先尝试从 data-formula-image 属性获取公式
    const formulaAttr = img.getAttribute('data-formula-image');

    if (img.src.includes('latexeasy.com')) {
      const srcUrl = new URL(img.src);
      const queryFormula = srcUrl.search.substring(1); // 去掉问号
      if (queryFormula) {
        formula = decodeURIComponent(queryFormula);
      }
    }

    // 如果成功获取到公式，进行替换
    if (formula) {
      // 创建新的文本节点
      const hasStyle = img.hasAttribute('style');
      const latexFormula = hasStyle ? `$$${formula}$$` : `$${formula}$`;
      const textNode = document.createTextNode(latexFormula);

      // 替换img标签
      img.parentNode?.replaceChild(textNode, img);
    }
  }

  // 返回转换后的HTML
  return tempDiv.innerHTML;
};

/**
 * 示例用法：
 * const html = `<p>这是一个公式：<img src="https://r.latexeasy.com/image.svg?..." data-formula-image="%5Cfrac%7B1%7D%7Bx%7D" /></p>`;
 * const converted = convertLatexImgToFormula(html);
 * // 输出：<p>这是一个公式：$ \frac{1}{x} $</p>
 */

// 处理公式
export const handleFormula = (text: string) => {
  if (!text) return text;

  // 预处理: 标记Markdown换行（两个空格后跟换行符）
  let processedText = text.replace(/  \n/g, '||MARKDOWN_LINE_BREAK||');

  // 处理双美元符号包裹的公式（块级公式）
  // 使用 [\s\S] 代替 . 来匹配所有字符（包括换行符）
  processedText = processedText.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
    // 去除公式中可能的前后空格
    const trimmedFormula = formula.trim();
    // 对公式进行URL编码
    const encodedFormula = encodeURIComponent(trimmedFormula);
    // 返回替换的HTML
    return `<img src="https://r.latexeasy.com/image.svg?${encodedFormula}" data-formula-image="${encodedFormula}" style="display: block;"/>`;
  });

  // 处理单美元符号包裹的公式（行内公式）
  // 使用否定前瞻和否定后顾确保不匹配到双美元符号的情况
  processedText = processedText.replace(
    /(?<!\$)\$(?!\$)([\s\S]*?)(?<!\$)\$(?!\$)/g,
    (match, formula) => {
      // 去除公式中可能的前后空格
      const trimmedFormula = formula.trim();
      // 对公式进行URL编码
      const encodedFormula = encodeURIComponent(trimmedFormula);
      // 返回替换的HTML
      return `<img src="https://r.latexeasy.com/image.svg?${encodedFormula}" data-formula-image="${encodedFormula}"/>`;
    }
  );

  // 还原Markdown换行为HTML换行标签
  processedText = processedText.replace(/\|\|MARKDOWN_LINE_BREAK\|\|/g, '<br/>');

  return processedText;
};

// 去掉所有height相关属性
export const removeHeight = (html: string) => {
  console.log('处理前:', html);
  const result = html
    .replace(/height="[^"]*"/g, '')
    .replace(/height='[^']*'/g, '')
    .replace(/height:\s*[^;}"']+[;}"']/g, '')
    .replace(/\s+style="\s*"/g, '')
    .replace(/\s+style='\s*'/g, '');
  console.log('处理后:', result);
  return result;
};
