import React, { useEffect, useState } from 'react';
import { TreeSelect } from 'antd';
import { Controller } from 'react-hook-form';
import styles from '../../student.module.scss';

type TreeItem = {
  key?: number | string;
  title?: string;
  value?: number | string;
  parentKey?: number | string;
  children?: TreeItem[];
  isLeaf?: boolean;
  path?: string[];
  selectable?: boolean;
  disabled?: boolean;
  parentValues?: (string | number)[]; // 添加这一行
};

interface SchoolStructureSelectProps {
  control: any;
  name: string;
  treeData: TreeItem[];
  placeholder: string;
  mode: string;
}

const SchoolStructureSelect: React.FC<SchoolStructureSelectProps> = ({
  control,
  name,
  treeData,
  placeholder,
  mode
}) => {
  const [processedTreeData, setProcessedTreeData] = useState<TreeItem[]>([]);
  const [treeDataMap, setTreeDataMap] = useState<Record<string | number, TreeItem>>({});

  const processTreeData = (
    tree: TreeItem[],
    level: number = 0,
    parentPath: string[] = [],
    parentValues: (string | number)[] = []
  ): TreeItem[] => {
    return tree.map((node) => {
      const currentPath = [...parentPath, node.title || ''];
      const currentValues = [...parentValues, node.value || ''];
      const newNode: TreeItem = {
        ...node,
        path: currentPath,
        disabled: level < 2,
        selectable: level === 2,
        isLeaf: level === 2 || !node.children || node.children.length === 0,
        parentValues: currentValues
      };
      if (newNode.children) {
        newNode.children = processTreeData(newNode.children, level + 1, currentPath, currentValues);
      }
      return newNode;
    });
  };

  const treeToMap = (tree: TreeItem[]): Record<string | number, TreeItem> => {
    const map: Record<string | number, TreeItem> = {};
    const traverse = (nodes: TreeItem[]) => {
      nodes.forEach((node) => {
        if (node.value !== undefined) {
          map[node.value] = node;
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(tree);
    return map;
  };

  useEffect(() => {
    if (treeData) {
      const processed = processTreeData(treeData);
      setProcessedTreeData(processed);
      setTreeDataMap(treeToMap(processed));
    }
  }, [treeData]);

  const getFullPath = (value: string | number): string[] => {
    return treeDataMap[value]?.path || [];
  };

  const getParentValues = (value: string | number): (string | number)[] => {
    return treeDataMap[value]?.parentValues || [];
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <TreeSelect
          {...field}
          className={styles['custom-cascader']}
          treeData={processedTreeData}
          treeDefaultExpandAll
          placeholder={placeholder}
          style={{
            width: '328px',
            height: '38px',
            backgroundColor: '#F6F6F6',
            border: 'none',
            borderRadius: '8px'
          }}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto', zIndex: 9999 }}
          disabled={mode === 'view'}
          treeNodeFilterProp="title"
          showSearch
          filterTreeNode={(inputValue, treeNode) => {
            return (treeNode.title as string).toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
          }}
          labelInValue
          treeNodeLabelProp="path"
          onChange={(value, label, extra) => {
            const parentValues = getParentValues(value.value);
            field.onChange({
              stageId: parentValues[0] || '',
              gradeId: parentValues[1] || '',
              clazzId: value.value
            });
          }}
          value={
            field.value
              ? { value: field.value.clazzId, label: getFullPath(field.value.clazzId).join(' / ') }
              : undefined
          }
        />
      )}
    />
  );
};

export default SchoolStructureSelect;
