/**
 * 填空题相关工具函数
 */

/**
 * 将填空题中的HTML标签解析为全角下划线用于显示
 * @param content 包含HTML标签的内容
 * @returns 替换后的内容
 */
export const parseFillInBlankForDisplay = (content: string): string => {
  if (!content) return '';

  // 将 <!--BA--><div class="quizPutTag" contenteditable="true"></div><!--EA--> 替换为全角下划线
  return content.replace(
    /<!--BA--><div class="quizPutTag" contenteditable="true"><\/div><!--EA-->/g,
    '＿＿＿＿＿'
  );
};

/**
 * 将编辑器中的空格标记转换为HTML标签用于提交
 * @param content 包含空格标记的内容
 * @returns 替换后的内容
 */
export const convertFillInBlankForSubmit = (content: string): string => {
  if (!content) return '';

  // 将 （  1  ）（  2  ）（  3  ） 等格式替换为HTML标签
  // 匹配全角括号和数字，中间可能有空格
  return content.replace(
    /（\s*(\d+)\s*）/g,
    '<!--BA--><div class="quizPutTag" contenteditable="true"></div><!--EA-->'
  );
};

/**
 * 统计填空题中的空格数量
 * @param content 题目内容
 * @returns 空格数量
 */
export const countFillInBlanks = (content: string): number => {
  if (!content) return 0;

  // 统计HTML标签的数量
  const htmlTagMatches = content.match(
    /<!--BA--><div class="quizPutTag" contenteditable="true"><\/div><!--EA-->/g
  );
  if (htmlTagMatches) {
    return htmlTagMatches.length;
  }

  // 统计 （  1  ）（  2  ）（  3  ） 格式的数量
  const numberMatches = content.match(/（\s*(\d+)\s*）/g);
  return numberMatches ? numberMatches.length : 0;
};
