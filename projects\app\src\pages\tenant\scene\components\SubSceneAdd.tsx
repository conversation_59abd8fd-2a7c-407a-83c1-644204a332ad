import React, { useEffect, useState, useImperativeHandle, forwardRef, Ref } from 'react';
import {
  Box,
  Button,
  ModalFooter,
  ModalBody,
  Input,
  Flex,
  useTheme,
  useDisclosure
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { createSubScene, updateSubScene } from '@/api/tenant/scene';
import { SubSceneCreateParams } from '@/types/api/tenant/scene';
import { useRouter } from 'next/router';

export interface SubSceneAddRef {
  openModal: (modalInfo: {
    records?: SubSceneCreateParams & { id: string };
    formStatus: 'view' | 'add' | 'edit';
  }) => void;
  closeModal: () => void;
}
const itemDefaultValues = {
  createUsername: '',
  name: '',
  sort: 0,
  tenantId: 0,
  tenantSceneId: 0
};
const SubSceneAdd = (
  {
    onSuccess,
    sceneId
  }: { onSuccess: () => void; sceneId: string; defaultValues?: SubSceneCreateParams },
  ref: Ref<SubSceneAddRef>
) => {
  const router = useRouter();
  const [isAdd, setIsAdd] = useState(false);
  const [defaultValues, setDefaultValues] = useState({ ...itemDefaultValues, id: '' });
  const { t } = useTranslation();
  const theme = useTheme();

  const { register, handleSubmit, setValue } = useForm<SubSceneCreateParams>({
    defaultValues: itemDefaultValues
  });

  const {
    isOpen: isOpenSubSceneAdd,
    onOpen: onOpenSubSceneAdd,
    onClose: onCloseSubSceneAdd
  } = useDisclosure();

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: SubSceneCreateParams) => {
      const formData: any = {
        ...data,
        tenantSceneId: sceneId
      };
      const appId = isAdd ? undefined : { id: defaultValues.id };
      return isAdd ? createSubScene(formData) : updateSubScene({ ...formData, ...appId });
    },
    onSuccess() {
      onSuccess();
      onCloseSubSceneAdd();
    },
    successToast: isAdd ? t('common.Create Success') : t('common.Update Success')
  });

  useImperativeHandle(ref, () => ({
    openModal: (modalInfo: {
      records?: SubSceneCreateParams & { id: string };
      formStatus: 'view' | 'add' | 'edit';
    }) => {
      const { records, formStatus } = modalInfo;
      if (records) {
        setDefaultValues(records);
        Object.keys(records).forEach((key) => {
          setValue(key as keyof SubSceneCreateParams, records[key as keyof SubSceneCreateParams]);
        });
      } else {
        Object.keys(itemDefaultValues).forEach((key) => {
          setValue(
            key as keyof SubSceneCreateParams,
            itemDefaultValues[key as keyof SubSceneCreateParams]
          );
        });
      }
      setIsAdd(formStatus === 'add');

      onOpenSubSceneAdd();
    },
    closeModal: () => {
      onCloseSubSceneAdd();
    }
  }));

  return isOpenSubSceneAdd ? (
    <MyModal
      title={isAdd ? t('新增子场景') : t('编辑子场景')}
      isOpen
      onClose={onCloseSubSceneAdd}
      isCentered
    >
      <ModalBody>
        <Box color={'myGray.800'} fontWeight={'bold'}>
          {t('子场景名称')}
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <Input
            flex={1}
            autoFocus
            placeholder="请输子场景名称"
            bg={'myWhite.600'}
            {...register('name', {
              required: t('core.app.error.App name can not be empty')
            })}
          />
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onCloseSubSceneAdd}>
          {t('取消')}
        </Button>
        <Button onClick={handleSubmit((data) => onClickConfirm(data))} isLoading={creating}>
          {isAdd ? t('添加') : t('确认')}
        </Button>
      </ModalFooter>
    </MyModal>
  ) : (
    <></>
  );
};

export default forwardRef<
  SubSceneAddRef,
  { onSuccess: () => void; sceneId: string; defaultValues?: SubSceneCreateParams }
>(SubSceneAdd);
