/** 资源广场首页请求参数 */
export interface ResourceSquareParams {
  source: number[];
}

/** AI推荐请求参数 */
export interface AIRecommendParams {
  pageNum: number;
  pageSize: number;
}

/** AI推荐响应数据 */
export interface AIRecommendResponse {
  data: ResourceSquareItem[];
}

/** 资源项详细信息 */
export interface ResourceSquareItem {
  id: number;
  title: string;
  description: string;
  resourceTypeId: number;
  resourceTypeName: string;
  fileFormatId: number;
  fileFormatName: string;
  subjectId: number;
  subjectName: string;
  gradeId: number;
  gradeName: string;
  stageId: number;
  stageName: string;
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  textbookChapterIds: number[];
  textbookChapterNames: string[];
  areaId: number;
  areaCode: string;
  areaName: string;
  createUserName: string;
  attribution: number;
  attributionToId: number;
  vintages: number;
  shareStatus: number;
  downloadCount: number;
  viewCount: number;
  curation: number;
  fileKey: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileJson: string;
  cutQuestion: number;
  status: number;
  createTime: string;
  isCollected: number;
  isOwned: number;
  isOperable: number;
  knowledgePointIds: number[];
  knowledgePointNames: string[];
  customTags: string[];
  updateTime?: string;
  hotScore?: number;
  recommendType?: number;
  videoId?: string;
}

/** 资源分类项 */
export interface ResourceCategoryItem {
  id: number;
  name: string;
  code: string;
  parentId: number;
  collection: number;
  homework: number;
  indexCount: number;
  sortOrder: number;
  children: ResourceCategoryItem[];
  resources?: ResourceSquareItem[];
}

/** 资源广场首页响应 */
export interface ResourceSquareResponse {
  data: ResourceCategoryItem[];
}
/** 教材树节点请求参数 */
export interface TextbookTreeParams {
  stageId: number;
  gradeId?: number;
  subjectId: number;
  type?: number; //0显示完整树, 1树只有两级
}
/** 教材树节点 */
export interface TextbookTreeItem {
  id: number;
  name: string;
  type: string;
  children: TextbookTreeItem[];
}

/** 教材树请求参数 */
export interface TextbookTreeParams {
  stageId: number;
  subjectId: number;
}
// AI搜索参数
export interface AISearchParams {
  attributionIds?: number[]; //来源id 0全部 3官方资源 2校本资源 1个人资源
  resourceTypeId?: number; // 资源类型ID
  keyWord?: string; // 搜索关键词
  stageId?: number; // 学段ID
  subjectId?: number; // 学科ID
  gradeId?: number; // 年级ID
  areaId?: number; // 地区ID
  fileTypeId?: number; // 文件格式ID
  year?: number; // 年份
  pageNum: number; // 页码
  pageSize: number; // 每页条数
  aiResourceIds?: number[]; // AI资源ID
  regionId?: number;
  textbookChapterId?: number;
  textbookVersionId?: number;
  textbookVolumeId?: number;
}

// AI搜索响应
export interface AISearchResponse {
  normalSearchResults: ResourceSquareItem[];
  aiResourceIds: number[];
  aiSearchResults: ResourceSquareItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/** 学科信息 */
export interface SubjectInfo {
  subjectId: number;
  subjectName: string;
  type: number;
}

/** 学段学科树节点 */
export interface StageSubjectTreeItem {
  stageId: number;
  stageName: string;
  stageCode: string;
  sortOrder: number;
  subjects: SubjectInfo[];
}

/** 学段学科树响应 */
export type StageSubjectTreeResponse = StageSubjectTreeItem[];

/** 文件解压响应项 */
export interface FileDecompressionItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: null | number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
}

/** 文件解压响应 */
export type FileDecompressionResponse = FileDecompressionItem[];

/** 热搜词汇项 */
export interface HotSearchItem {
  id: number;
  searchInfo: string;
  createTime: string;
}

/** 热搜词汇响应 */
export type HotSearchResponse = HotSearchItem[];

/** 资源详情推荐请求**/
export interface RecommendDetailParams {
  gradeId: number;
  resourceTypeId: number;
  subjectId: number;
}

/** 资源详情推荐响应**/
export type RecommendDetailResponse = RecommendDetailItem[];

export interface RecommendDetailItem {
  id: number;
  title: string;
  source: string;
  updateTime: string;
}

/** 批量收藏请求**/
export interface BatchCollectParams {
  resourceIds: number[];
}

/** 教材章节请求**/
export interface TextbookChaptersTreeParams {
  textbookVolumeId: number;
}
/** 教材章节Item**/
export interface TextbookChaptersTreeItem {
  id: number;
  name: string;
  code: string;
  parentId: number;
  sortOrder: number;
  children: TextbookChaptersTreeItem[];
}
/** 教材章节响应**/
export type TextbookChaptersTreeResponse = TextbookChaptersTreeItem[];
