import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { Box, Button, Input, Text, useDisclosure } from '@chakra-ui/react';
import { SelectProps, Select } from 'antd';
import { useEffect, useState } from 'react';
import styled from '@emotion/styled';
import { useHomeworkStore } from '@/store/useHomework';
import QuestionSourceSelect from '@/components/QuestionSourceSelect';
import KnowledgePointModal from '@/components/PushingRules/KnowledgePointModal';
import ClassSelectionModal from '@/pages/homeworkManagement/assignmentRelease/components/ClassSelectionModal';
import { useForm } from 'react-hook-form';
import {
  KnowledgePoint,
  TextbookChapter,
  getKnowledgePointsTree,
  getTextbookChaptersTreeNew
} from '@/api/teaching';
import type { TreeDataNode } from 'antd';
const TopicSelect = styled.div`
  .ant-select-selector {
    background-color: #f9fafb !important;
    border-radius: 8px !important;
    border: none !important;
    height: ${vwDims(42)} !important;
    width: 100% !important;
    padding: 0 ${vwDims(12)} !important;
  }

  .ant-select-selection-overflow-item,
  .ant-select-selection-item {
    background-color: #fff !important;
  }

  .ant-select-selection-overflow-item {
    border-radius: 4px !important;
  }

  .ant-select-selection-overflow {
    gap: ${vwDims(6)} !important;
  }

  .ant-select-selection-overflow-item-suffix {
    display: none !important;
  }

  .ant-select-arrow,
  .ant-select-clear,
  .ant-select-selection-item-remove {
    display: none;
  }
`;
// 定义表单数据类型
interface FormData {
  pushingRules: string;
}

/**
 * @description 智慧作业 taskType=1
 * @returns
 */
function SmartHomework() {
  const { homeworkDetail } = useHomeworkStore();
  // 表单控制
  const { control } = useForm<FormData>({
    defaultValues: {
      pushingRules:
        homeworkDetail?.pushRule?.pushMode === 1
          ? '1'
          : homeworkDetail?.pushRule?.pushMode === 2
            ? '2'
            : '3'
    }
  });

  // 知识点选择弹窗状态
  const {
    isOpen: isKnowledgeModalOpen,
    onOpen: onKnowledgeModalOpen,
    onClose: onKnowledgeModalClose
  } = useDisclosure();

  // 班级选择弹窗状态
  const {
    isOpen: isClassModalOpen,
    onOpen: onClassModalOpen,
    onClose: onClassModalClose
  } = useDisclosure();

  // 班级选择相关状态
  const [selectedClasses, setSelectedClasses] = useState<any[]>([]);
  const [gradeId, setGradeId] = useState<string>('');
  const [gradeName, setGradeName] = useState<string>('');
  const [subjectId, setSubjectId] = useState<string>('');
  const [subjectName, setSubjectName] = useState<string>('');

  // 知识点相关状态
  const [selectedKnowledgePoints, setSelectedKnowledgePoints] = useState<KnowledgePoint[]>([]);
  const [selectedTextbookChapters, setSelectedTextbookChapters] = useState<TextbookChapter[]>([]);
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [knowledgeLoading, setKnowledgeLoading] = useState(false);
  const [chaptersLoading, setChaptersLoading] = useState(false);

  // 初始化已选择的知识点数据
  useEffect(() => {
    if (homeworkDetail?.pushRule?.pointNameList && homeworkDetail?.pushRule?.pointIdList) {
      const knowledgePoints: KnowledgePoint[] = homeworkDetail.pushRule.pointNameList.map(
        (name: string, index: number) => ({
          id: homeworkDetail.pushRule.pointIdList[index] || index,
          name: name,
          code: '', // 默认空字符串，实际使用时可能需要从API获取
          parentId: 0, // 默认值，实际使用时可能需要从API获取
          level: 1, // 默认值，实际使用时可能需要从API获取
          createTime: new Date().toISOString(), // 默认当前时间
          isDeleted: 0 // 默认未删除
        })
      );
      setSelectedKnowledgePoints(knowledgePoints);
    }
  }, [homeworkDetail?.pushRule?.pointNameList, homeworkDetail?.pushRule?.pointIdList]);

  // 题目来源
  const [vendorOptions, setVendorOptions] = useState<SelectProps['options']>([]);
  // 题目年份
  const [yearOptions, setYearOptions] = useState<SelectProps['options']>([]);
  // 题型 questionTypeList
  const [questionTypeOptions, setQuestionTypeOptions] = useState<SelectProps['options']>([]);

  // 获取知识点树形数据
  const fetchKnowledgePoints = async () => {
    // 从homeworkDetail的根级别获取stageId和subjectId
    if (!homeworkDetail?.stageId || !homeworkDetail?.subjectId) {
      console.warn('缺少学段ID或学科ID，无法获取知识点数据', {
        stageId: homeworkDetail?.stageId,
        subjectId: homeworkDetail?.subjectId
      });
      return;
    }

    try {
      setKnowledgeLoading(true);
      const response = await getKnowledgePointsTree({
        stageId: homeworkDetail.stageId,
        subjectId: homeworkDetail.subjectId
      });

      // 转换为树形数据格式
      const convertToTreeData = (points: KnowledgePoint[]): TreeDataNode[] => {
        return points.map((point) => ({
          title: point.name,
          key: point.id.toString(),
          children: point.children ? convertToTreeData(point.children) : undefined
        }));
      };

      const treeData = convertToTreeData(response || []);
      setTreeData(treeData);
    } catch (error) {
      console.error('获取知识点数据失败:', error);
    } finally {
      setKnowledgeLoading(false);
    }
  };

  // 同步已选择的知识点到树形组件的选中状态
  const syncCheckedKeysFromSelected = () => {
    const keys = selectedKnowledgePoints.map((point) => point.id.toString());
    setCheckedKeys(keys);
  };

  // 处理知识点弹窗打开
  const handleOpenKnowledgeModal = () => {
    console.log('打开知识点选择弹窗');
    // 获取知识点数据
    fetchKnowledgePoints();
    // 同步已选择的知识点状态
    syncCheckedKeysFromSelected();
    onKnowledgeModalOpen();
  };

  // 处理知识点弹窗关闭
  const handleCloseKnowledgeModal = () => {
    onKnowledgeModalClose();
  };

  // 处理知识点选择确认
  const handleConfirmSelection = () => {
    console.log('确认知识点选择');
    onKnowledgeModalClose();
  };

  // 处理知识点移除
  const handleRemoveKnowledgePoint = (point: KnowledgePoint) => {
    console.log('移除知识点:', point);
  };

  // 处理树节点选中
  const onCheck = (
    checkedKeysValue: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }
  ) => {
    const keys = Array.isArray(checkedKeysValue) ? checkedKeysValue : checkedKeysValue.checked;
    setCheckedKeys(keys);
  };

  // 处理树节点展开
  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 初始化班级选择数据
  useEffect(() => {
    if (homeworkDetail) {
      setGradeId(homeworkDetail.gradeId?.toString() || '');
      setGradeName(homeworkDetail.gradeName || '');
      setSubjectId(homeworkDetail.subjectId?.toString() || '');
      setSubjectName(homeworkDetail.subjectName || '');

      // 构造已选择的班级数据
      if (homeworkDetail.clazzList) {
        const initialClasses = homeworkDetail.clazzList.map((clazz: any) => ({
          id: clazz.clazzId?.toString() || clazz.id?.toString(),
          name: clazz.clazzName,
          studentCount: clazz.studentList?.length || 0,
          selectedCount: clazz.studentList?.length || 0,
          expanded: false,
          selected: true,
          students:
            clazz.studentList?.map((student: any) => ({
              id: student.studentCode,
              name: student.studentName,
              selected: true
            })) || [],
          loading: false,
          gradeId: clazz.gradeId || homeworkDetail.gradeId,
          gradeName: clazz.gradeName || homeworkDetail.gradeName,
          studentIds: clazz.studentList?.map((student: any) => student.studentCode) || []
        }));
        setSelectedClasses(initialClasses);
      }
    }
  }, [homeworkDetail]);

  // 处理班级查看按钮点击
  const handleClassView = () => {
    console.log('打开班级选择弹窗');
    onClassModalOpen();
  };

  // 处理班级选择弹窗关闭
  const handleCloseClassModal = () => {
    onClassModalClose();
  };

  // 处理班级选择确认
  const handleConfirmClassSelection = (selectedClassesData: any[]) => {
    console.log('班级选择确认:', selectedClassesData);
    setSelectedClasses(selectedClassesData);
    onClassModalClose();
  };

  useEffect(() => {
    const vendorIdList = homeworkDetail?.pushRule?.vendorIdList;
    const vendorNameList = homeworkDetail?.pushRule?.vendorNameList;
    const options = vendorIdList.map((item: any, index: number) => ({
      label: vendorNameList[index],
      value: item
    }));
    setVendorOptions(options);

    const yearList = homeworkDetail?.pushRule?.yearList;
    const yearOptions = yearList.map((item: any, index: number) => ({
      label: item,
      value: item
    }));
    setYearOptions(yearOptions);

    const questionTypeList = homeworkDetail?.pushRule?.questionTypeList;
    console.log('questionTypeList', questionTypeList);
    const questionTypeOptions = questionTypeList.map((item: any, index: number) => ({
      label: item?.questionTypeName,
      value: item?.questionTypeName
    }));
    setQuestionTypeOptions(questionTypeOptions);
  }, [homeworkDetail]);

  return (
    //作业范围-智慧作业
    <Box>
      {/* 任务 */}
      <Box mt={vwDims(40)} mb={vwDims(50)} h={vwDims(40)} display={'flex'} gap={vwDims(18)}>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              作业任务类型
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              智慧作业
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              批改模式
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              {homeworkDetail.correctMethod === 1 && 'AI批改'}
              {homeworkDetail.correctMethod === 2 && '手动批改'}
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              提交方式
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              {homeworkDetail.submitMethod === 1 && '学生在线答题'}
              {homeworkDetail.submitMethod === 2 && '学生平板拍照'}
              {homeworkDetail.submitMethod === 3 && '教室帮录'}
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
      </Box>

      {/* 作业范围 */}
      <Box>
        <Text
          fontSize={vwDims(20)}
          fontWeight="500"
          color="#000"
          mb={vwDims(32)}
          display="flex"
          alignItems="center"
          fontFamily="PingFang SC"
          fontStyle="normal"
          lineHeight={vwDims(22)}
        >
          <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
          作业范围
        </Text>

        {/* 作业名称 */}
        <Box mb={vwDims(20)}>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            作业名称
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.name}
          </Box>
        </Box>

        {/* 作业说明 */}
        <Box mb={vwDims(20)}>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            作业说明
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.description}
          </Box>
        </Box>

        {/* 预计完成时间 */}
        <Box>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            预计完成时间
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.expectFinishDuration}分钟
          </Box>
        </Box>
      </Box>

      {/* 班级范围 */}
      <Box mt={vwDims(50)}>
        <Text
          fontSize={vwDims(20)}
          fontWeight="500"
          color="#000"
          mb={vwDims(32)}
          display="flex"
          alignItems="center"
          fontFamily="PingFang SC"
          fontStyle="normal"
          lineHeight={vwDims(22)}
        >
          <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
          班级范围
        </Text>

        <Box display={'flex'} gap={vwDims(9)}>
          {homeworkDetail?.clazzList?.map((item: any) => (
            <>
              <Box
                display={'flex'}
                justifyContent={'space-between'}
                alignItems={'center'}
                w={vwDims(292)}
                h={vwDims(70)}
                bgColor={'#FBF9FF'}
                borderRadius={'14px'}
                padding={`${vwDims(10)} ${vwDims(14)} ${vwDims(10)} ${vwDims(11)}`}
              >
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  w={vwDims(50)}
                  h={vwDims(50)}
                  borderRadius={'12px'}
                  border={'1px solid #E5DBFF'}
                  bgColor={'#F2EDFF'}
                >
                  <SvgIcon name="classFolderStar" w={vwDims(30)} h={vwDims(30)} />
                </Box>

                <Box flexGrow={1} marginLeft={vwDims(12)}>
                  <Box color={'#000'} fontSize={vwDims(16)} fontWeight={500}>
                    {item?.gradeName}
                    {item?.clazzName}
                  </Box>
                  <Box
                    color={'#86909C'}
                    fontSize={vwDims(14)}
                    fontWeight={400}
                    whiteSpace={'nowrap'}
                  >
                    已选择
                    <Text display={'inline-block'} padding={`${vwDims(0)} ${vwDims(4)}`}>
                      {item?.studentIdList?.length}
                    </Text>
                    名学生
                  </Box>
                </Box>

                <Button
                  w={vwDims(72)}
                  h={vwDims(24)}
                  fontSize={vwDims(11)}
                  border={'1px solid #E5E7EB'}
                  bgColor={'#fff'}
                  color={'#1D2129'}
                  _hover={{}}
                  onClick={handleClassView}
                >
                  查看
                </Button>
              </Box>
            </>
          ))}
        </Box>
      </Box>

      {homeworkDetail?.pushRule?.pushMode === 1 && (
        <>
          {/* 推题规则-按知识点推题 */}
          <Box mt={vwDims(50)}>
            <Text
              fontSize={vwDims(20)}
              fontWeight="500"
              color="#000"
              mb={vwDims(32)}
              display="flex"
              alignItems="center"
              fontFamily="PingFang SC"
              fontStyle="normal"
              lineHeight={vwDims(22)}
            >
              <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
              推题规则
            </Text>

            {/* 推题规则 */}
            <Box mb={vwDims(20)}>
              <Box
                fontSize={vwDims(15)}
                fontWeight="500"
                color="#303133"
                mb={vwDims(8)}
                display={'flex'}
                alignItems={'center'}
              >
                <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                推题规则
              </Box>
              <Box
                position={'relative'}
                display={'flex'}
                alignItems={'center'}
                h={vwDims(42)}
                bgColor={'#F9FAFB'}
                borderRadius={'8px'}
                padding={`${vwDims(0)} ${vwDims(12)}`}
              >
                {(homeworkDetail?.pushRule?.pushMode as 1 | 2 | 3) === 1 && '按知识点推题'}
                {(homeworkDetail?.pushRule?.pushMode as 1 | 2 | 3) === 2 && '按章节推题'}
                {(homeworkDetail?.pushRule?.pushMode as 1 | 2 | 3) === 3 && '自定义作文'}
              </Box>
            </Box>

            {/* 学段和学科 */}
            <Box display={'flex'} gap={vwDims(26)} mb={vwDims(20)}>
              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  学段
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.stageName}
                </Box>
              </Box>

              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  学科
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.subjectName}
                </Box>
              </Box>
            </Box>

            {/* 知识点 */}
            <Box mb={vwDims(20)}>
              <Box
                fontSize={vwDims(15)}
                fontWeight="500"
                color="#303133"
                mb={vwDims(8)}
                display={'flex'}
                alignItems={'center'}
              >
                <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                知识点
              </Box>
              <Box display={'flex'}>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  w={vwDims(306)}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  已选择{homeworkDetail?.pushRule?.pointNameList?.length}个知识点
                </Box>
                <Button
                  w={vwDims(120)}
                  h={vwDims(42)}
                  fontSize={vwDims(14)}
                  borderRadius={'8px'}
                  border={'1px solid #7D4DFF'}
                  bgColor={'#fff'}
                  color={'#7D4DFF'}
                  ml={vwDims(20)}
                  _hover={{}}
                  onClick={handleOpenKnowledgeModal}
                >
                  <SvgIcon
                    name="knowledgePointSearch"
                    w={vwDims(14)}
                    h={vwDims(14)}
                    mr={vwDims(8)}
                  />
                  查看知识点
                </Button>
              </Box>
            </Box>

            {/* 题目来源和题目难度 */}
            <Box display={'flex'} gap={vwDims(26)} mb={vwDims(20)}>
              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  题目来源
                </Box>
                <QuestionSourceSelect
                  value={homeworkDetail?.pushRule?.origins
                    .split(',')
                    .concat(homeworkDetail?.pushRule?.vendorIdList.map((item) => item.toString()))}
                  onChange={(value) => {
                    // 这里可以添加更新逻辑，目前是只读模式
                    console.log('题目来源选择变化:', value);
                  }}
                  placeholder="请选择题库来源"
                  disabled={true} // 当前是只读模式
                  required={true}
                />
              </Box>

              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  题目难度
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.difficultyMode === 1 &&
                    `试题综合难度/${homeworkDetail?.pushRule?.difficultyName}`}
                  {homeworkDetail?.pushRule?.difficultyMode === 2 &&
                    `试题难度一致/${homeworkDetail?.pushRule?.difficultyName}`}
                </Box>
              </Box>
            </Box>

            {/* 题目年份 */}
            <Box mb={vwDims(20)}>
              <Box
                fontSize={vwDims(15)}
                fontWeight="500"
                color="#303133"
                mb={vwDims(8)}
                display={'flex'}
                alignItems={'center'}
              >
                <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                题目年份
              </Box>
              <TopicSelect>
                <Select
                  className="topic-select"
                  mode="multiple"
                  allowClear
                  placeholder="请选择题目年份"
                  value={homeworkDetail?.pushRule?.yearList}
                  style={{ width: '100%', pointerEvents: 'none' }}
                  options={yearOptions}
                  maxTagPlaceholder={(omittedValues) => `+ ${omittedValues.length}`}
                  maxTagCount={2}
                />
              </TopicSelect>
            </Box>

            {/* 题型 */}
            <Box>
              <Box
                fontSize={vwDims(15)}
                fontWeight="500"
                color="#303133"
                mb={vwDims(8)}
                display={'flex'}
                alignItems={'center'}
              >
                <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                题型
              </Box>
              <TopicSelect>
                <Select
                  className="topic-select"
                  mode="multiple"
                  allowClear
                  placeholder="请选择题型"
                  value={homeworkDetail?.pushRule?.questionTypeList?.map(
                    (item: any) => item?.questionTypeName
                  )}
                  style={{ width: '100%', pointerEvents: 'none' }}
                  options={questionTypeOptions}
                  maxTagPlaceholder={(omittedValues) => `+ ${omittedValues.length}`}
                  // maxTagCount={2}
                />
              </TopicSelect>
            </Box>
          </Box>
        </>
      )}

      {homeworkDetail?.pushRule?.pushMode === 2 && (
        <>
          {/* 推题规则-按章节推题 */}
          <Box mt={vwDims(50)}>
            <Text
              fontSize={vwDims(20)}
              fontWeight="500"
              color="#000"
              mb={vwDims(32)}
              display="flex"
              alignItems="center"
              fontFamily="PingFang SC"
              fontStyle="normal"
              lineHeight={vwDims(22)}
            >
              <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
              推题规则
            </Text>

            {/* 推题规则 */}
            <Box mb={vwDims(20)}>
              <Box
                fontSize={vwDims(15)}
                fontWeight="500"
                color="#303133"
                mb={vwDims(8)}
                display={'flex'}
                alignItems={'center'}
              >
                <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                推题规则
              </Box>
              <Box
                position={'relative'}
                display={'flex'}
                alignItems={'center'}
                h={vwDims(42)}
                bgColor={'#F9FAFB'}
                borderRadius={'8px'}
                padding={`${vwDims(0)} ${vwDims(12)}`}
              >
                按章节推题
              </Box>
            </Box>

            {/* 学段和学科 */}
            <Box display={'flex'} gap={vwDims(26)} mb={vwDims(20)}>
              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  学段
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.stageName}
                </Box>
              </Box>

              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  学科
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.subjectName}
                </Box>
              </Box>
            </Box>

            {/* 教材版本和知识点  */}
            <Box display={'flex'} gap={vwDims(26)} mb={vwDims(20)}>
              {/* 教材版本 */}
              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  教材版本
                </Box>
                <Box
                  position={'relative'}
                  display={'flex'}
                  alignItems={'center'}
                  h={vwDims(42)}
                  bgColor={'#F9FAFB'}
                  borderRadius={'8px'}
                  padding={`${vwDims(0)} ${vwDims(12)}`}
                >
                  {homeworkDetail?.pushRule?.textbookVersionName}
                </Box>
              </Box>

              {/* 知识点 */}
              <Box flex={1}>
                <Box
                  fontSize={vwDims(15)}
                  fontWeight="500"
                  color="#303133"
                  mb={vwDims(8)}
                  display={'flex'}
                  alignItems={'center'}
                >
                  <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
                  知识点
                </Box>
                <Box display={'flex'}>
                  <Box
                    position={'relative'}
                    display={'flex'}
                    alignItems={'center'}
                    w={vwDims(306)}
                    h={vwDims(42)}
                    bgColor={'#F9FAFB'}
                    borderRadius={'8px'}
                    padding={`${vwDims(0)} ${vwDims(12)}`}
                  >
                    已选择{homeworkDetail?.pushRule?.pointNameList?.length}个知识点
                  </Box>
                  <Button
                    w={vwDims(120)}
                    h={vwDims(42)}
                    fontSize={vwDims(14)}
                    borderRadius={'8px'}
                    border={'1px solid #7D4DFF'}
                    bgColor={'#fff'}
                    color={'#7D4DFF'}
                    ml={vwDims(20)}
                    _hover={{}}
                    onClick={handleOpenKnowledgeModal}
                  >
                    <SvgIcon
                      name="knowledgePointSearch"
                      w={vwDims(14)}
                      h={vwDims(14)}
                      mr={vwDims(8)}
                    />
                    查看知识点
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>
        </>
      )}

      {/* 知识点选择弹窗 */}
      <KnowledgePointModal
        isOpen={isKnowledgeModalOpen}
        onClose={handleCloseKnowledgeModal}
        control={control}
        treeData={treeData}
        checkedKeys={checkedKeys}
        expandedKeys={expandedKeys}
        onCheck={onCheck}
        onExpand={onExpand}
        selectedKnowledgePoints={selectedKnowledgePoints}
        selectedTextbookChapters={selectedTextbookChapters}
        onRemoveKnowledgePoint={handleRemoveKnowledgePoint}
        onConfirm={handleConfirmSelection}
        loading={knowledgeLoading}
        chaptersLoading={chaptersLoading}
        viewMode={true}
      />

      {/* 班级选择弹窗 */}
      <ClassSelectionModal
        isOpen={isClassModalOpen}
        onClose={handleCloseClassModal}
        onConfirm={handleConfirmClassSelection}
        gradeId={gradeId}
        gradeName={gradeName}
        subjectId={subjectId}
        subjectName={subjectName}
        initialSelectedClasses={selectedClasses}
        viewMode={true}
      />
    </Box>
  );
}

export default SmartHomework;
