import { serviceSideProps } from '@/utils/i18n';
import { Box } from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import TeacherTypeTabs from './components/TeacherTypeTabs';
import TeacherList from './components/TeacherList';
import TeachersAdd from './components/TeachersAdd';
import TeachersEditInformation from './components/TeachersEditInformation';
import { useDisclosure } from '@chakra-ui/react';
import { vwDims } from '@/utils/chakra';
import { getAccountList } from '@/api/teacher';
import type { TeacherAccountItem } from '@/types/api/teacher';
import PageContainer from '@/components/PageContainer';
import MyBox from '@/components/common/MyBox';
import ImportPanel from '@/components/ImportPanel';

const Teacher = () => {
  const [activeTab, setActiveTab] = useState<string>('1');
  const [refreshKey, setRefreshKey] = useState(0);
  // 刷新处理
  const handleRefresh = () => {
    console.log('=== TEACHER REFRESH TRIGGERED ===');
    setRefreshKey((k) => {
      const newKey = k + 1;
      console.log('=== TEACHER REFRESH KEY UPDATED ===', newKey);
      return newKey;
    });
  };
  const [searchParams, setSearchParams] = useState({});
  
  // 处理搜索参数变化
  const handleSearchParamsChange = (params: any) => {
    console.log('教师管理页面收到搜索参数:', params);
    setSearchParams(params);
  };

  // 新增：账号列表和弹窗控制
  const [accountList, setAccountList] = useState<TeacherAccountItem[]>([]);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // 上传成功处理
  const handleUploadSuccess = () => {
    console.log('=== TEACHER UPLOAD SUCCESS - REFRESHING LIST ===');
    handleRefresh();
  };

  useEffect(() => {
    getAccountList().then((res) => {
      setAccountList(res || []);
    });
  }, []);

  return (
    <PageContainer
      w={vwDims(1668)}
      h={vwDims(1048)}
      top={vwDims(14)}
      left={vwDims(236)}
      opacity={1}
      borderRadius={vwDims(16)}
      pageBgColor="#FFFFFF"
      boxShadow="0px 0px 18.7px 0px #0000000F"
      overflow="hidden"
      p={4}
    >
      {/* 页面标题 */}
      <MyBox
        w={vwDims(72)}
        h={vwDims(22)}
        position="absolute"
        top={vwDims(41)}
        left={vwDims(46)}
        opacity={1}
        fontFamily="PingFang SC"
        fontWeight={500}
        fontStyle="normal"
        fontSize={vwDims(18)}
        lineHeight={vwDims(22)}
        letterSpacing="0%"
        verticalAlign="middle"
        color="#000000"
        zIndex={20}
      >
        教师管理
      </MyBox>

      {/* 批量导入按钮 */}
      <MyBox
        position="absolute"
        top={vwDims(34)}
        left={vwDims(1242)}
        zIndex={20}
      >
        <ImportPanel
          type="teacher"
          typeName="教师"
          onUploadSuccess={handleUploadSuccess}
        />
      </MyBox>
      
      {/* 教师类型tabs */}
      <MyBox position="absolute" top={vwDims(0)} left={vwDims(0)} right={vwDims(46)} zIndex={10}>
        <TeacherTypeTabs
          onSearch={handleSearchParamsChange}
          onAddTeacher={onOpen}
          accountList={accountList}
          addModalOpen={isOpen}
          onAddModalClose={onClose}
        />
      </MyBox>

      {/* 教师列表 */}
      <MyBox
        position="absolute"
        top={vwDims(230)}
        left={vwDims(46)}
        right={vwDims(46)}
        bottom={vwDims(20)}
        overflow="hidden"
      >
        <TeacherList onRefresh={handleRefresh} searchParams={searchParams} refreshKey={refreshKey} />
      </MyBox>

      {/* 后续如需全局弹窗可在此处加 TeachersEditInformation 并传递 onSuccess=handleRefresh */}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Teacher;
