import { Box, But<PERSON>, Text, Tooltip } from '@chakra-ui/react';
import { SelectProps, Select } from 'antd';
import { useEffect } from 'react';
import styled from '@emotion/styled';
import AIPushTopicDescription from '@/pages/homeworkManagement/assignmentRelease/components/AIPushTopicDescription';
import { vwDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useHomeworkStore } from '@/store/useHomework';

/**
 * @description 自定义作业 taskType=4
 * @returns
 */
function CustomHomework() {
  const { homeworkDetail } = useHomeworkStore();

  return (
    <Box>
      {/* 任务 */}
      <Box mt={vwDims(40)} mb={vwDims(50)} h={vwDims(40)} display={'flex'} gap={vwDims(18)}>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              作业任务类型
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              自定义作业
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              批改模式
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              {homeworkDetail.correctMethod === 1 && 'AI批改'}
              {homeworkDetail.correctMethod === 2 && '手动批改'}
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
        <Box position="relative" w={vwDims(362.5)} h={vwDims(40)}>
          <Box position="absolute" top={vwDims(5)} left={vwDims(0)}>
            <Box
              w={vwDims(114)}
              h={vwDims(30)}
              position="absolute"
              zIndex={4}
              padding={`${vwDims(4)}`}
              fontSize={vwDims(14)}
              fontWeight={500}
              whiteSpace={'nowrap'}
              textAlign={'center'}
              color={'#fff'}
            >
              提交方式
            </Box>
            <SvgIcon
              name="workRectangle1"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              zIndex={3}
            />
            <SvgIcon
              name="workRectangle2"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(5)}
              zIndex={2}
            />
            <SvgIcon
              name="workRectangle3"
              w={vwDims(114)}
              h={vwDims(30)}
              position={'absolute'}
              left={vwDims(10)}
              zIndex={1}
            />
          </Box>
          <Box marginLeft={vwDims(45.5)} position="relative" h={vwDims(40)}>
            <Box
              display={'flex'}
              alignItems={'center'}
              position={'absolute'}
              zIndex={2}
              left={vwDims(100)}
              h={'100%'}
              fontSize={vwDims(16)}
              fontWeight={500}
            >
              {homeworkDetail.submitMethod === 1 && '学生在线答题'}
              {homeworkDetail.submitMethod === 2 && '学生平板拍照'}
              {homeworkDetail.submitMethod === 3 && '教室帮录'}
            </Box>
            <SvgIcon
              name="workRectangle4"
              w={vwDims(248.5)}
              h={'100%'}
              position={'absolute'}
              zIndex={0}
            />
          </Box>
        </Box>
      </Box>

      {/* 作业范围 */}
      <Box>
        <Text
          fontSize={vwDims(20)}
          fontWeight="500"
          color="#000"
          mb={vwDims(32)}
          display="flex"
          alignItems="center"
          fontFamily="PingFang SC"
          fontStyle="normal"
          lineHeight={vwDims(22)}
        >
          <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
          作业范围
        </Text>

        {/* 作业名称 */}
        <Box mb={vwDims(20)}>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            作业名称
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.name}
          </Box>
        </Box>

        {/* 作业范围 */}
        <Box mb={vwDims(20)}>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            作业范围
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.description}
          </Box>
        </Box>

        {/* 预计完成时间 */}
        <Box>
          <Box
            fontSize={vwDims(15)}
            fontWeight="500"
            color="#303133"
            mb={vwDims(8)}
            display={'flex'}
            alignItems={'center'}
          >
            <SvgIcon name="homeworkPrefix" w={vwDims(6)} h={vwDims(14)} mr={vwDims(4)} />
            预计完成时间
          </Box>
          <Box
            position={'relative'}
            display={'flex'}
            alignItems={'center'}
            h={vwDims(42)}
            bgColor={'#F9FAFB'}
            borderRadius={'8px'}
            padding={`${vwDims(0)} ${vwDims(12)}`}
          >
            {homeworkDetail?.expectFinishDuration}分钟
          </Box>
        </Box>
      </Box>

      {/* 班级范围 */}
      <Box mt={vwDims(50)}>
        <Text
          fontSize={vwDims(20)}
          fontWeight="500"
          color="#000"
          mb={vwDims(32)}
          display="flex"
          alignItems="center"
          fontFamily="PingFang SC"
          fontStyle="normal"
          lineHeight={vwDims(22)}
        >
          <SvgIcon name="titlepre" w={22} h={22} mr={vwDims(8)} />
          班级范围
        </Text>

        <Box display={'flex'} gap={vwDims(9)}>
          {homeworkDetail?.clazzList?.map((item: any) => (
            <>
              <Box
                display={'flex'}
                justifyContent={'space-between'}
                alignItems={'center'}
                w={vwDims(292)}
                h={vwDims(70)}
                bgColor={'#FBF9FF'}
                borderRadius={'14px'}
                padding={`${vwDims(10)} ${vwDims(14)} ${vwDims(10)} ${vwDims(11)}`}
              >
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  justifyContent={'center'}
                  w={vwDims(50)}
                  h={vwDims(50)}
                  borderRadius={'12px'}
                  border={'1px solid #E5DBFF'}
                  bgColor={'#F2EDFF'}
                >
                  <SvgIcon name="classFolderStar" w={vwDims(30)} h={vwDims(30)} />
                </Box>

                <Box flexGrow={1} marginLeft={vwDims(12)}>
                  <Box color={'#000'} fontSize={vwDims(16)} fontWeight={500}>
                    {item?.gradeName}
                    {item?.clazzName}
                  </Box>
                  <Box
                    color={'#86909C'}
                    fontSize={vwDims(14)}
                    fontWeight={400}
                    whiteSpace={'nowrap'}
                  >
                    已选择
                    <Text display={'inline-block'} padding={`${vwDims(0)} ${vwDims(4)}`}>
                      {item?.studentIdList?.length}
                    </Text>
                    名学生
                  </Box>
                </Box>

                <Button
                  w={vwDims(72)}
                  h={vwDims(24)}
                  fontSize={vwDims(11)}
                  border={'1px solid #E5E7EB'}
                  bgColor={'#fff'}
                  color={'#1D2129'}
                  _hover={{}}
                >
                  查看
                </Button>
              </Box>
            </>
          ))}
        </Box>
      </Box>
    </Box>
  );
}

export default CustomHomework;
