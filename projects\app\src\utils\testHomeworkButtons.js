/**
 * 作业按钮逻辑测试脚本
 * 在浏览器控制台中运行此脚本来快速测试按钮显示逻辑
 * 
 * 使用方法:
 * 1. 在浏览器中打开作业详情页面
 * 2. 打开开发者工具控制台
 * 3. 复制粘贴此脚本并运行
 */

// 模拟按钮配置函数（简化版）
function getStudentActionButtons(studentStatus, correctMethod, submitMethod) {
  const defaultConfig = {
    view: { show: false, text: '查看', type: 'secondary', action: 'view' },
    urge: { show: false, text: '催交', type: 'primary', action: 'urge' },
    helpRecord: { show: false, text: '帮录', type: 'primary', action: 'helpRecord' },
    manualCorrect: { show: false, text: '手动批改', type: 'primary', action: 'manualCorrect' },
    aiCorrect: { show: false, text: 'AI批改', type: 'correction', action: 'aiCorrect' },
    continueCorrect: { show: false, text: '继续批改', type: 'primary', action: 'continueCorrect' },
    confirm: { show: false, text: '确认', type: 'primary', action: 'confirm' },
    modify: { show: false, text: '修改', type: 'secondary', action: 'modify' },
    reCorrect: { show: false, text: '重新批改', type: 'primary', action: 'reCorrect' },
    export: { show: false, text: '导出', type: 'secondary', action: 'export' }
  };

  switch (studentStatus) {
    case 0: // 待提交
    case 1: // 未提交
    case 6: // 待订正
      // 同时显示催交和帮录按钮
      defaultConfig.urge.show = true;
      defaultConfig.helpRecord.show = true;
      break;

    case 2: // 待批改
      if (correctMethod === 1) {
        // AI批改模式 - 无操作按钮
        // 不显示任何按钮
      } else if (correctMethod === 2) {
        // 手动批改模式 - 显示批改和AI批改
        defaultConfig.manualCorrect.show = true;
        defaultConfig.aiCorrect.show = true;
      }
      break;

    case 3: // 批改中（AI）
      // AI批改中 - 无操作按钮
      break;

    case 4: // 待确认
      if (correctMethod === 1) {
        // AI批改模式 - 显示确认和重新批改
        defaultConfig.confirm.show = true;
        defaultConfig.reCorrect.show = true;
      } else if (correctMethod === 2) {
        // 手动批改模式 - 显示确认和AI批改
        defaultConfig.confirm.show = true;
        defaultConfig.aiCorrect.show = true;
      }
      break;

    case 8: // 批改中（手动）
      // 手动批改中 - 显示AI批改
      defaultConfig.aiCorrect.show = true;
      break;

    case 5: // 已完成
      // 已完成 - 只显示查看
      defaultConfig.view.show = true;
      break;

    case 7: // 批改失败
      // 批改失败状态在表格中未明确定义，保持与待订正相同的逻辑
      defaultConfig.urge.show = true;
      defaultConfig.helpRecord.show = true;
      break;
  }

  return defaultConfig;
}

// 状态映射
const statusMap = {
  0: '待提交',
  1: '未提交', 
  2: '待批改',
  3: 'AI批改中',
  4: '待确认',
  5: '已完成',
  6: '待订正',
  7: '批改失败',
  8: '手动批改中'
};

// 测试函数
function testHomeworkButtons() {
  console.log('🧪 开始测试作业按钮显示逻辑...\n');
  
  // 测试配置
  const testConfigs = [
    { correctMethod: 1, submitMethod: 1, desc: 'AI批改 + 学生在线答题' },
    { correctMethod: 1, submitMethod: 2, desc: 'AI批改 + 学生平板拍照' },
    { correctMethod: 1, submitMethod: 3, desc: 'AI批改 + 教师帮录' },
    { correctMethod: 2, submitMethod: 1, desc: '手动批改 + 学生在线答题' },
    { correctMethod: 2, submitMethod: 2, desc: '手动批改 + 学生平板拍照' },
    { correctMethod: 2, submitMethod: 3, desc: '手动批改 + 教师帮录' }
  ];
  
  testConfigs.forEach(config => {
    console.log(`\n📋 测试配置: ${config.desc}`);
    console.log('=' .repeat(50));
    
    // 测试所有状态
    Object.keys(statusMap).forEach(status => {
      const statusNum = parseInt(status);
      const buttonsConfig = getStudentActionButtons(
        statusNum,
        config.correctMethod,
        config.submitMethod
      );
      
      const visibleButtons = Object.values(buttonsConfig)
        .filter(btn => btn.show)
        .map(btn => btn.text);
      
      console.log(
        `状态${statusNum}(${statusMap[statusNum]}): `,
        visibleButtons.length > 0 ? visibleButtons.join(', ') : '无操作按钮'
      );
    });
  });
  
  console.log('\n✅ 测试完成！');
  console.log('\n💡 提示: 你可以调用以下函数进行自定义测试:');
  console.log('testSpecificCase(状态, 批改方式, 提交方式)');
  console.log('例如: testSpecificCase(2, 1, 1)');
}

// 测试特定情况
function testSpecificCase(status, correctMethod, submitMethod) {
  console.log(`\n🔍 测试特定情况:`);
  console.log(`状态: ${status}(${statusMap[status] || '未知'})`);
  console.log(`批改方式: ${correctMethod === 1 ? 'AI批改' : '手动批改'}`);
  console.log(`提交方式: ${submitMethod === 1 ? '学生在线答题' : submitMethod === 2 ? '学生平板拍照' : '教师帮录'}`);

  const buttonsConfig = getStudentActionButtons(status, correctMethod, submitMethod);
  const visibleButtons = Object.values(buttonsConfig).filter(btn => btn.show);
  
  console.log(`\n显示的按钮 (${visibleButtons.length}个):`);
  visibleButtons.forEach(btn => {
    console.log(`  ✅ ${btn.text} (${btn.type})`);
  });
  
  if (visibleButtons.length === 0) {
    console.log('  ❌ 无操作按钮');
  }
}

// 快速测试常见场景
function testCommonScenarios() {
  console.log('🎯 测试常见场景...\n');
  
  const scenarios = [
    { status: 0, correctMethod: 1, submitMethod: 1, desc: '学生待提交作业(在线答题)' },
    { status: 0, correctMethod: 1, submitMethod: 2, desc: '学生待提交作业(平板拍照)' },
    { status: 0, correctMethod: 1, submitMethod: 3, desc: '学生待提交作业(教师帮录)' },
    { status: 6, correctMethod: 1, submitMethod: 1, desc: '学生待订正作业(在线答题)' },
    { status: 2, correctMethod: 1, submitMethod: 1, desc: '作业待AI批改(无操作)' },
    { status: 2, correctMethod: 2, submitMethod: 1, desc: '作业待手动批改' },
    { status: 3, correctMethod: 1, submitMethod: 1, desc: 'AI批改中(无操作)' },
    { status: 4, correctMethod: 1, submitMethod: 1, desc: 'AI批改待确认' },
    { status: 4, correctMethod: 2, submitMethod: 1, desc: '手动批改待确认' },
    { status: 8, correctMethod: 2, submitMethod: 1, desc: '手动批改中' },
    { status: 5, correctMethod: 1, submitMethod: 1, desc: '作业已完成' }
  ];
  
  scenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.desc}:`);
    const buttonsConfig = getStudentActionButtons(
      scenario.status,
      scenario.correctMethod,
      scenario.submitMethod
    );
    const visibleButtons = Object.values(buttonsConfig)
      .filter(btn => btn.show)
      .map(btn => btn.text);
    console.log(`   按钮: ${visibleButtons.join(', ') || '无'}\n`);
  });
}

// 导出到全局作用域（仅在浏览器环境中）
if (typeof window !== 'undefined') {
  window.testHomeworkButtons = testHomeworkButtons;
  window.testSpecificCase = testSpecificCase;
  window.testCommonScenarios = testCommonScenarios;
}

// 自动运行基础测试
console.log('🚀 作业按钮测试脚本已加载！');
console.log('📝 可用的测试函数:');
console.log('  • testHomeworkButtons() - 完整测试所有配置');
console.log('  • testCommonScenarios() - 测试常见场景');
console.log('  • testSpecificCase(状态, 批改方式, 提交方式) - 测试特定情况');
console.log('\n🎯 运行 testCommonScenarios() 查看常见场景测试结果:');

// 自动运行常见场景测试
testCommonScenarios();

// 测试新的submitMethod逻辑
function testNewSubmitMethodLogic() {
  console.log('\n🆕 测试新的submitMethod逻辑:');
  console.log('='.repeat(60));

  const testCases = [
    // 待提交/未提交/待订正状态测试 - 同时显示催交和帮录
    { status: 0, submitMethod: 1, desc: '待提交 + 学生在线答题', expected: ['催交', '帮录'] },
    { status: 0, submitMethod: 2, desc: '待提交 + 学生平板拍照', expected: ['催交', '帮录'] },
    { status: 0, submitMethod: 3, desc: '待提交 + 教师帮录', expected: ['催交', '帮录'] },
    { status: 1, submitMethod: 1, desc: '未提交 + 学生在线答题', expected: ['催交', '帮录'] },
    { status: 1, submitMethod: 2, desc: '未提交 + 学生平板拍照', expected: ['催交', '帮录'] },
    { status: 1, submitMethod: 3, desc: '未提交 + 教师帮录', expected: ['催交', '帮录'] },
    { status: 6, submitMethod: 1, desc: '待订正 + 学生在线答题', expected: ['催交', '帮录'] },
    { status: 6, submitMethod: 3, desc: '待订正 + 教师帮录', expected: ['催交', '帮录'] },
    // 已完成状态测试
    { status: 5, submitMethod: 1, desc: '已完成 + 学生在线答题', expected: ['查看'] },
    { status: 5, submitMethod: 2, desc: '已完成 + 学生平板拍照', expected: ['查看'] },
    { status: 5, submitMethod: 3, desc: '已完成 + 教师帮录', expected: ['查看'] },
    // 批改相关状态测试
    { status: 2, correctMethod: 1, submitMethod: 1, desc: '待批改 + AI批改模式', expected: [] },
    { status: 2, correctMethod: 2, submitMethod: 1, desc: '待批改 + 手动批改模式', expected: ['手动批改', 'AI批改'] },
    { status: 4, correctMethod: 1, submitMethod: 1, desc: '待确认 + AI批改模式', expected: ['确认', '重新批改'] },
    { status: 4, correctMethod: 2, submitMethod: 1, desc: '待确认 + 手动批改模式', expected: ['确认', 'AI批改'] },
    { status: 8, correctMethod: 2, submitMethod: 1, desc: '手动批改中', expected: ['AI批改'] }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. ${testCase.desc}:`);

    const buttonsConfig = getStudentActionButtons(
      testCase.status,
      testCase.correctMethod || 1, // 使用测试用例中的correctMethod，默认为AI批改
      testCase.submitMethod
    );

    const actualButtons = Object.values(buttonsConfig)
      .filter(btn => btn.show)
      .map(btn => btn.text);

    console.log(`   预期按钮: [${testCase.expected.join(', ')}]`);
    console.log(`   实际按钮: [${actualButtons.join(', ')}]`);

    const isMatch = testCase.expected.length === actualButtons.length &&
                   testCase.expected.every(btn => actualButtons.includes(btn));

    if (isMatch) {
      console.log('   ✅ 测试通过');
    } else {
      console.log('   ❌ 测试失败');
    }
  });

  console.log('\n📊 新submitMethod逻辑测试完成！');
}

// 导出新测试函数（仅在浏览器环境中）
if (typeof window !== 'undefined') {
  window.testNewSubmitMethodLogic = testNewSubmitMethodLogic;
}

console.log('\n🆕 新增测试函数:');
console.log('  • testNewSubmitMethodLogic() - 测试新的submitMethod逻辑');

// 自动运行新测试
testNewSubmitMethodLogic();

// 显示新规则总结
function showNewRulesSummary() {
  console.log('\n📋 新按钮显示规则总结:');
  console.log('='.repeat(80));

  const rules = [
    { category: '待提交/未提交/待订正', conditions: '所有情况', buttons: '催交 + 帮录' },
    { category: 'AI批改模式-待批改/批改中', conditions: 'correctMethod=1', buttons: '(无操作)' },
    { category: 'AI批改模式-待确认', conditions: 'correctMethod=1', buttons: '确认 + 重新批改' },
    { category: 'AI批改模式-已完成', conditions: 'correctMethod=1', buttons: '查看' },
    { category: '手动批改模式-待批改', conditions: 'correctMethod=2', buttons: '批改 + AI批改' },
    { category: '手动批改模式-批改中(AI)', conditions: 'correctMethod=2', buttons: '(无操作)' },
    { category: '手动批改模式-批改中(手动)', conditions: 'correctMethod=2', buttons: 'AI批改' },
    { category: '手动批改模式-待确认', conditions: 'correctMethod=2', buttons: '确认 + AI批改' },
    { category: '手动批改模式-已完成', conditions: 'correctMethod=2', buttons: '查看' }
  ];

  rules.forEach((rule, index) => {
    console.log(`${index + 1}. ${rule.category}`);
    console.log(`   条件: ${rule.conditions}`);
    console.log(`   按钮: ${rule.buttons}\n`);
  });
}

// 导出新函数
if (typeof window !== 'undefined') {
  window.showNewRulesSummary = showNewRulesSummary;
}

console.log('\n📋 新增函数:');
console.log('  • showNewRulesSummary() - 显示新规则总结');

// 自动显示规则总结
showNewRulesSummary();
