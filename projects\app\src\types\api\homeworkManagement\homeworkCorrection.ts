// 作业批改相关类型定义

import {
  GradeTypeEnum,
  HomeworkStatusEnum,
  TaskTypeEnum,
  SubmitMethodEnum,
  EnableStatusEnum,
  SubjectTypeEnum,
  CorrectResultEnum,
  AddErrorRuleEnum,
  StudentNoteTypeEnum,
  AnswerTypeEnum,
  ShareStatusEnum,
  SyncStatusEnum,
  ResourceStatusEnum,
  CutQuestionStatusEnum,
  QuestionStatusEnum,
  MasteryLevelEnum,
  StudentSubmitStatusEnum,
  CorrectMethodEnum
} from '@/constants/api/homeworkManagement/homeworkCorrection';

// 文件对象类型
export interface FileObject {
  createTime: string; // 创建时间
  fileJson: string; // 文件详情Json数据
  fileKey: string; // 文件objectKey
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileType: string; // 文件类型：图片、音频、视频等
  fileUrl: string; // 文件链接
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  updateTime: string; // 更新时间
}

// 资源VO类型
export interface ResourcesVO {
  areaCode: string; // 地区code
  areaId: number; // 地区ID
  areaName: string; // 地区名称
  attribution: number; // 归属类型
  attributionToId: number; // 归属关联ID
  attributionToName: string; // 归属管理名称
  createTime: string; // 创建时间
  createUserName: string; // 上传者名称
  curation: EnableStatusEnum; // 是否精选:0否；1是
  customTags: string[]; // 自定义标签列表
  cutQuestion: CutQuestionStatusEnum; // 切题状态：0未切题；1已切题，未审核；2已审核
  description: string; // 资源描述
  downloadCount: number; // 下载量
  fileFormatId: number; // 文件格式ID
  fileFormatName: string; // 文件格式名
  fileJson: string; // 文件详情Json
  fileKey: string; // 文件Key
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileUrl: string; // 文件路径
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  id: number; // 主键ID
  isCollected: EnableStatusEnum; // 是否已收藏：0否，1是
  isOperable: EnableStatusEnum; // 是否可操作：0否，1是
  isOwned: EnableStatusEnum; // 是否属于当前用户：0否，1是
  knowledgePointIds: number[]; // 知识点ID列表
  knowledgePointNames: string[]; // 知识点名称列表
  resourceTypeId: number; // 资源类型ID
  resourceTypeName: string; // 资源类型名称
  shareStatus: ShareStatusEnum; // 共享状态：0私有，1共享到学校
  stageId: number; // 学段ID
  stageName: string; // 学段名称
  status: ResourceStatusEnum; // 资源状态：0草稿，1审核中，2被驳回，3已发布，4已下架
  subjectId: number; // 学科ID
  subjectName: string; // 学科名称
  textbookChapterIds: number[]; // 章节ID列表
  textbookChapterNames: string[]; // 章节名称列表
  textbookVersionId: number; // 教材版本ID
  textbookVersionName: string; // 教材版本名称
  textbookVolumeId: number; // 教材ID
  textbookVolumeName: string; // 教材名称
  title: string; // 资源标题
  viewCount: number; // 预览量
  vintages: number; // 年份
}

// 作业辅助材料文件响应
export interface HomeworkMaterialFilesResponse {
  contentId: number; // 内容ID
  createTime: string; // 创建时间
  file: FileObject; // 文件对象
  homeworkId: number; // 作业ID
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  level: MasteryLevelEnum; // 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
  resourceId: number; // 资源ID
  resourcesVO: ResourcesVO; // 资源详情对象
  updateTime: string; // 更新时间
}

// 学生作业提交题目文件响应
export interface StudentHomeworkSubmitFilesResponse {
  createTime: string; // 创建时间
  file: FileObject; // 文件对象
  fileKey: string; // 文件key
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  questionId: number; // 题目ID
  submitId: number; // 学生作业提交ID
  updateTime: string; // 更新时间
}

// 学生笔记对象
export interface StudentsNotes {
  createTime: string; // 创建时间
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  noteContent: string; // 笔记内容
  questionId: number; // 题目ID
  resourceId: number; // 资源ID
  tenandId: number; // 租户ID（注意：原API文档中拼写为 tenandId，保持一致）
  tmdId: number; // 租户用户ID
  type: StudentNoteTypeEnum; // 类型：1做题笔记；2学习笔记；3自由笔记
  updateTime: string; // 更新时间
}

// 学生作业提交题目评分响应
export interface StudentHomeworkSubmitQuestionsResponse {
  correctResult: CorrectResultEnum; // 批改结果：0-错误；1-正确；2-部分正确
  createTime: string; // 创建时间
  earnedScore: number; // 评分
  files: StudentHomeworkSubmitFilesResponse[]; // 学生作业提交题目文件列表
  id: number; // 主键ID
  isAddError: EnableStatusEnum; // 是否归纳为错题：0-否；1-是
  isDeleted: number; // 是否删除：0-否，1-是
  questionId: number; // 题目ID
  reason: string; // 评分理由
  studentAnswer: string; // 学生答案
  submitId: number; // 学生作业提交ID
  updateTime: string; // 更新时间
  wrongCount: number; // 累计答错次数
}

// 题目VO类型
export interface QuestionsVO {
  analysis: string; // 解析
  answer: string; // 答案
  answerType: AnswerTypeEnum; // 答题形式：1选择；2填空；3解答
  areaCode: string; // 地区Code
  areaId: number; // 地区ID
  areaName: string; // 地区名称
  attribution: number; // 归属类型
  attributionToId: number; // 归属关联ID
  attributionToName: string; // 题目归属关联名称
  children: QuestionsVO[]; // 子题目列表
  createTime: number; // 创建时间(时间戳)
  difficulty: number; // 难度
  difficultyName: string; // 难度名称
  feedbackCount: number; // 待处理反馈数量
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  id: number; // 主键ID
  knowledgePointIds: number[]; // 题目知识点ID列表
  knowledgePointNames: string[]; // 题目知识点名称列表
  options: string; // 题目选项
  originInfo: string; // 题目来源
  originQuestionId: number; // 源题目ID
  originResourceId: number; // 来源资源ID
  parentId: number; // 父级ID
  questionOrder: number; // 题号
  questionTypeCode: string; // 题目类型编码
  questionTypeId: number; // 题目类型ID
  questionTypeName: string; // 题目类型名称
  resourceId: number; // 资源ID
  resourceQuestion: EnableStatusEnum; // 是否资源题：0-否，1-是
  shareStatus: ShareStatusEnum; // 共享状态：0私有，1共享到学校
  stageId: number; // 学段ID
  stageName: string; // 学段名称
  status: QuestionStatusEnum; // 状态
  stem: string; // 题干
  studentsNotes: StudentsNotes; // 学生笔记对象
  subjectId: number; // 学科ID
  subjectName: string; // 学科名称
  submitQuestion: StudentHomeworkSubmitQuestionsResponse; // 学生作业提交题目评分响应
  syncStatus: SyncStatusEnum; // 同步状态(0:未同步,1:已同步,2:同步失败)
  tags: string[]; // 题目标签列表
  textbookChapterIds: number[]; // 教材章节ID列表
  textbookChapterNames: string[]; // 教材章节名称列表
  textbookVersionId: number; // 教材版本ID
  textbookVersionName: string; // 教材版本名称
  textbookVolumeId: number; // 教材ID
  textbookVolumeName: string; // 教材名称
  tmbName: string; // 租户用户名
  updateTime: number; // 更新时间(时间戳)
  vintages: number; // 年份
}

// 作业题目响应
export interface HomeworkQuestionResponse {
  children: HomeworkQuestionResponse[]; // 子题目列表
  contentLayoutId: number; // 内容布局ID
  createTime: string; // 创建时间
  gradingCriteria: string; // 评分标准
  homeworkId: number; // 作业ID
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  level: MasteryLevelEnum; // 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层
  parentId: number; // 父作业题目ID
  question: QuestionsVO; // 题目详情对象
  questionId: number; // 题目ID
  questionOrder: number; // 题号：题目在作业中的顺序
  score: number; // 题目分值
  updateTime: string; // 更新时间
}

// 学生作业提交记录响应
export interface StudentHomeworkSubmitResponse {
  addErrorRule: AddErrorRuleEnum; // 归纳错题规则：1-订正后，需要批改；2-订正后，向学生公布答案解析
  completedCount: number; // 已完成题目数
  confirmTime: string; // 确认批改时间
  createTime: string; // 创建时间
  homeworkStudentId: number; // 作业学生ID
  id: number; // 主键ID
  isDeleted: number; // 是否删除：0-否，1-是
  isRevise: EnableStatusEnum; // 是否为订正：0-否；1-是
  paperFiles: StudentHomeworkSubmitFilesResponse[]; // 学生纸质作业提交文件列表
  questionList: HomeworkQuestionResponse[]; // 学生作业提交题目列表
  rejectReason: string; // 打回原因
  reviseCount: number; // 订正次数
  score: number; // 得分
  status: StudentSubmitStatusEnum; // 状态：0-待提交，1-已提交
  submitTime: string; // 提交时间
  timeSecond: number; // 用时(秒)
  totalCount: number; // 共计题目数
  totalScore: number; // 作业总分值
  earnedScore: number; // 得分
  uncompletedCount: number; // 未完成题目数
  updateTime: string; // 更新时间
}

// 学生作业响应（主要响应类型）
export interface StudentHomeworkResponse {
  aiAssistantTriggers: string; // AI答疑环节：1-学生练习中；2-学生提交作业后；3-教师确认批改后。多选字段，值以逗号分隔
  correctionReason: string; // 订正原因
  deadlineTime: string; // 截止时间
  description: string; // 作业说明
  detailedExplanation: string; // 详细解析
  enableAiAssistant: EnableStatusEnum; // 是否开启AI答疑助手：0-关闭；1-开启
  enableAllowResubmit: EnableStatusEnum; // 是否开启允许补交：0-关闭；1-开启
  enableAnswerVisible: EnableStatusEnum; // 是否开启批改后，答案对学生可见：0-关闭；1-开启
  failureReason: string; // 失败原因
  gradeType: GradeTypeEnum; // 年级类型：1-小学低年级；2-小学高年级；3-初中；4-高中
  homeworkId: number; // 作业ID
  id: number; // 主键ID
  isSelfHomework: EnableStatusEnum; // 是否是学生自主作业：0-否，1-是
  materialFileList: HomeworkMaterialFilesResponse[]; // 辅助材料文件列表
  name: string; // 作业名称
  overallAnalysis: string; // 总体分析
  questionList: HomeworkQuestionResponse[]; // 作业题目列表
  selfHomeworkFiles: FileObject[]; // 自主作业文件列表
  status: HomeworkStatusEnum; // 状态：0-待提交，1-未提交，2-待批改，3-批改中，4-待确认，5-已完成，6-待订正，7-批改失败
  correctMethod: CorrectMethodEnum; // 批改方式：1-AI批改；2-手动批改
  subjectId: number; // 学科ID
  subjectName: string; // 学科名称
  subjectType: SubjectTypeEnum; // 学科分科类型：0未选择；1文科；2理科
  submitList: StudentHomeworkSubmitResponse[]; // 学生提交记录列表
  submitMethod: SubmitMethodEnum; // 提交方式：1-学生在线答题；2-学生平板拍照；3-教师帮录
  taskType: TaskTypeEnum; // 作业任务类型：1-在线作业；2-纸质作业；3-写作任务；4-自主作业
  typeAlias: string; // 作业类型别名
  typeId: number; // 作业类型ID
  typeName: string; // 作业类型名称
  uploadTime: string; // 上传时间
}

// 获取学生作业详情请求参数
export interface GetStudentHomeworkDetailRequest {
  id: number; // 学生作业ID
  tmbId?: number; // 租户用户ID（可选）
  userId?: number; // 用户ID（可选）
}

// 题目评分对象
export interface QuestionScore {
  questionId: number; // 题目ID
  score: number; // 学生得分（可修改）
  scoreReason: string; // 评分理由（可修改）
  maxScore: number; // 题目分值（只读）
}

// 手动批改评分请求参数
export interface ManualScoreRequest {
  isSave: boolean; // 是否保存
  questionScores?: QuestionScore[]; // 题目评分列表
  submitId?: number; // 提交ID
}

// 批量转为AI批改 请求参数
export interface ConvertToAiBatchRequest {
  /** 学生作业ID列表 */
  homeworkStudentIds: number[];
}

// API响应包装类型（虽然说忽略success层，但为了完整性保留）
export interface ApiResponse<T> {
  code: number; // 状态码
  data: T; // 返回数据
  msg: string; // 返回消息
  success: boolean; // 是否成功
}
