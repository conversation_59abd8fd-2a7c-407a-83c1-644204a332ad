type TreeType = Record<PropertyKey, any>;

/**
 * 遍历树结构
 * @param nodes
 * @param callback 回调，返回非undefined时停止遍历
 * @param preOrder 是否先序遍历，否则后序（没有中序）
 * @param childrenKey 孩子结点名称
 * @param level 起始层级
 * @returns callback的返回值
 */
export function treeTraverse<T extends TreeType, R>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => R | undefined,
  childrenKey: keyof T = 'children',
  preOrder = true,
  level = 0,
  parent?: T
) {
  let result: R | undefined;
  (Array.isArray(nodes) ? nodes : [nodes]).some((node) => {
    if (preOrder) {
      result = callback(node, parent, level);
      if (result !== undefined) {
        return true;
      }
    }
    if (childrenKey && node[childrenKey]?.length) {
      result = treeTraverse(node[childrenKey], callback, childrenKey, preOrder, level + 1, node);
      if (result !== undefined) {
        return true;
      }
    }
    if (!preOrder) {
      result = callback(node, parent, level);
      if (result !== undefined) {
        return true;
      }
    }
    return false;
  });
  return result;
}

export const treeFind = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => (callback(node, parent, level) ? node : undefined),
    childrenKey,
    preOrder
  );

export const treeFindAll = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) => {
  const result: T[] = [];
  treeTraverse(
    nodes,
    (node, parent, level) => {
      callback(node, parent, level) && result.push(node);
    },
    childrenKey,
    preOrder
  );
  return result;
};

export const treeSome = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  !!treeTraverse(
    nodes,
    (node, parent, level) => callback(node, parent, level) || undefined,
    childrenKey,
    preOrder
  );

export const treeEvery = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => boolean,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => callback(node, parent, level) || undefined,
    childrenKey,
    preOrder
  ) === undefined;

export const treeForEach = <T extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, level: number) => void,
  childrenKey: keyof T = 'children',
  preOrder = true
) =>
  treeTraverse(
    nodes,
    (node, parent, level) => {
      callback(node, parent, level);
    },
    childrenKey,
    preOrder
  );

export function treeMap<T extends TreeType, R extends TreeType>(
  nodes: T[],
  callback: (node: T, parent: T | undefined, mapParent: R | undefined, level: number) => R,
  childrenKey?: keyof T,
  mappedChildrenKey?: keyof R,
  parent?: T,
  mappedParent?: R,
  level?: number
): R[];

export function treeMap<T extends TreeType, R extends TreeType = any>(
  nodes: T,
  callback: (node: T, parent: T | undefined, mapParent: R | undefined, level: number) => R,
  childrenKey?: keyof T,
  mappedChildrenKey?: keyof R,
  parent?: T,
  mappedParent?: R,
  level?: number
): R;

export function treeMap<T extends TreeType, R extends TreeType>(
  nodes: T | T[],
  callback: (node: T, parent: T | undefined, mapParent: R | undefined, level: number) => R,
  childrenKey: keyof T = 'children',
  mappedChildrenKey: keyof R = 'children',
  parent?: T,
  mappedParent?: R,
  level?: number
): R | R[] {
  if (Array.isArray(nodes)) {
    return nodes.map((node) => {
      const mappedNode = callback(node, parent, mappedParent, level || 0);
      delete mappedNode[childrenKey];
      if (node[childrenKey]) {
        mappedNode[mappedChildrenKey] = treeMap(
          node[childrenKey],
          callback,
          childrenKey,
          mappedChildrenKey,
          node,
          mappedNode,
          (level || 0) + 1
        ) as any;
      }
      return mappedNode;
    }) as R[];
  } else {
    return treeMap(
      [nodes],
      callback,
      childrenKey,
      mappedChildrenKey,
      parent,
      mappedParent,
      level
    )[0] as R;
  }
}

export const treeToList = <T extends TreeType>(
  nodes: T | T[],
  childrenKey: keyof T = 'children',
  preOrder = true
) => {
  const result: T[] = [];
  treeTraverse(
    nodes,
    (node) => {
      result.push(node);
    },
    childrenKey,
    preOrder
  );
  return result;
};

export function treeClone<T extends TreeType>(node: T[], childrenKey?: keyof T): T[];
export function treeClone<T extends TreeType>(node: T, childrenKey?: keyof T): T;

export function treeClone<T extends TreeType>(
  nodes: T | T[],
  childrenKey: keyof T = 'children'
): T | T[] {
  if (Array.isArray(nodes)) {
    return nodes.map((node) => {
      const newNode = {
        ...node
      };
      childrenKey &&
        newNode[childrenKey] &&
        (newNode[childrenKey] = treeClone(newNode[childrenKey], childrenKey) as any);
      return newNode;
    }) as T[];
  } else {
    const newNode = {
      ...nodes
    } as T;
    childrenKey &&
      newNode[childrenKey] &&
      (newNode[childrenKey] = treeClone(newNode[childrenKey], childrenKey) as any);
    return newNode;
  }
}
/**
 * 找出每个ID的路径
 * @param nodes 树结构
 * @param ids 需要查找路径的ID数组
 * @param idKey 节点ID的键名
 * @param childrenKey 孩子结点名称
 * @returns 每个ID对应的路径数组
 */
export function treeFindPaths<T extends TreeType>(
  nodes: T | T[],
  ids: any[],
  idKey: keyof T = 'id',
  childrenKey: keyof T = 'children'
): Record<any, T[]> {
  const paths: Record<any, T[]> = {};

  function traverse(node: T, currentPath: T[]) {
    const newPath = [...currentPath, node];

    if (ids.includes(node[idKey])) {
      paths[node[idKey]] = newPath;
    }

    const children = node[childrenKey];
    if (children && Array.isArray(children)) {
      for (const child of children as T[]) {
        traverse(child, newPath);
      }
    }
  }

  if (Array.isArray(nodes)) {
    for (const node of nodes) {
      traverse(node, []);
    }
  } else {
    traverse(nodes, []);
  }

  return paths;
}

interface TreeConfigOptions {
  // 子属性的名称，默认为'children'
  childProps: string;
}

/**
 * 根据条件过滤给定树结构的节点，并以原有顺序返回所有匹配节点的数组。
 * @param tree 要过滤的树结构的根节点数组。
 * @param filter 用于匹配每个节点的条件。
 * @param options 作为子节点数组的可选属性名称。
 * @returns 包含所有匹配节点的数组。
 */

interface TreeTypeChildren extends TreeType {
  children: TreeTypeChildren;
}

export function filterTree<T extends Record<string, any>>(
  tree: T[],
  filter: (node: T) => boolean,
  options?: TreeConfigOptions
): T[] {
  const { childProps } = options || {
    childProps: 'children'
  };

  const _filterTree = (nodes: T[]): T[] => {
    return nodes.reduce((acc: T[], node: T) => {
      const children = node[childProps] as T[] | undefined;
      let hasMatchingChildren = false;

      // 递归过滤子节点
      if (children) {
        const filteredChildren = _filterTree(children);
        if (filteredChildren.length > 0) {
          hasMatchingChildren = true; // 有匹配的子节点
          (node[childProps as keyof T] as T[]) = filteredChildren; // 更新节点的子节点
        }
      }

      // 检查当前节点是否匹配
      if (filter(node) || hasMatchingChildren) {
        acc.push({ ...node }); // 复制节点并添加到结果中
      }

      return acc;
    }, []);
  };

  return _filterTree(tree);
}

type FilterCallback<T> = (node: T) => boolean;
