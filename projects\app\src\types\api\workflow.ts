import { PromptType } from './prompt';
import { AppListItemType } from './app';
import { DataSource } from '@/constants/common';

// src/types/api/tenantWorkflow.ts

export interface TenantWorkflow {
  id: string;
  name: string;
  tenantAppId: string;
  tenantId: string;
  tmbId: string;
  workflowId: number;
  industry: number;
  isDeleted: number;
  source: DataSource;
  status: number;
  createTime: string;
  updateTime: string;
}

export interface CreateWorkflowParams {
  name: string;
  tenantAppId: string;
}

export interface DeleteWorkflowParams {
  id: string;
  tmbId: string;
}

export interface CopyWorkflowParams {
  id: string;
}

export interface ListWorkflowsParams {
  tenantAppId: string;
}

export interface UpdateWorkflowParams {
  id: string;
  name: string;
}

// src/types/api/tenantWorkflowProcess.ts

export interface TenantWorkflowProcess {
  id: string;
  name?: string;
  intro?: string;
  tenantAppId?: string;
  tenantPromptId?: string;
  tenantWorkflowId?: string;
  ignoreContext?: number;
  isDeleted?: number;
  isAdd?: boolean;
  status?: number;
  sort?: number;
  createTime?: string;
  updateTime?: string;
  appAvatarUrl?: string;
  appIntro?: string;
  appName?: string;
  appSource?: DataSource;
  finalAppId?: string;
  hiddenContent?: string;
  inputContent?: string;
  proContent?: string;
  promptSource?: DataSource;
  promptDescription?: string;
  promptTitle?: string;
  workflowProcessId?: number;
}

export interface CreateWorkflowProcessParams {
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
  tenantWorkflowId: string;
}

export interface DeleteWorkflowProcessParams {
  id: string;
  tmbId?: string;
}

export interface ListWorkflowProcessesParams {
  id: string;
  tmbId?: string;
}

export interface ReSortWorkflowProcessesParams {
  reSortList: TenantWorkflowProcess[];
}

export interface UpdateWorkflowProcessParams {
  id: string;
  ignoreContext: number;
  intro: string;
  name: string;
  tenantAppId: string;
  tenantPromptId: string;
}
