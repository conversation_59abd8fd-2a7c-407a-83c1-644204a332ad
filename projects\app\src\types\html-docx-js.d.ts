declare module 'html-docx-js/dist/html-docx' {
  /**
   * Converts HTML string to a Blob object.
   *
   * @param html - The HTML string to convert.
   * @param options - Optional configuration options.
   * @param options.orientation - The orientation of the document, either 'landscape' or 'portrait' (default: 'portrait').
   * @param options.margins - The margins of the document (expressed in twentieths of a point).
   * @param options.margins.top - The top margin (default: 1440, i.e. 2.54 cm).
   * @param options.margins.right - The right margin (default: 1440).
   * @param options.margins.bottom - The bottom margin (default: 1440).
   * @param options.margins.left - The left margin (default: 1440).
   * @param options.margins.header - The header margin (default: 720).
   * @param options.margins.footer - The footer margin (default: 720).
   * @param options.margins.gutter - The gutter margin (default: 0).
   * @returns A Blob object representing the converted HTML.
   */
  export function asBlob(
    html: string,
    options?: {
      orientation?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
        header?: number;
        footer?: number;
        gutter?: number;
      };
    }
  ): Blob;
}
