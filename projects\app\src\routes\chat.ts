import { RouteConfigType } from '@/types/routes';

export const chatRoutes: RouteConfigType[] = [
  {
    name: '首页',
    path: '/home/<USER>',
    icon: 'home',
    code: 'home_index',
    activeIcon: 'home2'
  },
  {
    name: '首页',
    path: '/home',
    hidden: true
  },
  {
    name: '首页', // 职教版
    path: '/home/<USER>',
    code: 'education_advanced_home_index',
    icon: 'home',
    activeIcon: 'home2'
  },
  {
    name: '首页', // 企业版
    path: '/home/<USER>',
    code: 'enterprise_home_index',
    icon: 'home',
    activeIcon: 'home2'
  },
  {
    name: '应用中心',
    path: '/app/list',
    code: 'app_center',
    icon: 'navAppCenter',
    activeIcon: 'navAppCenter2'
  },
  {
    name: '应用中心', // 职教版
    path: '/app/list/educationAdvanced',
    code: 'education_advanced_app_center',
    icon: 'navAppCenter',
    activeIcon: 'navAppCenter2'
  },
  {
    name: '应用中心', // 企业版
    path: '/app/list/enterprise',
    code: 'enterprise_app_center',
    icon: 'navAppCenter',
    activeIcon: 'navAppCenter2'
  },
  {
    name: '数据空间',
    path: '/cloud/list',
    code: 'cloud_library',
    icon: 'navCloud',
    activeIcon: 'navCloud2',
    activePrefixes: ['/cloud/']
  },
  // {
  //   name: '知识库',
  //   path: '/dataset/list',
  //   code: 'knowledge_base',
  //   icon: 'navDataset',
  //   activeIcon: 'navDataset2',
  //   activePrefixes: ['/dataset/']
  // },
  {
    name: '零代码空间',
    path: '/zeroCode',
    code: 'zero_code_space',
    icon: 'navZeroCode',
    activeIcon: 'navZeroCode2',
    activePrefixes: ['/zeroCode', '/dataset']
  },
  {
    name: 'AI作文批改',
    path: '/compositionCorrection', // 添加可选传参
    code: 'ai_correction',
    icon: 'navCompositionCorrection',
    activeIcon: 'navCompositionCorrection2',
    activePrefixes: ['/compositionCorrection'],
    children: [
      {
        name: '作文批改',
        path: '/compositionCorrection/detail',
        code: 'ai_correction_detail',
        hidden: true,
        unauth: true
      },
      {
        name: '批改结果',
        path: '/compositionCorrection/taskResult',
        code: 'ai_correction_task_result',
        hidden: true,
        unauth: true
      },
      {
        name: '录入作文',
        path: '/compositionCorrection/correctionUpload',
        code: 'ai_correction_correction_upload',
        hidden: true,
        unauth: true
      },
      {
        name: '作文批改',
        path: '/compositionCorrection/correctionRecord/:id',
        code: 'ai_correction_correction_record',
        hidden: true,
        unauth: true
      }
    ]
  },
  {
    name: 'PPT生成',
    path: '/pptGenerate',
    code: 'ppt_generate',
    icon: 'navPPT',
    activeIcon: 'navPPT2',
    activePrefixes: ['/pptGenerate']
  },
  {
    name: 'AI平台使用指南',
    path: '/aiPlatform/guide',
    code: 'ai_platform_guide',
    icon: 'navAiPlatform',
    activeIcon: 'navAiPlatform2',
    activePrefixes: ['/aiPlatform/guide'],
    unauth: true,
    hidden: true
  },
  {
    name: 'AI出题组卷',
    path: '/aiExam',
    code: 'ai_exam',
    icon: 'navAiExam',
    activeIcon: 'navAiExam2',
    activePrefixes: ['/aiExam']
  },
  {
    name: 'AI平台导航',
    path: '/aiPlatform',
    code: 'ai_platform',
    icon: 'navAiPlatform',
    activeIcon: 'navAiPlatform2',
    activePrefixes: ['/aiPlatform']
  },
  {
    name: '社团管理',
    path: '/clubManagement',
    code: 'club_management',
    icon: 'navClub',
    activeIcon: 'navClub2',
    activePrefixes: ['/clubManagement']
  },
  {
    name: '教务管理系统',
    path: '/educationalManagement',
    code: 'educational_management',
    icon: 'navEducational',
    activeIcon: 'navEducational2',
    activePrefixes: ['/educationalManagement']
  },
  // 以下为演示路由
  {
    name: 'AI教育资源',
    icon: 'navAiResource',
    path: '/ai/resources',
    code: 'demo_resources',
    unauth: true
  },
  {
    name: 'AI学校治理',
    icon: 'navAiGovern',
    path: '/ai/governance',
    code: 'demo_governance',
    unauth: true
  },
  {
    name: 'AI自适应学习',
    icon: 'navAiLearning',
    path: '/ai/learning',
    code: 'demo_learning',
    unauth: true
  },
  {
    name: '应用详情',
    icon: 'appDetail',
    path: '/app/detail',
    code: 'app_detail',
    hidden: true,
    unauth: true
  },
  {
    name: '数字人',
    path: '/humans',
    hidden: true
  },
  {
    name: '资源广场',
    path: '/resourceSquare',
    code: 'resource_square',
    icon: 'navResourceSquare',
    activeIcon: 'navResourceSquare1',
    activePrefixes: ['/resourceSquare']
  },
  {
    name: '资源详情',
    path: '/resourceSquare/detail',
    code: 'resource_square_detail',
    icon: 'navHomework'
    // activeIcon: 'navHomework2',
  },
  {
    name: '个人中心',
    path: '/personalCenter',
    code: 'personal_center',
    icon: 'navPersonalCenter',
    activeIcon: 'navPersonalCenter1',
    activePrefixes: ['/personalCenter']
  },
  {
    name: '资源详情',
    path: '/personalCenter/resourceDetail',
    code: 'personal_center_resource_detail'

    // activeIcon: 'navHomework2',
  },
  {
    name: '作业管理',
    path: '/homeworkManagement',
    code: 'homework_manage',
    icon: 'navHomework',
    activeIcon: 'navHomework2',
    children: [
      {
        name: '作文批改',
        path: '/homeworkManagement/assignmentRelease',
        code: 'homework_manage_assignment_release',
        hidden: true,
        unauth: true
      },
      {
        name: '作业详情',
        path: '/homeworkManagement/assignmentRelease/homeworkDetails',
        code: 'homework_manage_homework_details',
        hidden: true,
        unauth: true
      }
    ]
  },
  {
    name: '数据看板',
    path: '/databaseBoard',
    code: 'database_board',
    icon: 'dataBoard',
    activeIcon: 'dataBoard2',
    activePrefixes: ['/databaseBoard']
  }
];
