import {
  GetStudentHomeworkDetailRequest,
  StudentHomeworkResponse,
  ManualScoreRequest,
  ConvertToAiBatchRequest
} from '@/types/api/homeworkManagement/homeworkCorrection';
import { POST } from '@/utils/request';
import { getHomeworkDetail, getStudentSubmissionPage } from '../homeworkDetail';
// 更新为实际的API服务地址
export const BASE_URL = '/ai-homework';
/**
 * 获取学生作业详情
 * @param data 请求参数
 * @returns 学生作业详情
 *
 * @example
 * ```typescript
 * import { getStudentHomeworkDetail } from '@/api/homeworkManagement/homeworkCorrection';
 *
 * const fetchHomeworkDetail = async () => {
 *   try {
 *     const result = await getStudentHomeworkDetail({
 *       id: 123,
 *       tmbId: 456,
 *       userId: 789
 *     });
 *     console.log('作业详情:', result);
 *   } catch (error) {
 *     console.error('获取作业详情失败:', error);
 *   }
 * };
 * ```
 */
export const getStudentHomeworkDetail = (data: GetStudentHomeworkDetailRequest) =>
  POST<StudentHomeworkResponse>('/teacher/homework/student-submission/detail', data, {
    baseURL: BASE_URL
  });

/**
 * 手动批改评分
 * @param data 请求参数
 * @returns 是否成功
 *
 * @example
 * ```typescript
 * import { manualScore } from '@/api/homeworkManagement/homeworkCorrection';
 *
 * const submitScore = async () => {
 *   try {
 *     const result = await manualScore({
 *       isSave: true,
 *       questionScores: [
 *         {
 *           questionId: 123,
 *           score: 85,
 *           scoreReason: "答案正确，但步骤不够完整"
 *         }
 *       ],
 *       submitId: 456
 *     });
 *     console.log('评分结果:', result);
 *   } catch (error) {
 *     console.error('评分失败:', error);
 *   }
 * };
 * ```
 */
export const manualScore = (data: ManualScoreRequest) =>
  POST<boolean>('/teacher/homework/student-submission/manual-score', data, {
    baseURL: BASE_URL
  });

export const getStudentHomeworkSubmissionPage = getStudentSubmissionPage;

export const getHomeworkDetailForCorrection = getHomeworkDetail;

// 当前学生作业id
export const batchReminder = (data: { ids: number[] }) =>
  POST<boolean>('/teacher/homework/student-submission/batch-reminder', data, {
    baseURL: BASE_URL
  });

// 批量确认
export const batchConfirm = (data: { submitIds: number[] }) =>
  POST<boolean>('/teacher/homework/student-submission/batch-confirm', data, {
    baseURL: BASE_URL
  });

// 批量转为AI批改
export const convertToAiBatch = (data: ConvertToAiBatchRequest) =>
  POST<boolean>('/teacher/homework/correction/batch/convertToAi', data, {
    baseURL: BASE_URL
  });
