@import './reactflow.scss';
@import './default.scss';
@import './variable.module.scss';

body,
h1,
h2,
h3,
h4,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td,
svg {
  margin: 0;
}

::-webkit-scrollbar {
  width: $scrollbar-width;
  height: $scrollbar-height;
}
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover {
  background: #e5e7eb;
}

// 隐藏mermaid错误组件,避免影响布局,如果不加渲染错误会出现炸弹报错,影响布局
body > [id^='dmermaid-'] {
  opacity: 0;
  position: fixed;
}

// 常规版本数字人
#veplayerId {
  background-color: transparent !important;
  width: 100% !important;
  height: 40% !important;
  flex-shrink: 0;
}

// mocc数字人(国际公开课大竖屏)
#veplayerIdMocc {
  background-color: transparent !important;
  width: 100vw !important;
  height: 100vh !important;
  flex-shrink: 0;
  position: relative;
  top: -12px;
  left: -23px;
  > video {
    transform: scale(1.04);
  }
}

.xgplayer .xgplayer-controls {
  background-image: none !important;
}

.xgplayer-enter {
  background-color: transparent !important;
}

div {
  &::-webkit-scrollbar-thumb {
    background: transparent !important;
    transition: background 1s;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #d8d8d8 !important;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #d8d8d8 !important;
    }
  }
}

input::placeholder,
textarea::placeholder {
  font-size: 0.85em;
}

* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-focus-ring-color: rgba(0, 0, 0, 0);
  outline: none;
  box-sizing: border-box;
}

#__next {
  height: 100%;
}

@media (max-width: 900px) {
  html {
    font-size: 14px;
  }
  ::-webkit-scrollbar {
    width: $scrollbar-sm-width;
    height: $scrollbar-sm-height;
  }
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  body {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.ant-select-item-option-selected {
  background-color: #f8fafc !important;
}

.ant-cascader-menu-item-active {
  background-color: #f8fafc !important;
}

.ant-modal-confirm-body-wrapper {
  .ant-btn-primary {
    background-color: #784cff !important;
  }
  .anticon-exclamation-circle {
    color: #fb4240 !important;
  }
  .ant-btn-default {
    background: #f0f1f3 !important;
    border-color: #fff !important;
    &:hover {
      color: rgba(0, 0, 0, 0.88) !important;
    }
  }
}
.ant-dropdown-menu {
  max-height: 300px !important;
}

.ant-image-preview-operations-wrapper {
  z-index: 999999 !important;
}

.ant-image-preview-mask {
  z-index: 999999 !important;
}
.ant-image-preview-wrap {
  z-index: 999999 !important;
}

.tour-zero-space-background.ant-tour {
  width: 450px !important;
}
