import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { FileType, SpaceType, FileInfo } from './api/cloud';
import { AccessModeEnum, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';

export type BreadcrumbItemType = {
  label: string;
  isBack?: boolean;
  clickable?: boolean;
  dndData?: DndDataType;
};

export type PathSpaceType = {
  type: PathItemTypeEnum.space;
  space: SpaceType;
  parentId?: number;
  fileType?: number;
  file?: FileType;
};

export type SpacePathType = PathSpaceType[];

export type PathFileType = {
  type: PathItemTypeEnum.file;
  file: FileType;
};

export type RecyclePathFileType = {
  type: PathItemTypeEnum.file;
  file: FileType;
  name: string;
  fileType: number;
  parentId: number;
  fileUrl: string;
};

export type FilePathType = PathFileType[];

export type PathItemType = PathSpaceType | PathFileType;

export type PathType = PathItemType[];

export type TenantNavType = {
  type: NavTypeEnum.tenant;
  path: SpacePathType;
};

export type PersonalNavType = {
  type: NavTypeEnum.personal;
};

export type RecycleNavType = {
  type: NavTypeEnum.recycle;
  path?: SpacePathType;
};

export type NavType = TenantNavType | PersonalNavType | RecycleNavType;

export type BaseModalProps = {
  settingData?: { id: string; name: string };
  groupData?: { id: string; name: string };
  space?: SpaceType;
  folder?: FileType;
  bizType?: BizTypeEnum;
  onClose?: () => void;
  onSuccess?: () => void;
  EditSpaceSuccess?: () => void;
  refetchSpaceAudit?: () => void;
  onNavChange?: (nav?: NavType) => void;
};

interface FileDetails {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: number;
  tmbId: number;
  bizType: number;
  parentSpaceId: number;
  fileType: number;
  bizId: number;
  fileName: string;
  location: string;
  deleterId: number;
  deleterName: string;
  fileKey: string;
  file: FileInfo;
}

export type FileData = {
  type: string;
  file: FileDetails;
};

export type DndSpaceType = {
  type: 'space';
  space: SpaceType;
};

export type DndFileType = {
  type: 'file';
  file: FileType;
};

export type DndDataType = { onDragEnd?: () => void; onDropEnd?: () => void } & (
  | DndSpaceType
  | DndFileType
);

export type ManageAccessType = {
  mode: AccessModeEnum.Manage;
};

export type ViewAccessType = {
  mode: AccessModeEnum.View;
};

export type ViewTenantAccessType = {
  mode: AccessModeEnum.ViewTenant;
};

export type ViewMemberAccessType = {
  mode: AccessModeEnum.ViewMember;
  tmbId: string;
};

export type FolderPathType = {
  id: string;
  name: string;
};

export type AccessType =
  | ManageAccessType
  | ViewAccessType
  | ViewTenantAccessType
  | ViewMemberAccessType;
