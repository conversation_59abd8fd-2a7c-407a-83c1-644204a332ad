import React, { useEffect, useState } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Flex,
  Button,
  Input,
  Text,
  CloseButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody
} from '@chakra-ui/react';
import { Select } from 'antd';
import { use<PERSON><PERSON>, Controller, SubmitHandler } from 'react-hook-form';
import { getSchoolDeptDetail, updateSchoolDept } from '@/api/tenant/teamManagement/administration';
import { ClassDetailResponse } from '@/types/api/tenant/teamManagement/administration';
import { Toast } from '@/utils/ui/toast';

interface EditClassFormData {
  schoolSection: string; // 学段
  grade: string; // 年级
  className: string; // 班级名称
}

interface EditClassModalProps {
  isOpen: boolean;
  onClose: () => void;
  classId?: string; // 班级ID，用于编辑时获取详情
  initialData?: {
    schoolSection: string;
    grade: string;
    className: string;
  };
  onRefresh?: () => void; // 刷新父组件数据的回调
}

// 学段选项
const schoolSectionOptions = [
  { label: '小学', value: 'elementary' },
  { label: '初中', value: 'junior' },
  { label: '高中', value: 'senior' }
];

// 年级选项映射
const gradeOptions = {
  elementary: [
    { label: '一年级', value: '一年级' },
    { label: '二年级', value: '二年级' },
    { label: '三年级', value: '三年级' },
    { label: '四年级', value: '四年级' },
    { label: '五年级', value: '五年级' },
    { label: '六年级', value: '六年级' }
  ],
  junior: [
    { label: '七年级', value: '七年级' },
    { label: '八年级', value: '八年级' },
    { label: '九年级', value: '九年级' }
  ],
  senior: [
    { label: '高一', value: '高一' },
    { label: '高二', value: '高二' },
    { label: '高三', value: '高三' }
  ]
};

const EditClassModal: React.FC<EditClassModalProps> = ({
  isOpen,
  onClose,
  classId,
  initialData,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<EditClassFormData>({
    defaultValues: {
      schoolSection: '',
      grade: '',
      className: ''
    }
  });

  // 获取班级详情并设置初始数据
  useEffect(() => {
    const fetchClassDetail = async () => {
      if (classId && isOpen) {
        try {
          setLoading(true);
          const response = await getSchoolDeptDetail(classId);
          // 根据API返回的数据结构设置表单值
          // 需要根据stageName映射到对应的学段值
          let schoolSectionValue = '';
          if (response.stageName === '小学') {
            schoolSectionValue = 'elementary';
          } else if (response.stageName === '初中') {
            schoolSectionValue = 'junior';
          } else if (response.stageName === '高中') {
            schoolSectionValue = 'senior';
          }

          reset({
            schoolSection: schoolSectionValue,
            grade: response.gradeName || '',
            className: response.name || ''
          });
        } catch (error) {
          console.error('获取班级详情失败:', error);
          Toast.error('获取班级详情失败');
        } finally {
          setLoading(false);
        }
      } else if (initialData && isOpen) {
        reset({
          schoolSection: initialData.schoolSection,
          grade: initialData.grade,
          className: initialData.className
        });
      } else if (isOpen) {
        reset({
          schoolSection: '',
          grade: '',
          className: ''
        });
      }
    };

    fetchClassDetail();
  }, [classId, initialData, isOpen, reset]);

  const onSubmit: SubmitHandler<EditClassFormData> = async (data) => {
    if (!classId) {
      Toast.error('班级ID不能为空');
      return;
    }

    try {
      setLoading(true);
      await updateSchoolDept({
        id: classId,
        name: data.className
      });

      Toast.success('班级信息更新成功');

      // 调用父组件的刷新函数
      if (onRefresh) {
        onRefresh();
      }

      onClose();
    } catch (error) {
      console.error('更新班级信息失败:', error);
      Toast.error('更新班级信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} isCentered size="lg">
      <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
      <ModalContent bg="white" borderRadius="8px" minW="600px" maxW="600px">
        <ModalBody p={0}>
          {/* 标题栏 */}
          <Flex
            justifyContent="space-between"
            alignItems="center"
            p="24px 24px 20px 24px"
            borderBottom="1px solid #F2F3F5"
          >
            <Text fontSize="18px" fontWeight="500" color="#1D2129">
              编辑班级信息
            </Text>
            <CloseButton onClick={handleCancel} />
          </Flex>

          {/* 表单内容 */}
          <Box p="32px">
            {/* 学段选择 */}
            <FormControl isInvalid={!!errors.schoolSection} mb="24px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    学段
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="schoolSection"
                    control={control}
                    rules={{ required: '请选择学段' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        placeholder="请选择学段"
                        disabled={true}
                        style={{
                          width: '100%',
                          height: '48px',
                          backgroundColor: '#F7F8FA',
                          cursor: 'not-allowed',
                          borderRadius: '8px'
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        suffixIcon={
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path
                              d="M3 4.5L6 7.5L9 4.5"
                              stroke="#C9CDD4"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        }
                      >
                        {schoolSectionOptions.map((option) => (
                          <Select.Option key={option.value} value={option.value}>
                            {option.label}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  />
                </Box>
              </Flex>
              {errors.schoolSection && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.schoolSection.message}
                </Text>
              )}
            </FormControl>

            {/* 年级选择 */}
            <FormControl isInvalid={!!errors.grade} mb="24px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    年级
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="grade"
                    control={control}
                    rules={{ required: '请选择年级' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        placeholder="请选择年级"
                        disabled={true}
                        style={{
                          width: '100%',
                          height: '48px',
                          backgroundColor: '#F7F8FA',
                          cursor: 'not-allowed',
                          borderRadius: '8px'
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        suffixIcon={
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path
                              d="M3 4.5L6 7.5L9 4.5"
                              stroke="#C9CDD4"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        }
                      ></Select>
                    )}
                  />
                </Box>
              </Flex>
              {errors.grade && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.grade.message}
                </Text>
              )}
            </FormControl>

            {/* 班级名称输入 */}
            <FormControl isInvalid={!!errors.className} mb="40px">
              <Flex direction="column">
                <Flex alignItems="center" mb="8px">
                  <FormLabel
                    color="#1D2129"
                    fontSize="14px"
                    fontWeight="400"
                    mb={0}
                    whiteSpace="nowrap"
                  >
                    班级名称
                  </FormLabel>
                  <Text color="#F53F3F" ml="2px">
                    *
                  </Text>
                </Flex>
                <Box>
                  <Controller
                    name="className"
                    control={control}
                    rules={{ required: '请输入班级名称' }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="请输入班级名称"
                        height="48px"
                        border="1px solid #E5E6EB"
                        borderRadius="8px"
                        fontSize="14px"
                        bg="white"
                        _placeholder={{ color: '#C9CDD4' }}
                        _focus={{
                          borderColor: '#7C3AED',
                          boxShadow: '0 0 0 2px rgba(124, 58, 237, 0.1)'
                        }}
                        _hover={{
                          borderColor: '#B794F6'
                        }}
                      />
                    )}
                  />
                </Box>
              </Flex>
              {errors.className && (
                <Text color="#F53F3F" fontSize="12px" mt="4px">
                  {errors.className.message}
                </Text>
              )}
            </FormControl>

            {/* 操作按钮 */}
            <Flex justifyContent="flex-end" gap="16px">
              <Button
                variant="outline"
                borderColor="#E5E6EB"
                color="#6B7280"
                bg="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                _hover={{
                  borderColor: '#D1D5DB',
                  bg: '#F9FAFB'
                }}
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                bg="#7C3AED"
                color="white"
                height="40px"
                px="24px"
                fontSize="14px"
                fontWeight="400"
                borderRadius="8px"
                minW="80px"
                isLoading={loading}
                loadingText="保存中..."
                _hover={{
                  bg: '#6D28D9'
                }}
                _active={{
                  bg: '#5B21B6'
                }}
                onClick={handleSubmit(onSubmit)}
              >
                保存
              </Button>
            </Flex>
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default EditClassModal;
