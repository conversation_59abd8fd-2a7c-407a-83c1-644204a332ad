import React, { useState, useCallback } from 'react';
import { Box, useDisclosure } from '@chakra-ui/react';
import SettingSubjectsModal from '../SettingSubjectsModal';
import { useSubjectManagerData } from './hooks/useSubjectManagerData';
import { useEditTeachers } from './hooks/useEditTeachers';
import LeadershipTable from './components/LeadershipTable';
import GradeTable from './components/GradeTable';
import EmptyState from './components/EmptyState';
import { SubjectHeadManagementProps, HoveredCellState } from './types';

/**
 * 学科负责人管理组件
 * 重构后的版本，提高了代码可维护性和可读性
 */
const SubjectHeadManagement: React.FC<SubjectHeadManagementProps> = ({
  onEditTeachers,
  refreshList,
  semesterData,
  highlightedTmbIds,
  semesterId
}) => {
  // 悬停状态管理
  const [hoveredCell, setHoveredCell] = useState<HoveredCellState | null>(null);

  // 弹窗控制
  const {
    isOpen: isOpenSettingModal,
    onOpen: onOpenSettingModal,
    onClose: onCloseSettingModal
  } = useDisclosure();

  // 使用自定义 hooks 管理数据和逻辑
  const { treeData, loading, topLevelData, schoolData, refetchData } =
    useSubjectManagerData(semesterId);

  const { modalState, handleEditTeachers, handleModalClose } = useEditTeachers(
    treeData,
    schoolData,
    semesterId,
    refetchData
  );

  // 处理教师编辑操作
  const handleTeacherEdit = useCallback(
    (
      nodeId: string | null,
      grade: string,
      subject: string,
      role: 'leader' | 'phase_leader' | 'teacher' = 'leader',
      stage?: string
    ) => {
      const result = handleEditTeachers(role, nodeId, grade, subject, '', stage);
      if (result?.openModal) {
        onOpenSettingModal();
      }
    },
    [handleEditTeachers, onOpenSettingModal, onEditTeachers]
  );

  // 处理弹窗关闭
  const handleModalCloseWrapper = useCallback(
    async (submitted: boolean, selectedTmbIds?: number[], deptId?: string) => {
      await handleModalClose(submitted, selectedTmbIds, deptId);
      onCloseSettingModal();
      if (submitted) {
        refreshList();
      }
    },
    [handleModalClose, onCloseSettingModal, refreshList]
  );

  // 加载状态
  if (loading) {
    return (
      <Box w="100%" textAlign="center" py="50px">
        正在加载数据...
      </Box>
    );
  }

  // 空状态
  if (treeData.length === 0) {
    return <EmptyState semesterData={semesterData} />;
  }
  // 主要内容渲染
  return (
    <Box>
      <Box style={{ height: '100%' }}>
        {/* 总负责人表格 */}
        {topLevelData.topLevelSubjects.length > 0 && (
          <LeadershipTable
            subjects={topLevelData.topLevelSubjects}
            leaders={topLevelData.topLevelLeaders}
            treeData={treeData}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            onCellHover={setHoveredCell}
            onEditTeacher={handleTeacherEdit}
          />
        )}

        {/* 各学部年级表格 */}
        {schoolData.primary.length > 0 && (
          <GradeTable
            schools={schoolData.primary}
            title="小学部"
            subjects={topLevelData.topLevelSubjects}
            leaders={schoolData.primaryLeaders}
            treeData={treeData}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            onCellHover={setHoveredCell}
            onEditTeacher={handleTeacherEdit}
          />
        )}

        {schoolData.junior.length > 0 && (
          <GradeTable
            schools={schoolData.junior}
            title="初中部"
            subjects={topLevelData.topLevelSubjects}
            leaders={schoolData.juniorLeaders}
            treeData={treeData}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            onCellHover={setHoveredCell}
            onEditTeacher={handleTeacherEdit}
          />
        )}

        {schoolData.senior.length > 0 && (
          <GradeTable
            schools={schoolData.senior}
            title="高中部"
            subjects={topLevelData.topLevelSubjects}
            leaders={schoolData.seniorLeaders}
            treeData={treeData}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            onCellHover={setHoveredCell}
            onEditTeacher={handleTeacherEdit}
          />
        )}

        {schoolData.other.length > 0 && (
          <GradeTable
            schools={schoolData.other}
            title="其他"
            subjects={topLevelData.topLevelSubjects}
            leaders={schoolData.otherLeaders}
            treeData={treeData}
            hoveredCell={hoveredCell}
            highlightedTmbIds={highlightedTmbIds}
            onCellHover={setHoveredCell}
            onEditTeacher={handleTeacherEdit}
          />
        )}
      </Box>

      {/* 弹窗组件 */}
      {isOpenSettingModal && (
        <SettingSubjectsModal
          settingId={modalState.settingId}
          title={modalState.title}
          deptName={modalState.deptName}
          onClose={handleModalCloseWrapper}
          selectedUsers={modalState.selectedUsers}
          onSuccess={refreshList}
          deptId={modalState.settingId}
          semesterId={semesterId}
        />
      )}
    </Box>
  );
};

export default SubjectHeadManagement;
