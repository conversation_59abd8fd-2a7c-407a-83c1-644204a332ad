import MyTable from '@/components/MyTable';
import PageContainer from '@/components/PageContainer';
import { serviceSideProps } from '@/utils/i18n';
import {
  Box,
  Button,
  Flex,
  Text,
  Checkbox,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Textarea,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Spinner,
  VStack,
  HStack,
  useDisclosure
} from '@chakra-ui/react';
import { useRef, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  getResourcePage,
  updateTenantResource,
  deleteTenantResource,
  batchDeleteTenantResource
} from '@/api/tenant/tenant';
import SearchBar from './components/SearchBar';
import { Tag } from 'antd';
import { ResourceItem } from '@/types/personalCenter';
import { TenantResourcesParams } from '@/types/api/tenant/tenant';
import { MyTableRef } from '@/components/MyTable/types';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { vwDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { getFileMeta } from '@/api/file';
import { fileDecompression } from '@/api/resourceSquare';
import { useSystemStore } from '@/store/useSystemStore';
import { getToken } from '@/utils/auth';

const Resources = () => {
  const router = useRouter();
  const actionRef = useRef<MyTableRef<TenantResourcesParams, ResourceItem>>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<ResourceItem[]>([]);

  // 新增状态变量
  const [isOfflineModalOpen, setIsOfflineModalOpen] = useState(false);
  const [offlineReason, setOfflineReason] = useState('');
  const [offlineReasonError, setOfflineReasonError] = useState('');
  const [offlineMode, setOfflineMode] = useState<'single' | 'batch'>('single');
  const [offlineTarget, setOfflineTarget] = useState<ResourceItem | ResourceItem[] | null>(null);

  // 文件预览相关状态
  const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = useDisclosure();
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [previewLoading, setPreviewLoading] = useState(false);
  const [isCompressedFile, setIsCompressedFile] = useState(false);
  const [decompressedFiles, setDecompressedFiles] = useState<any[]>([]);
  const [selectedDecompressedFile, setSelectedDecompressedFile] = useState<any>(null);
  const { systemConfig } = useSystemStore();

  // 跳转到上传资源页面
  const handleUploadResource = () => {
    router.push('/personalCenter/uploadFile?from=tenant');
  };
  const handleToDraftsPage = () => {
    // 在资源管理页面
    router.push('/personalCenter/draftsPage?from=tenant');
  };

  // 添加调试信息
  useEffect(() => {
    console.log('Resources 组件已挂载');
    console.log('actionRef.current:', actionRef.current);
  }, []);

  useEffect(() => {
    if (actionRef.current) {
      console.log('MyTable 实例信息:', {
        current: actionRef.current.current,
        size: actionRef.current.size,
        total: actionRef.current.total,
        query: actionRef.current.query
      });
    }
  }, [actionRef.current?.current, actionRef.current?.size, actionRef.current?.total]);
  // 计算按钮状态
  const getButtonStates = () => {
    if (selectedRows.length === 0) {
      return {
        canBatchOnline: false,
        canBatchOffline: false,
        canBatchDelete: false
      };
    }

    // 检查所有选中资源的状态
    const allOffline = selectedRows.every((row) => row.status === 2); // 所有都是下架状态
    const allOnline = selectedRows.every((row) => row.status === 1); // 所有都是上架状态

    return {
      canBatchOnline: allOffline, // 只有全部下架时才能批量上架
      canBatchOffline: allOnline, // 只有全部上架时才能批量下架
      canBatchDelete: allOnline // 只有全部上架时才能批量删除
    };
  };

  const { canBatchOnline, canBatchOffline, canBatchDelete } = getButtonStates();

  // 安全渲染函数，确保不会渲染对象
  const safeRender = (value: any, fallback: string = '未知'): string => {
    if (value === null || value === undefined) return fallback;
    if (typeof value === 'object') return fallback;
    return String(value);
  };

  // 生成OnlyOffice预览URL
  const generateOnlyOfficeUrl = (fileUrl: string, fileExtension: string, title: string) => {
    try {
      const token = getToken();
      const params = new URLSearchParams({
        fileType: fileExtension,
        key: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        title: title || '文档预览',
        url: encodeURIComponent(fileUrl)
      });

      if (token) {
        params.set('token', token);
      }

      return `/onlyoffice/index.html?${params.toString()}`;
    } catch (error) {
      console.error('生成OnlyOffice URL时出错:', error);
      return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`;
    }
  };

  // 生成文件预览URL
  const generatePreviewUrl = async (fileUrl: string, fileName: string) => {
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
    const browserAccessibleFileTypes = [
      'pdf',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'svg',
      'txt',
      'html',
      'htm'
    ];
    const officeFileTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];

    if (browserAccessibleFileTypes.includes(fileExtension)) {
      return fileUrl;
    } else if (officeFileTypes.includes(fileExtension)) {
      const onlyOfficeConfig = systemConfig?.onlyOfficeConfig;
      if (onlyOfficeConfig) {
        return generateOnlyOfficeUrl(fileUrl, fileExtension, fileName);
      } else {
        return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`;
      }
    }

    return fileUrl;
  };

  const onViewFile = async (record: ResourceItem) => {
    console.log('查看文件:', record);

    if (!record.fileKey) {
      Toast.error('文件标识不存在');
      return;
    }

    try {
      setPreviewLoading(true);
      onPreviewOpen();

      // 获取文件元数据
      const fileInfo = await getFileMeta(record.fileKey);

      if (!fileInfo.fileUrl) {
        Toast.error('无法获取文件URL');
        return;
      }

      const fileName = record.fileName || record.title || 'unknown';
      const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
      const compressedFileTypes = ['zip', '7z', 'rar'];

      if (compressedFileTypes.includes(fileExtension)) {
        // 处理压缩文件
        setIsCompressedFile(true);

        try {
          const decompressionResult = await fileDecompression({
            objectKey: record.fileKey
          });

          setDecompressedFiles(decompressionResult || []);

          // 自动预览第一个文件
          if (decompressionResult && decompressionResult.length > 0) {
            const firstFile = decompressionResult[0];
            setSelectedDecompressedFile(firstFile);
            const previewUrl = await generatePreviewUrl(firstFile.fileUrl, firstFile.fileName);
            setPreviewUrl(previewUrl);
          }
        } catch (error) {
          console.error('文件解压失败:', error);
          Toast.error('文件解压失败');
        }
      } else {
        // 处理普通文件
        setIsCompressedFile(false);
        setDecompressedFiles([]);
        setSelectedDecompressedFile(null);

        const previewUrl = await generatePreviewUrl(fileInfo.fileUrl, fileName);
        setPreviewUrl(previewUrl);
      }
    } catch (error) {
      console.error('获取文件信息失败:', error);
      Toast.error('获取文件信息失败');
    } finally {
      setPreviewLoading(false);
    }
  };

  // 处理解压文件选择
  const handleDecompressedFileSelect = async (file: any) => {
    setSelectedDecompressedFile(file);
    setPreviewLoading(true);

    try {
      const previewUrl = await generatePreviewUrl(file.fileUrl, file.fileName);
      setPreviewUrl(previewUrl);
    } catch (error) {
      console.error('生成预览URL失败:', error);
      Toast.error('生成预览URL失败');
    } finally {
      setPreviewLoading(false);
    }
  };

  // 关闭预览弹窗
  const handlePreviewClose = () => {
    onPreviewClose();
    setPreviewUrl('');
    setIsCompressedFile(false);
    setDecompressedFiles([]);
    setSelectedDecompressedFile(null);
    setPreviewLoading(false);
  };

  // 上架资源 (设置status=1)
  const onOnlineResource = (record: ResourceItem) => {
    console.log([Number(record.id)]);

    MessageBox.confirm({
      title: '上架提示',
      content: '确认上架该资源吗？上架后用户可以正常访问该资源。',
      onOk: async () => {
        try {
          await updateTenantResource({
            resourceIds: [Number(record.id)],
            status: 1,
            rejection: ''
          });
          Toast.success('上架成功');
          // 刷新表格
          actionRef.current?.reload();
        } catch (error) {
          Toast.error('上架失败');
        }
      }
    });
  };

  // 打开下架弹窗
  const openOfflineModal = (target: ResourceItem | ResourceItem[], mode: 'single' | 'batch') => {
    setOfflineMode(mode);
    setOfflineTarget(target);
    setOfflineReason('');
    setOfflineReasonError('');
    setIsOfflineModalOpen(true);
  };

  // 关闭下架弹窗
  const closeOfflineModal = () => {
    setIsOfflineModalOpen(false);
    setOfflineReason('');
    setOfflineReasonError('');
    setOfflineTarget(null);
  };

  // 处理下架理由输入变化
  const handleOfflineReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setOfflineReason(value);
    if (value.trim()) {
      setOfflineReasonError('');
    }
  };

  // 确认下架
  const handleConfirmOffline = async () => {
    if (!offlineReason.trim()) {
      setOfflineReasonError('下架理由不能为空');
      return;
    }

    try {
      let resourceIds: number[] = [];
      let successMessage = '';

      if (offlineMode === 'single' && offlineTarget) {
        resourceIds = [Number((offlineTarget as ResourceItem).id)];
        successMessage = '下架成功';
      } else if (offlineMode === 'batch' && offlineTarget) {
        resourceIds = (offlineTarget as ResourceItem[]).map((record) => Number(record.id));
        successMessage = `成功下架 ${resourceIds.length} 个资源`;
      }

      await updateTenantResource({
        resourceIds,
        status: 2, // 2=下架
        rejection: offlineReason.trim()
      });

      Toast.success(successMessage);
      closeOfflineModal();

      // 清空选择
      if (offlineMode === 'batch') {
        setSelectedRowKeys([]);
        setSelectedRows([]);
      }

      // 刷新表格
      actionRef.current?.reload();
    } catch (error) {
      Toast.error(offlineMode === 'single' ? '下架失败' : '批量下架失败');
    }
  };

  // 下架资源 (设置status=2)
  const onOfflineResource = (record: ResourceItem) => {
    openOfflineModal(record, 'single');
  };

  // 删除资源
  const onDeleteResource = (record: ResourceItem) => {
    MessageBox.confirm({
      title: '删除提示',
      content: '删除当前资源不可恢复，确定删除当前资源吗？',
      onOk: async () => {
        try {
          await deleteTenantResource({
            id: Number(record.id)
          });
          Toast.success('删除成功');
          // 刷新表格
          actionRef.current?.reload();
        } catch (error) {
          Toast.error('删除失败');
        }
      }
    });
  };

  // 批量上架
  const onBatchOnline = () => {
    if (selectedRows.length === 0) {
      Toast.error('请选择要上架的资源');
      return;
    }

    // 检查是否所有选中的资源都是下架状态
    const hasOnlineResource = selectedRows.some((row) => row.status === 1);
    if (hasOnlineResource) {
      Toast.error('只能对下架状态的资源进行批量上架操作');
      return;
    }

    MessageBox.confirm({
      title: '批量上架提示',
      content: `确认上架选中的 ${selectedRows.length} 个资源吗？上架后用户可以正常访问这些资源。`,
      onOk: async () => {
        try {
          // 调用updateTenantResource接口，传入所有选中的资源ID
          await updateTenantResource({
            resourceIds: selectedRows.map((record) => Number(record.id)),
            status: 1, // 1=上架
            rejection: ''
          });
          Toast.success(`成功上架 ${selectedRows.length} 个资源`);
          // 清空选择
          setSelectedRowKeys([]);
          setSelectedRows([]);
          // 刷新表格
          actionRef.current?.reload();
        } catch (error) {
          Toast.error('批量上架失败');
        }
      }
    });
  };

  // 批量下架
  const onBatchOffline = () => {
    if (selectedRows.length === 0) {
      Toast.error('请选择要下架的资源');
      return;
    }

    // 检查是否所有选中的资源都是上架状态
    const hasOfflineResource = selectedRows.some((row) => row.status === 2);
    if (hasOfflineResource) {
      Toast.error('只能对上架状态的资源进行批量下架操作');
      return;
    }

    openOfflineModal(selectedRows, 'batch');
  };

  // 批量删除
  const onBatchDelete = () => {
    if (selectedRows.length === 0) {
      Toast.error('请选择要删除的资源');
      return;
    }

    // 检查是否所有选中的资源都是上架状态
    const hasOfflineResource = selectedRows.some((row) => row.status === 2);
    if (hasOfflineResource) {
      Toast.error('只能对上架状态的资源进行批量删除操作');
      return;
    }

    MessageBox.confirm({
      title: '批量删除提示',
      content: `确认删除选中的 ${selectedRows.length} 个资源吗？删除后将无法恢复。`,
      onOk: async () => {
        try {
          // 调用updateTenantResource接口，传入所有选中的资源ID
          await batchDeleteTenantResource({
            resourceIds: selectedRows.map((record) => Number(record.id))
          });
          Toast.success(`成功删除 ${selectedRows.length} 个资源`);
          // 清空选择
          setSelectedRowKeys([]);
          setSelectedRows([]);
          // 刷新表格
          actionRef.current?.reload();
        } catch (error) {
          Toast.error('批量删除失败');
        }
      }
    });
  };

  // 自定义样式
  const statusStyles = {
    notEnabled: {
      color: '#000',
      display: 'flex',
      alignItems: 'center',
      opacity: 0.6
    },
    enabled: {
      color: '#00B42A',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#F53F3F',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: (
        <Checkbox
          colorScheme="purple"
          isChecked={
            selectedRowKeys.length > 0 && selectedRowKeys.length === actionRef.current?.data?.length
          }
          isIndeterminate={
            selectedRowKeys.length > 0 &&
            selectedRowKeys.length < (actionRef.current?.data?.length || 0)
          }
          onChange={(e) => {
            const allData = actionRef.current?.data || [];
            if (e.target.checked) {
              // 全选
              setSelectedRowKeys(allData.map((item) => String(item.id)));
              setSelectedRows(allData);
            } else {
              // 取消全选
              setSelectedRowKeys([]);
              setSelectedRows([]);
            }
          }}
        />
      ),
      dataIndex: 'checkbox',
      key: 'checkbox',
      width: 50,
      render: (_: any, record: ResourceItem) => (
        <Checkbox
          colorScheme="purple"
          isChecked={selectedRowKeys.includes(String(record.id))}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedRowKeys([...selectedRowKeys, String(record.id)]);
              setSelectedRows([...selectedRows, record]);
            } else {
              setSelectedRowKeys(selectedRowKeys.filter((key) => key !== String(record.id)));
              setSelectedRows(selectedRows.filter((row) => row.id !== record.id));
            }
          }}
        />
      )
    },
    {
      title: '文件名称',
      key: 'fileName',
      width: 280,
      render: (_: any, record: ResourceItem) => (
        <Flex alignItems="center">
          <Box
            w="20px"
            h="20px"
            bg="#8B5CF6"
            borderRadius="4px"
            mr="8px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text color="white" fontSize="12px">
              📄
            </Text>
          </Box>
          <Text fontSize="14px" color="#333">
            {safeRender(record.fileName || record.title, '未知文件')}
          </Text>
        </Flex>
      )
    },
    {
      title: '上传人',
      key: 'tmbName',
      width: 100,
      render: (_: any, record: ResourceItem) => (
    
        <Text fontSize="14px" color="#333">
          {safeRender(record.tmbName)}
        </Text>
      )
    },
    {
      title: '文件大小',
      key: 'fileSize',
      width: 100,
      render: (_: any, record: ResourceItem) => {

        const size = record.fileSize;
        if (!size || typeof size === 'object')
          return (
            <Text fontSize="14px" color="#333">
              -
            </Text>
          );

        // 如果是数字，假设单位是字节，转换为可读格式
        if (typeof size === 'number') {
          if (size < 1024)
            return (
              <Text fontSize="14px" color="#333">
                {size}B
              </Text>
            );
          if (size < 1024 * 1024)
            return (
              <Text fontSize="14px" color="#333">
                {(size / 1024).toFixed(1)}KB
              </Text>
            );
          if (size < 1024 * 1024 * 1024)
            return (
              <Text fontSize="14px" color="#333">
                {(size / (1024 * 1024)).toFixed(1)}MB
              </Text>
            );
          return (
            <Text fontSize="14px" color="#333">
              {(size / (1024 * 1024 * 1024)).toFixed(1)}GB
            </Text>
          );
        }

        return (
          <Text fontSize="14px" color="#333">
            {safeRender(size)}
          </Text>
        );
      }
    },
    {
      title: '资源类型',
      key: 'resourceTypeName',
      width: 120,
      render: (_: any, record: ResourceItem) => {
        const resourceType = record.resourceTypeName;
        return <Tag style={{ borderRadius: '4px' }}>{safeRender(resourceType)}</Tag>;
      }
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render: (_: any, record: ResourceItem) => {
        const status = record.status;
        switch (status) {
          case 0:
            return (
              <>
                <Box style={statusStyles.notEnabled}>
                  <Box
                    style={{
                      ...statusStyles.dot,
                      backgroundColor: statusStyles.notEnabled.color
                    }}
                  />
                  未激活
                </Box>
              </>
            );
          case 1:
            return (
              <>
                <Box style={statusStyles.enabled}>
                  <Box
                    style={{
                      ...statusStyles.dot,
                      backgroundColor: statusStyles.enabled.color
                    }}
                  />
                  上架
                </Box>
              </>
            );
          case 2:
            return (
              <>
                <Box style={statusStyles.disabled}>
                  <Box
                    style={{
                      ...statusStyles.dot,
                      backgroundColor: statusStyles.disabled.color
                    }}
                  />
                  下架
                </Box>
              </>
            );
          default:
            return <>传入数值错误</>;
        }
      }
    },
    {
      title: '上传时间',
      key: 'createTime',
      width: 180,
      render: (_: any, record: ResourceItem) => {
        const createTime = record.createTime || record.updateTime;
        if (!createTime || typeof createTime === 'object')
          return (
            <Text fontSize="14px" color="#333">
              未知
            </Text>
          );

        try {
          // 处理时间戳（毫秒或秒）
          let date: Date;
          if (typeof createTime === 'number') {
            // 如果是时间戳，判断是秒还是毫秒
            date = new Date(createTime > 1000000000000 ? createTime : createTime * 1000);
          } else if (typeof createTime === 'string') {
            date = new Date(createTime);
          } else {
            return (
              <Text fontSize="14px" color="#333">
                未知
              </Text>
            );
          }

          // 检查日期是否有效
          if (isNaN(date.getTime())) {
            return (
              <Text fontSize="14px" color="#333">
                未知
              </Text>
            );
          }

          // 格式化为 yyyy-MM-dd HH:mm:ss
          const formatDateTime = (date: Date): string => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          };

          return (
            <Text fontSize="14px" color="#333">
              {formatDateTime(date)}
            </Text>
          );
        } catch (error) {
          return (
            <Text fontSize="14px" color="#333">
              未知
            </Text>
          );
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_: any, record: ResourceItem) => {
        const status = record.status;
        const isOnline = status === 1; // 1表示上架状态
        const isOffline = status === 2; // 2表示下架状态

        return (
          <Flex gap="8px">
            <Button
              size="sm"
              variant="outline"
              color="#7D4DFF"
              border="1px solid #7D4DFF"
              onClick={() => onViewFile(record)}
            >
              查看文件
            </Button>

            {/* 根据状态显示上架/下架按钮 */}
            {isOffline && (
              <Button
                size="sm"
                variant="outline"
                border={'1px solid #00B42A'}
                width={vwDims(84)}
                h={vwDims(33)}
                color="#00B42A"
                onClick={() => onOnlineResource(record)}
              >
                上架
              </Button>
            )}

            {isOnline && (
              <Button
                size="sm"
                variant="outline"
                border={'1px solid #F53F3F'}
                width={vwDims(84)}
                h={vwDims(33)}
                color={'#F53F3F'}
                onClick={() => onOfflineResource(record)}
              >
                下架
              </Button>
            )}

            {/* 删除按钮：状态为2时可用，状态为1时禁用 */}
            <Button
              size="sm"
              variant="outline"
              width={vwDims(84)}
              h={vwDims(33)}
              colorScheme={isOnline ? 'gray' : 'gray'}
              isDisabled={isOnline} // 上架状态时禁用删除
              border={'1px solid #D1D5DB'}
              onClick={() => onDeleteResource(record)}
            >
              删除
            </Button>
          </Flex>
        );
      }
    }
  ];

  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)" border="2px solid #FFFFFF">
      <Flex w="100%" h="100%" flexDir="column">
        {/* 顶部标题和按钮 */}
        <Flex justifyContent="space-between" alignItems="center" mb="16px" mt="16px" ml={'20px'}>
          <Text fontSize="20px" fontWeight="bold" color="#333">
            资源管理
          </Text>
          <Flex gap="12px">
            <Button
              variant="outline"
              colorScheme="gray"
              size="sm"
              px={vwDims(16)}
              w={vwDims(163)}
              height={vwDims(36)}
              border={'1px solid #7D4DFF'}
              borderRadius={vwDims(6)}
              color={'#7D4DFF'}
              onClick={handleToDraftsPage}
            >
              草稿箱
            </Button>
            <Button
              size="sm"
              px={vwDims(16)}
              mr={vwDims(16)}
              w={vwDims(163)}
              height={vwDims(36)}
              borderRadius={vwDims(6)}
              onClick={handleUploadResource}
            >
              + 上传资源
            </Button>
          </Flex>
        </Flex>

        <MyTable
          columns={columns}
          api={getResourcePage}
          rowKey="id"
          ref={actionRef}
          defaultQuery={{
            title: '',
            resourceTypeId: 0,
            status: 0,
            endTime: '',
            startTime: '',
            tmbIds: []
          }}
          pageConfig={{
            showPaginate: true,
            showSizeChanger: true,
            showQuickJumper: true,
            defaultCurrent: 1,
            defaultSize: 10
          }}
          cacheKey="tenant-resources-table"
          headerConfig={{
            showHeader: true,
            HeaderComponent: ({ onSearch, query, defaultQuery, tableInstance }) => (
              <Flex direction="column" gap="16px">
                <SearchBar
                  onSearch={onSearch}
                  query={query}
                  defaultQuery={defaultQuery}
                  tableInstance={tableInstance}
                />

                {/* 批量操作按钮 */}
                {selectedRows.length > 0 && (
                  <Flex gap="12px" alignItems="center" mb={'16px'} mt={'-5px'}>
                    <Text fontSize="14px" color="#666">
                      已选择 {selectedRows.length} 项
                    </Text>
                    <Button
                      size="sm"
                      colorScheme="green"
                      variant="outline"
                      onClick={onBatchOnline}
                      isDisabled={!canBatchOnline}
                      opacity={canBatchOnline ? 1 : 0.5}
                    >
                      批量上架
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="red"
                      variant="outline"
                      onClick={onBatchOffline}
                      isDisabled={!canBatchOffline}
                      opacity={canBatchOffline ? 1 : 0.5}
                    >
                      批量下架
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="red"
                      variant="outline"
                      onClick={onBatchDelete}
                      isDisabled={!canBatchDelete}
                      opacity={canBatchDelete ? 1 : 0.5}
                    >
                      批量删除
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="gray"
                      variant="outline"
                      onClick={() => {
                        setSelectedRowKeys([]);
                        setSelectedRows([]);
                      }}
                    >
                      清空选择
                    </Button>
                  </Flex>
                )}
              </Flex>
            )
          }}
          // tableConfig={{
          //   pagination: true,
          //   scroll: { x: 1200 }
          // }}
        />

        {/* 下架理由弹窗 */}
        <Modal isOpen={isOfflineModalOpen} onClose={closeOfflineModal} size="md">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{offlineMode === 'single' ? '下架资源' : '批量下架资源'}</ModalHeader>
            <ModalBody>
              <FormControl isInvalid={!!offlineReasonError}>
                <FormLabel>下架理由</FormLabel>
                <Textarea
                  value={offlineReason}
                  onChange={handleOfflineReasonChange}
                  placeholder="请输入下架理由..."
                  rows={4}
                  resize="vertical"
                />
                <FormErrorMessage>{offlineReasonError}</FormErrorMessage>
              </FormControl>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={closeOfflineModal}>
                取消
              </Button>
              <Button colorScheme="red" onClick={handleConfirmOffline}>
                确认
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 文件预览弹窗 */}
        <Modal isOpen={isPreviewOpen} onClose={handlePreviewClose} size="6xl" isCentered>
          <ModalOverlay />
          <ModalContent maxW={vwDims(1200)} h="80vh">
            <ModalHeader fontSize={vwDims(16)} fontWeight="600" color="#1D2129">
              文件预览
            </ModalHeader>

            <ModalBody p={0}>
              {isCompressedFile ? (
                // 压缩文件预览：左侧文件列表，右侧预览
                <Flex h="calc(80vh - 120px)">
                  {/* 左侧文件列表 */}
                  <Box w="30%" borderRight="1px solid #E5E6EB" p={vwDims(16)}>
                    <Text fontSize={vwDims(14)} fontWeight="500" color="#1D2129" mb={vwDims(16)}>
                      解压文件列表
                    </Text>
                    <VStack spacing={vwDims(8)} align="stretch">
                      {decompressedFiles.map((file, index) => (
                        <Box
                          key={index}
                          p={vwDims(12)}
                          borderRadius={vwDims(6)}
                          border="1px solid #E5E6EB"
                          bg={
                            selectedDecompressedFile?.fileName === file.fileName
                              ? '#F7F5FF'
                              : 'white'
                          }
                          cursor="pointer"
                          _hover={{ bg: '#F7F8FA' }}
                          onClick={() => handleDecompressedFileSelect(file)}
                        >
                          <Text fontSize={vwDims(12)} color="#1D2129" noOfLines={2}>
                            {file.fileName}
                          </Text>
                        </Box>
                      ))}
                    </VStack>
                  </Box>

                  {/* 右侧预览区域 */}
                  <Box flex="1" p={vwDims(16)}>
                    {previewLoading ? (
                      <Flex justify="center" align="center" h="100%">
                        <Spinner size="lg" color="#7D4DFF" />
                        <Text ml={4}>正在加载预览...</Text>
                      </Flex>
                    ) : previewUrl ? (
                      <iframe
                        src={previewUrl}
                        width="100%"
                        height="100%"
                        style={{
                          border: 'none',
                          borderRadius: vwDims(8)
                        }}
                        title="文件预览"
                      />
                    ) : (
                      <Flex justify="center" align="center" h="100%">
                        <Text color="#86909C">请选择要预览的文件</Text>
                      </Flex>
                    )}
                  </Box>
                </Flex>
              ) : (
                // 普通文件预览：全屏预览
                <Box h="calc(80vh - 120px)" p={vwDims(16)}>
                  {previewLoading ? (
                    <Flex justify="center" align="center" h="100%">
                      <Spinner size="lg" color="#7D4DFF" />
                      <Text ml={4}>正在加载预览...</Text>
                    </Flex>
                  ) : previewUrl ? (
                    <iframe
                      src={previewUrl}
                      width="100%"
                      height="100%"
                      style={{
                        border: 'none',
                        borderRadius: vwDims(8)
                      }}
                      title="文件预览"
                    />
                  ) : (
                    <Flex justify="center" align="center" h="100%">
                      <Text color="#86909C">无法预览此文件</Text>
                    </Flex>
                  )}
                </Box>
              )}
            </ModalBody>

            <ModalFooter>
              <Button onClick={handlePreviewClose}>关闭</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Flex>
    </PageContainer>
  );
};

export async function getServerSideProps(content: any) {
  return {
    props: { ...(await serviceSideProps(content)) }
  };
}

export default Resources;
