import React from 'react';
import { Box } from '@chakra-ui/react';
import { TmbUser } from '@/types/api/tenant/teamManagement/teach';
import SvgIcon from '@/components/SvgIcon';
import { HoveredCellState } from '../types';

interface TeacherCellProps {
  cellId: string;
  teacherInfo: TmbUser[];
  hoveredCell: HoveredCellState | null;
  highlightedTmbIds: number[];
  backgroundColor?: string;
  canEdit?: boolean; // 是否可以编辑（用于区分"未设置"和"无法设置"）
}

/**
 * 渲染教师信息
 */
const renderTeachers = (teacherInfo: TmbUser[], highlightedTmbIds: number[]) => {
  return teacherInfo.map((t, index) => (
    <React.Fragment key={t.tmbId}>
      <span
        style={{
          color: highlightedTmbIds.includes(Number(t.tmbId)) ? 'red' : 'inherit'
        }}
      >
        {t.userName}
      </span>
      {index < teacherInfo.length - 1 && ', '}
    </React.Fragment>
  ));
};

/**
 * 教师单元格组件
 */
const TeacherCell: React.FC<TeacherCellProps> = ({
  cellId,
  teacherInfo,
  hoveredCell,
  highlightedTmbIds,
  backgroundColor = 'transparent',
  canEdit = true
}) => {
  const isHovered = hoveredCell?.id === cellId;

  // 根据是否可编辑决定显示内容和样式
  const displayText =
    teacherInfo.length > 0
      ? renderTeachers(teacherInfo, highlightedTmbIds)
      : canEdit
        ? '未设置'
        : '无法设置';

  const textColor = canEdit ? '#606266' : '#c0c4cc'; // 不可编辑时使用灰色

  return (
    <Box
      position="relative"
      fontSize="12px"
      color={textColor}
      textAlign="center"
      style={{
        backgroundColor: backgroundColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%'
      }}
    >
      {displayText}
      {/* 只有可编辑且有悬停时才显示编辑图标 */}
      {canEdit && (
        <span
          style={{
            position: 'absolute',
            right: '5px',
            top: '50%',
            transform: 'translateY(-50%)',
            opacity: isHovered ? 1 : 0,
            transition: 'opacity 0.3s'
          }}
        >
          <SvgIcon name="editIcon" w="16px" h="16px" cursor="pointer" />
        </span>
      )}
    </Box>
  );
};

export default TeacherCell;
