import {
  CollaborationTypeEnum,
  DatasetTypeEnum,
  PermissionKeyEnum,
  SearchScoreTypeEnum
} from '@/constants/api/dataset';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';
import { LLMModelItemType } from '@/fastgpt/global/core/ai/model';

export type DatasetItemType = {
  id: string;
  finalDatasetId: string;
  parentId: string;
  finalParentId: string;
  avatarUrl: string;
  vectorModel?: string;
  agentModel?: string;
  name: string;
  intro: string;
  permission: PermissionTypeEnum;
  type: `${DatasetTypeEnum}`;
  agentModuleJson?: string;
  authority?: PermissionKeyEnum;
  source: DataSource;
  collaborationType: CollaborationTypeEnum;
};

export type SearchDataResponseItemType = {
  id: string;
  datasetId: string;
  collectionId: string;
  sourceName: string;
  sourceId?: string;
  q: string;
  a: string;
  chunkIndex: number;
  score: { type: `${SearchScoreTypeEnum}`; value: number; index: number }[];
};

export type CreateDatasetProps = {
  parentId?: string;
  finalParentId?: string;
  name: string;
  intro: string;
  avatarUrl: string;
  permission?: PermissionTypeEnum;
  vectorModel?: string;
  agentModel?: string;
  type: `${DatasetTypeEnum}`;
};

export type UpdateDatasetProps = {
  id: string;
  parentId?: string;
  finalParentId?: string;
  name?: string;
  intro?: string;
  avatarUrl?: string;
  permission?: PermissionTypeEnum;
  vectorModel?: string;
  agentModel?: string;
  type?: `${DatasetTypeEnum}`;
};

export type UpdateDatasetProps = {
  id: string;
  parentId?: string;
  finalParentId?: string;
  name?: string;
  avatarUrl?: string;
  intro?: string;
  permission?: PermissionTypeEnum;
  agentModel?: LLMModelItemType;
};

export type PermissionValueType = number;

export type PermissionListType<T = {}> = Record<
  T | PermissionKeyEnum,
  {
    name: string;
    description: string;
    value: PermissionValueType;
    checkBoxType: 'single' | 'multiple';
  }
>;

// 定义租户知识库用户创建请求类型
export interface TenantDatasetUserCreateRequest {
  authority: PermissionKeyEnum;
  tenantDatasetId: string;
  tenantId?: string;
  tmbIds: string[];
}

// 定义详情请求类型
export interface DetailRequest {
  id: string;
  tmbId?: string;
}

// 定义租户知识库用户分页请求类型
export interface TenantDatasetUserPageRequest {
  ascs?: string;
  current?: number;
  descs?: string;
  searchKey?: string;
  size?: number;
  tenantDatasetId?: string;
  tenantId?: string;
  username?: string;
}

// 定义租户知识库用户出参类型
export interface TenantDatasetUsersVO {
  authority: PermissionKeyEnum;
  avatar: string;
  avatarFile: File;
  createTime: string;
  id: string;
  isDeleted: number;
  tenantDatasetId: number;
  tenantId: number;
  tmbId: string;
  updateTime: string;
  username: string;
}

// 定义分页数据类型
export interface PagingData<T> {
  current?: number;
  size?: number;
  records: T[];
  pages: number;
  total?: number;
}

// 定义租户成员出参类型
export interface TenantUserVO {
  avatar: string;
  avatarFile: File;
  createTime: string;
  deptId: number;
  deptName: string;
  dingUserId: string;
  email: string;
  id: string;
  isDeleted: number;
  phone: string;
  qywxUserId: string;
  roleId: number;
  roleName: string;
  roleType: number;
  status: number;
  tenantId: number;
  updateTime: string;
  userId: string;
  username: string;
}

// 定义租户知识库用户更新权限请求类型
export interface TenantDatasetUserUpdateAuthorityRequest {
  authority: PermissionKeyEnum;
  id: string;
}

// 租户知识库入参-列表
export interface TenantDatasetListRequest {
  parentId?: number;
  permission?: number;
  source?: number;
  tenantId?: number;
  tmbId?: string;
  type?: `${DatasetTypeEnum}`;
}

// 租户知识库出参
export interface TenantDatasetResp {
  agentModel: LLMModelItemType;
  agentModuleJson: string;
  authority: PermissionKeyEnum;
  avatarUrl: string;
  collaborationType: CollaborationTypeEnum;
  createTime: string;
  createUsername: string;
  finalDatasetId: string;
  finalParentId: string;
  id: string;
  intro: string;
  isDeleted: number;
  name: string;
  parentId: number;
  permission: number;
  sort: number;
  source: DataSource;
  tenantId: number;
  tenantName: string;
  tmbId: number;
  type: `${DatasetTypeEnum}`;
  updateTime: string;
  updateUsername: string;
  userName: string;
  vectorModel: LLMModelItemType;
  vectorModelJson: string;
}
