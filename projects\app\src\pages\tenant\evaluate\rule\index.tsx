import React, { memo, useState, useEffect } from 'react';
import PageContainer from '@/components/PageContainer';
import { Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { serviceSideProps } from '@/utils/i18n';
import { useTranslation } from 'react-i18next';

// 导入新的组件
import RulesTab from './components/RulesTab';
import DimensionsTab from './components/DimensionsTab';
import IndicatorsTab from './components/IndicatorsTab';
import { respDims } from '@/utils/chakra';

const tabs = [
  { name: '评价规则', value: 'rules' },
  { name: '评价维度和项目', value: 'dimensions' }
] as const;

type TabValue = (typeof tabs)[number]['value'];

export type RulesTabProps = {
  TabCompoent: React.ComponentType<any>;
};

interface EvaluationManagementProps {
  initialTab: TabValue;
  appName: string;
  tenantName: string;
  userName: string;
}

const EvaluationManagement: React.FC<EvaluationManagementProps> = ({
  initialTab,
  appName,
  tenantName,
  userName
}) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState<TabValue>(initialTab);
  const router = useRouter();

  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            {...(tab.value === currentTab
              ? {
                  color: 'primary.500',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: 'primary.500'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 'rules':
        return <RulesTab TabCompoent={TabRender} />;
      case 'dimensions':
        return <DimensionsTab TabCompoent={TabRender} />;

      default:
        return null;
    }
  };

  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)" border="2px solid #FFFFFF">
      <Flex w="100%" h="100%" flexDir="column">
        {renderTabContent()}
      </Flex>
    </PageContainer>
  );
};

export const getServerSideProps = async (context: any) => {
  return {
    props: {
      initialTab: (context.query?.currentTab as TabValue) || 'rules',
      appName: context.query?.appName || '',
      tenantName: context.query?.tenantName || '',
      userName: context.query?.userName || '',
      ...(await serviceSideProps(context))
    }
  };
};

export default memo(EvaluationManagement);
