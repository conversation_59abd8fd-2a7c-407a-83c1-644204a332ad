import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, Flex, Button, BoxProps, ModalBody } from '@chakra-ui/react';
import { Select } from 'antd';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useTranslation } from 'next-i18next';
import { getTeacherListPage } from '@/api/tenant/teamManagement/administration';
import MyModal from '@/components/MyModal';
import { TeacherInfo } from '@/types/api/tenant/teamManagement/administration';

interface FormData {
  userIds: { name: string; id: string; tmbId?: string }[];
}

const MAX_COUNT = 5;

const SettingModal = ({
  settingId,
  title,
  deptName,
  onClose,
  onSuccess,
  selectedTmbIds,
  ...props
}: {
  settingId: string;
  title: string;
  deptName: string;
  selectedTmbIds: string[];
  onClose: (submited: boolean, settingId: string, selectedTmbIds: number[]) => void;
  onSuccess: () => void | Promise<void>;
} & BoxProps) => {
  const { t } = useTranslation();
  const [teachers, setTeachers] = useState<TeacherInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      userIds: []
    }
  });

  // 处理表单提交
  const onSubmit: SubmitHandler<FormData> = (data) => {
    // 提取选中教师的tmbId
    const selectedUserTmbIds = data.userIds
      .map((user) => {
        const matchedTeacher = teachers.find((teacher) => String(teacher.id) === user.id);
        return matchedTeacher ? Number(matchedTeacher.tmbId) : null;
      })
      .filter(Boolean) as number[];

    // 调用父组件方法关闭弹窗，传递选中的教师ID
    onClose(true, settingId, selectedUserTmbIds);
    onSuccess();
  };

  // 获取教师列表并设置初始选中项
  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        setLoading(true);
        const response = await getTeacherListPage();

        // 处理API返回的数据
        let teacherList: TeacherInfo[] = [];
        if (response?.data && Array.isArray(response.data)) {
          teacherList = response.data;
        } else if (Array.isArray(response)) {
          teacherList = response;
        }

        setTeachers(teacherList);

        // 根据selectedTmbIds匹配教师并设置选中项
        if (selectedTmbIds?.length > 0) {
          const selectedTeachers = teacherList
            .filter((teacher) => selectedTmbIds.includes(String(teacher.tmbId)))
            .map((teacher) => ({
              name: teacher.name,
              id: String(teacher.id),
              tmbId: String(teacher.tmbId)
            }));

          if (selectedTeachers.length > 0) {
            setValue('userIds', selectedTeachers);
          }
        }
      } catch (error) {
        console.error('获取教师数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeachers();
  }, [selectedTmbIds, setValue, settingId]);

  return (
    <MyModal isOpen={true} title={title} isCentered>
      <ModalBody>
        <Box p="20px" {...props}>
          {deptName && (
            <FormControl>
              <Flex alignItems="center" whiteSpace="nowrap">
                <FormLabel color="#4E5969" fontSize="14px">
                  {deptName}
                </FormLabel>
              </Flex>
            </FormControl>
          )}

          <FormControl mt="14px" isInvalid={!!errors.userIds}>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <Flex flexDirection="column">
                <Controller
                  name="userIds"
                  control={control}
                  rules={{ required: '请选择用户' }}
                  render={({ field }) => {
                    const currentValues = field.value.map((user) => user.id);

                    return (
                      <Select
                        mode="multiple"
                        showSearch
                        maxCount={MAX_COUNT}
                        value={currentValues}
                        onChange={(selectedIds) => {
                          const selectedUsers = teachers
                            .filter((teacher) => selectedIds.includes(String(teacher.id)))
                            .map((teacher) => ({
                              name: teacher.name,
                              id: String(teacher.id),
                              tmbId: String(teacher.tmbId)
                            }));

                          field.onChange(selectedUsers);
                        }}
                        dropdownStyle={{ zIndex: 2000 }}
                        style={{ width: '460px', height: '38px' }}
                        placeholder="请选择用户"
                        loading={loading}
                        filterOption={(input, option) => {
                          if (!option) return false;
                          const childrenAsString = option.children?.toString() || '';
                          return childrenAsString.toLowerCase().includes(input.toLowerCase());
                        }}
                        notFoundContent={loading ? '加载中...' : '暂无数据'}
                      >
                        {teachers.map((teacher) => (
                          <Select.Option key={String(teacher.id)} value={String(teacher.id)}>
                            {teacher.name}
                          </Select.Option>
                        ))}
                      </Select>
                    );
                  }}
                />
                {errors.userIds && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.userIds.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  variant={'grayBase'}
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false, settingId, [])}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit)}
                  isLoading={loading}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SettingModal;
