import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  getMyAppList,
  getSystemTenantGuideValidFinish,
  getAppointedAppList,
  getTenantSceneListOfficialHome
} from '@/api/app';

import type { AppListItemType, AppListParams } from '@/types/api/app';
import { ClientUseGuidanceType } from '@/constants/common';

type State = {
  myApps: AppListItemType[];
  myAppsOfficialHome: AppListItemType[];
  loadMyApps: (init?: boolean, data?: AppListParams) => Promise<AppListItemType[]>;

  loadMyAppsOfficialHome: (init?: boolean, data?: AppListParams) => Promise<AppListItemType[]>;

  specialAppList: AppListItemType[];
  loadSpecialAppList: () => Promise<AppListItemType[]>;

  generalAppId?: string;
  setGeneralAppId: (appId?: string) => void;

  viewApp?: AppListItemType;
  setViewApp: (app?: string | AppListItemType) => void;

  isFinish?: boolean;
  setIsFinish: (isFinish: boolean) => void;

  refreshIsFinish: () => Promise<void>;
};

export const useAppStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        myApps: [],
        myAppsOfficialHome: [],

        async loadMyAppsOfficialHome(init = true, data?: AppListParams) {
          if (get().myAppsOfficialHome.length > 0 && !init) return get().myAppsOfficialHome;
          const res = await getTenantSceneListOfficialHome(data!);
          set((state) => {
            state.myAppsOfficialHome = res;
            if (state.viewApp) {
              state.viewApp = res.find((it) => it.id == state.viewApp?.id);
            }
          });

          return res;
        },

        async loadMyApps(init = true, data?: AppListParams) {
          if (get().myApps.length > 0 && !init) return get().myApps;
          const res = await getMyAppList(data!);
          set((state) => {
            state.myApps = res;
            if (state.viewApp) {
              state.viewApp = res.find((it) => it.id == state.viewApp?.id);
            }
          });

          const guideRes = await getSystemTenantGuideValidFinish(
            ClientUseGuidanceType.DeepEditorGuide
          );

          set((state) => {
            state.isFinish = guideRes;
          });

          return res;
        },

        specialAppList: [],
        async loadSpecialAppList() {
          if (get().specialAppList.length > 0) return get().specialAppList;
          const res = await getAppointedAppList({});
          set((state) => {
            state.specialAppList = res;
          });
          return res;
        },

        generalAppId: undefined,
        setGeneralAppId(appId) {
          set((state) => {
            state.generalAppId = appId;
          });
        },

        viewApp: undefined,
        setViewApp(app) {
          const appId = app ? (typeof app === 'object' ? app.id : app) : undefined;
          if (appId && appId == get().generalAppId) {
            return;
          }
          set((state) => {
            state.viewApp = appId ? state.myApps.find((it) => it.id == appId) : undefined;
          });
        },

        isFinish: undefined,
        setIsFinish(isFinish) {
          set((state) => {
            state.isFinish = isFinish;
          });
        },

        async refreshIsFinish() {
          const guideRes = await getSystemTenantGuideValidFinish(
            ClientUseGuidanceType.DeepEditorGuide
          );
          set((state) => {
            state.isFinish = guideRes;
          });
        }
      })),
      {
        name: 'appStore',
        partialize: (state) => ({
          generalAppId: state.generalAppId,
          isFinish: state.isFinish
        })
      }
    )
  )
);
