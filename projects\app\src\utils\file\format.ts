export const ImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

export const VideoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'];

export const AudioExtensions = ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a', '.wma'];

export const isTheTypeFile = (file: string | File, extensions: string[], types: string[]) =>
  typeof file === 'string'
    ? extensions.includes(file.substring(file.lastIndexOf('.')).toLowerCase())
    : types.some((type) => file.type.startsWith(type));

export const isImageFile = (file: string | File) => isTheTypeFile(file, ImageExtensions, ['image']);

export const isVideoFile = (file: string | File) => isTheTypeFile(file, VideoExtensions, ['video']);

export const isAudioFile = (file: string | File) => isTheTypeFile(file, AudioExtensions, ['audio']);

/**
 * 格式化文件大小，自动选择合适的单位
 * @param sizeInBytes 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (sizeInBytes: number): string => {
  if (!sizeInBytes || sizeInBytes <= 0) {
    return '未知大小';
  }

  const sizeInKB = sizeInBytes / 1024;
  const sizeInMB = sizeInKB / 1024;

  // 如果大于等于1MB，显示MB单位
  if (sizeInMB >= 1) {
    return `${sizeInMB.toFixed(2)}MB`;
  }

  // 否则显示KB单位
  return `${sizeInKB.toFixed(2)}KB`;
};
